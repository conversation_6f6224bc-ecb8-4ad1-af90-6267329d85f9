# 使用@Lazy注解解决循环依赖

## 问题描述

在`EpsSecurityConfigure`类中存在循环依赖：
1. `EpsSecurityConfigure`需要注入`adminUserDetailService`和`phoneUserDetailsService`
2. 这些UserDetailsService实现类需要`PasswordEncoder`
3. `PasswordEncoder`在`EpsSecurityConfigure`中定义
4. 形成循环依赖链

## 解决方案：使用@Lazy注解

### 1. @Lazy注解的工作原理

`@Lazy`注解告诉Spring容器延迟初始化Bean，直到第一次被实际使用时才创建。这样可以打破循环依赖：

- Spring首先创建`EpsSecurityConfigure`实例
- 对于标记了`@Lazy`的依赖，Spring创建代理对象而不是实际的Bean
- 当实际使用这些依赖时，Spring才会创建真正的Bean实例

### 2. 修复后的代码

```java
@Order(2)
@EnableWebSecurity
@Configurable
public class EpsSecurityConfigure extends WebSecurityConfigurerAdapter {

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Qualifier("adminUserDetailService")
    @Autowired
    @Lazy  // ✅ 延迟加载，打破循环依赖
    private UserDetailsService adminUserDetailService;
    
    @Qualifier("phoneUserDetailsService")
    @Autowired
    @Lazy  // ✅ 延迟加载，打破循环依赖
    private UserDetailsService phoneUserDetailsService;

    @Autowired
    private ValidateCodeFilter validateCodeFilter;
    @Autowired
    private EpsWebLoginSuccessHandler successHandler;
    @Autowired
    private EpsWebLoginFailureHandler failureHandler;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ILoginLogService loginLogService;

    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        EpsUserDetailsAuthenticationProvider provider = new EpsUserDetailsAuthenticationProvider();
        LinkedList<UserDetailsService> userDetailsServices = new LinkedList();
        userDetailsServices.add(adminUserDetailService);  // 使用延迟加载的代理
        userDetailsServices.add(phoneUserDetailsService); // 使用延迟加载的代理
        provider.setUserDetailsServiceList(userDetailsServices);
        provider.setRedisService(redisService);
        provider.setLoginLogService(loginLogService);
        provider.setPasswordEncoder(passwordEncoder()); // ✅ 直接调用方法
        List<AuthenticationProvider> list = new ArrayList<>();
        list.add(provider);
        return new ProviderManager(list);
    }
}
```

### 3. 关键修改点

#### 添加@Lazy注解
```java
@Qualifier("adminUserDetailService")
@Autowired
@Lazy  // ✅ 新增@Lazy注解
private UserDetailsService adminUserDetailService;

@Qualifier("phoneUserDetailsService")
@Autowired
@Lazy  // ✅ 新增@Lazy注解
private UserDetailsService phoneUserDetailsService;
```

#### 移除PasswordEncoder字段注入
```java
// ❌ 删除了这行，避免循环依赖
// @Autowired
// private PasswordEncoder passwordEncoder;
```

#### 直接调用PasswordEncoder方法
```java
// ✅ 直接调用方法而不是使用字段
provider.setPasswordEncoder(passwordEncoder());
```

#### 添加必要的import
```java
import org.springframework.context.annotation.Lazy;
```

## 技术原理

### 1. 循环依赖的形成
```
EpsSecurityConfigure 创建
    ↓ 需要注入
adminUserDetailService 创建
    ↓ 需要注入
PasswordEncoder 创建
    ↓ 定义在
EpsSecurityConfigure (正在创建中)
    ↓ 循环依赖！
```

### 2. @Lazy如何打破循环依赖
```
EpsSecurityConfigure 创建
    ↓ 注入代理对象 (不创建实际Bean)
adminUserDetailService 代理
    ↓ 配置完成后
EpsSecurityConfigure 创建完成
    ↓ 第一次使用时
adminUserDetailService 实际创建
    ↓ 此时PasswordEncoder已可用
成功创建所有Bean
```

### 3. 代理对象的工作方式
- Spring创建一个代理对象注入到`EpsSecurityConfigure`中
- 代理对象实现了`UserDetailsService`接口
- 当第一次调用代理对象的方法时，Spring才创建实际的Bean
- 后续调用直接使用已创建的实际Bean

## 优势和注意事项

### ✅ 优势
1. **简单有效**：只需添加一个注解即可解决循环依赖
2. **性能友好**：延迟加载可以提高启动性能
3. **透明使用**：使用方式与普通Bean完全相同
4. **保持原有逻辑**：不需要重构现有代码结构

### ⚠️ 注意事项
1. **首次调用延迟**：第一次使用时可能有轻微的性能开销
2. **调试复杂性**：代理对象可能使调试变得复杂
3. **异常延迟**：Bean创建异常会延迟到首次使用时才抛出
4. **循环依赖检测**：Spring的循环依赖检测可能无法及时发现问题

## 验证步骤

### 1. 编译验证
```bash
mvn clean compile
```

### 2. 启动验证
```bash
mvn spring-boot:run
```
应该看到应用正常启动，无循环依赖错误。

### 3. 功能验证
- [ ] 用户登录功能正常
- [ ] OAuth2认证正常工作
- [ ] 密码加密功能正常
- [ ] 所有UserDetailsService正常工作

### 4. 日志验证
启动日志中应该看到：
```
INFO  - Bean 'adminUserDetailService' is being lazily initialized
INFO  - Bean 'phoneUserDetailsService' is being lazily initialized
```

## 替代方案

如果@Lazy方案不适合，还有其他解决方案：

### 方案1：分离配置类
```java
@Configuration
public class PasswordEncoderConfig {
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}

@Configuration
@RequiredArgsConstructor
public class SecurityConfig {
    private final PasswordEncoder passwordEncoder;
    // ...
}
```

### 方案2：使用ApplicationContext
```java
@Autowired
private ApplicationContext applicationContext;

@Bean
public AuthenticationManager authenticationManagerBean() {
    UserDetailsService adminService = applicationContext.getBean("adminUserDetailService", UserDetailsService.class);
    // ...
}
```

### 方案3：使用@PostConstruct
```java
@PostConstruct
public void init() {
    // 在Bean创建完成后初始化依赖关系
}
```

## 总结

使用`@Lazy`注解是解决这种循环依赖最简单有效的方案：

1. **最小改动**：只需添加两个`@Lazy`注解
2. **保持兼容**：不改变现有的业务逻辑
3. **性能友好**：延迟加载提高启动性能
4. **易于维护**：代码结构清晰，易于理解

这个解决方案应该能够完全解决循环依赖问题，让应用正常启动。
