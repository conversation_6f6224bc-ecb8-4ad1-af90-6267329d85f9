# PasswordEncoder循环依赖解决方案

## 问题描述

应用启动时出现以下错误：

```
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'oauthClientDetailsController'
...
nested exception is org.springframework.beans.factory.BeanCurrentlyInCreationException: 
Error creating bean with name 'passwordEncoder': Requested bean is currently in creation: Is there an unresolvable circular reference?
```

## 问题原因分析

### 1. 循环依赖的根本原因
在`EpsSecurityConfigure`类中存在循环依赖：

```java
@Configuration
public class EpsSecurityConfigure extends WebSecurityConfigurerAdapter {
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Autowired
    private PasswordEncoder passwordEncoder;  // ❌ 循环依赖
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() {
        // ...
        provider.setPasswordEncoder(passwordEncoder);  // 使用注入的Bean
        // ...
    }
}
```

### 2. 依赖链分析
1. `EpsSecurityConfigure`定义了`passwordEncoder` Bean
2. 同时它又通过`@Autowired`注入`passwordEncoder`
3. 在`authenticationManagerBean()`方法中使用注入的`passwordEncoder`
4. Spring在创建`passwordEncoder` Bean时发现需要先创建`EpsSecurityConfigure`
5. 但`EpsSecurityConfigure`又需要`passwordEncoder`，形成循环依赖

### 3. 影响范围
- `OauthClientDetailsController`无法创建
- `OauthClientDetailsServiceImpl`无法创建
- 整个OAuth2认证体系无法启动
- 应用启动失败

## 解决方案

### 1. 移除循环依赖
**修改前（有问题）：**
```java
@Configuration
public class EpsSecurityConfigure extends WebSecurityConfigurerAdapter {
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Autowired
    private PasswordEncoder passwordEncoder;  // ❌ 循环依赖
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() {
        provider.setPasswordEncoder(passwordEncoder);  // 使用注入的Bean
    }
}
```

**修改后（正确）：**
```java
@Configuration
public class EpsSecurityConfigure extends WebSecurityConfigurerAdapter {
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    // 移除了 @Autowired private PasswordEncoder passwordEncoder;
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() {
        provider.setPasswordEncoder(passwordEncoder());  // 直接调用方法
    }
}
```

### 2. 关键修改点

#### 移除字段注入
```java
// 删除这行
// @Autowired
// private PasswordEncoder passwordEncoder;
```

#### 直接调用Bean方法
```java
// 修改前
provider.setPasswordEncoder(passwordEncoder);

// 修改后
provider.setPasswordEncoder(passwordEncoder());
```

## 修改的文件

### 1. 主要修改
- `src/main/java/com/example/eps/ins/auth/configure/EpsSecurityConfigure.java`

### 2. 修改内容
- 移除了`@Autowired private PasswordEncoder passwordEncoder;`字段注入
- 在`authenticationManagerBean()`方法中直接调用`passwordEncoder()`方法

## 技术原理

### 1. Spring Bean创建过程
1. **实例化**：创建Bean实例
2. **属性注入**：注入依赖的其他Bean
3. **初始化**：调用初始化方法

### 2. 循环依赖检测
Spring在Bean创建过程中会检测循环依赖：
- 如果Bean A依赖Bean B，Bean B又依赖Bean A，就会形成循环依赖
- Spring会抛出`BeanCurrentlyInCreationException`异常

### 3. 解决方案原理
- **直接方法调用**：在同一个配置类中，直接调用`@Bean`方法不会触发Spring的依赖注入机制
- **避免字段注入**：移除`@Autowired`字段注入，避免Spring管理这个依赖关系

## 验证方法

### 1. 编译验证
```bash
mvn clean compile
```

### 2. 启动验证
```bash
mvn spring-boot:run
```

### 3. 功能验证
- 检查OAuth2认证是否正常工作
- 验证用户登录功能
- 确认密码加密功能正常

## 最佳实践

### 1. 避免循环依赖的方法

#### 方法1：直接调用Bean方法（推荐）
```java
@Configuration
public class SecurityConfig {
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Bean
    public AuthenticationManager authenticationManager() {
        // 直接调用方法，不使用字段注入
        return new CustomAuthenticationManager(passwordEncoder());
    }
}
```

#### 方法2：使用构造器注入
```java
@Configuration
@RequiredArgsConstructor
public class SecurityConfig {
    private final PasswordEncoder passwordEncoder;
    
    @Bean
    public AuthenticationManager authenticationManager() {
        return new CustomAuthenticationManager(passwordEncoder);
    }
}
```

#### 方法3：分离配置类
```java
@Configuration
public class PasswordEncoderConfig {
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}

@Configuration
@RequiredArgsConstructor
public class SecurityConfig {
    private final PasswordEncoder passwordEncoder;
    
    @Bean
    public AuthenticationManager authenticationManager() {
        return new CustomAuthenticationManager(passwordEncoder);
    }
}
```

### 2. 配置类设计原则
- **单一职责**：每个配置类专注于特定功能
- **避免自依赖**：配置类不要注入自己定义的Bean
- **明确依赖关系**：使用构造器注入明确依赖关系

## 相关配置说明

### 1. 安全配置类型
应用配置中使用：
```yaml
eps:
  security:
    type: mvc  # 使用传统的 MVC 安全配置
```

### 2. 其他安全配置
- `SecurityConfigure`：WebFlux安全配置（当前未启用）
- `EpsAuthorizationServerConfigure`：OAuth2授权服务器配置
- `ResourceServerConfig`：资源服务器配置

## 常见问题

### 1. 为什么不能使用字段注入？
- 字段注入会让Spring管理依赖关系
- 在同一个配置类中会形成循环依赖
- 直接方法调用避免了Spring的依赖管理

### 2. 直接调用方法是否会创建多个实例？
- 不会，Spring会确保`@Bean`方法只被调用一次
- 后续调用会返回缓存的实例
- 这是Spring的代理机制保证的

### 3. 如何避免类似问题？
- 使用IDE的循环依赖检测工具
- 遵循配置类设计最佳实践
- 定期进行代码审查

## 总结

这个问题的核心是Spring Bean的循环依赖。通过移除字段注入并直接调用Bean方法，我们成功解决了循环依赖问题。

**关键要点**：
- 避免在配置类中注入自己定义的Bean
- 使用直接方法调用替代字段注入
- 遵循配置类设计最佳实践
- 定期检查和重构配置类结构
