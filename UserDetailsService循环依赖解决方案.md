# UserDetailsService循环依赖解决方案

## 问题描述

应用启动时出现以下循环依赖错误：

```
The dependencies of some of the beans in the application context form a cycle:

┌─────┐
|  adminUserDetailService defined in URL [jar:file:/data/eps-ins/eps-ins-0.0.1-SNAPSHOT.jar!/BOOT-INF/classes!/com/example/eps/ins/service/impl/EpsUserDetailsUserDetailServiceImpl.class]
↑     ↓
|  epsSecurityConfigure (field private org.springframework.security.core.userdetails.UserDetailsService com.example.eps.ins.auth.configure.EpsSecurityConfigure.adminUserDetailService)
└─────┘
```

## 问题原因分析

### 1. 循环依赖链
1. `EpsSecurityConfigure`需要注入`adminUserDetailService`
2. `adminUserDetailService`（`EpsUserDetailsUserDetailServiceImpl`）需要`PasswordEncoder`
3. `PasswordEncoder`在`EpsSecurityConfigure`中定义
4. 形成循环依赖：`EpsSecurityConfigure` → `adminUserDetailService` → `PasswordEncoder` → `EpsSecurityConfigure`

### 2. 具体依赖关系

#### EpsSecurityConfigure类
```java
@Configuration
public class EpsSecurityConfigure extends WebSecurityConfigurerAdapter {
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Qualifier("adminUserDetailService")
    @Autowired
    private UserDetailsService adminUserDetailService;  // ❌ 循环依赖
    
    @Bean
    public AuthenticationManager authenticationManagerBean() {
        // 使用 adminUserDetailService
    }
}
```

#### EpsUserDetailsUserDetailServiceImpl类
```java
@Service("adminUserDetailService")
@RequiredArgsConstructor
public class EpsUserDetailsUserDetailServiceImpl implements UserDetailsService {
    
    private final PasswordEncoder passwordEncoder;  // ❌ 需要PasswordEncoder
    // ...
}
```

### 3. 依赖循环图
```
EpsSecurityConfigure
       ↓ (需要)
adminUserDetailService (EpsUserDetailsUserDetailServiceImpl)
       ↓ (需要)
PasswordEncoder
       ↓ (定义在)
EpsSecurityConfigure
```

## 解决方案

### 1. 使用方法参数注入替代字段注入

**修改前（有问题）：**
```java
@Configuration
public class EpsSecurityConfigure extends WebSecurityConfigurerAdapter {
    
    @Qualifier("adminUserDetailService")
    @Autowired
    private UserDetailsService adminUserDetailService;  // ❌ 字段注入
    
    @Qualifier("phoneUserDetailsService")
    @Autowired
    private UserDetailsService phoneUserDetailsService;  // ❌ 字段注入
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() {
        // 使用字段注入的服务
        userDetailsServices.add(adminUserDetailService);
        userDetailsServices.add(phoneUserDetailsService);
    }
}
```

**修改后（正确）：**
```java
@Configuration
public class EpsSecurityConfigure extends WebSecurityConfigurerAdapter {
    
    // 移除了字段注入
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean(
            @Qualifier("adminUserDetailService") UserDetailsService adminUserDetailService,
            @Qualifier("phoneUserDetailsService") UserDetailsService phoneUserDetailsService) {
        // 使用方法参数注入的服务
        userDetailsServices.add(adminUserDetailService);
        userDetailsServices.add(phoneUserDetailsService);
    }
}
```

### 2. 关键修改点

#### 移除字段注入
```java
// 删除这些行
// @Qualifier("adminUserDetailService")
// @Autowired
// private UserDetailsService adminUserDetailService;
// @Qualifier("phoneUserDetailsService")
// @Autowired
// private UserDetailsService phoneUserDetailsService;
```

#### 使用方法参数注入
```java
@Bean
@Override
public AuthenticationManager authenticationManagerBean(
        @Qualifier("adminUserDetailService") UserDetailsService adminUserDetailService,
        @Qualifier("phoneUserDetailsService") UserDetailsService phoneUserDetailsService) {
    // 方法体保持不变，使用参数而不是字段
}
```

## 修改的文件

### 1. 主要修改
- `src/main/java/com/example/eps/ins/auth/configure/EpsSecurityConfigure.java`

### 2. 修改内容
- 移除了两个UserDetailsService的字段注入
- 将依赖改为方法参数注入
- 保持其他逻辑不变

## 技术原理

### 1. 字段注入 vs 方法参数注入

#### 字段注入（会导致循环依赖）
```java
@Autowired
private UserDetailsService adminUserDetailService;

@Bean
public AuthenticationManager authenticationManager() {
    // Spring需要先创建EpsSecurityConfigure实例
    // 然后注入adminUserDetailService字段
    // 但adminUserDetailService又依赖PasswordEncoder
    // PasswordEncoder在同一个类中定义，形成循环
}
```

#### 方法参数注入（避免循环依赖）
```java
@Bean
public AuthenticationManager authenticationManager(
        UserDetailsService adminUserDetailService) {
    // Spring在调用方法时才解析参数依赖
    // 此时EpsSecurityConfigure已经创建完成
    // PasswordEncoder Bean也已经可用
    // 避免了循环依赖
}
```

### 2. Spring Bean创建时序

#### 修改前的问题时序
1. Spring开始创建`EpsSecurityConfigure`
2. 发现需要注入`adminUserDetailService`字段
3. 开始创建`adminUserDetailService`
4. 发现需要`PasswordEncoder`
5. 发现`PasswordEncoder`在`EpsSecurityConfigure`中定义
6. 但`EpsSecurityConfigure`还在创建中 → 循环依赖

#### 修改后的正确时序
1. Spring创建`EpsSecurityConfigure`实例
2. 调用`passwordEncoder()`方法创建`PasswordEncoder` Bean
3. 创建`adminUserDetailService`（此时`PasswordEncoder`已可用）
4. 调用`authenticationManagerBean()`方法时传入已创建的服务
5. 成功创建所有Bean

## 验证方法

### 1. 编译验证
```bash
mvn clean compile
```

### 2. 启动验证
```bash
mvn spring-boot:run
```

### 3. 功能验证
- 检查用户登录功能是否正常
- 验证OAuth2认证是否工作
- 确认密码加密功能正常

## 最佳实践

### 1. 避免循环依赖的方法

#### 方法1：使用方法参数注入（推荐）
```java
@Bean
public SomeService someService(Dependency dependency) {
    return new SomeService(dependency);
}
```

#### 方法2：使用@Lazy注解
```java
@Autowired
@Lazy
private UserDetailsService adminUserDetailService;
```

#### 方法3：分离配置类
```java
@Configuration
public class PasswordEncoderConfig {
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}

@Configuration
public class SecurityConfig {
    @Bean
    public AuthenticationManager authenticationManager(
            UserDetailsService adminUserDetailService) {
        // ...
    }
}
```

### 2. 配置类设计原则
- **避免自引用**：配置类不要注入自己定义的Bean
- **使用方法参数**：优先使用方法参数注入而不是字段注入
- **分离关注点**：将相关但独立的Bean分离到不同配置类
- **明确依赖关系**：使用构造器注入明确依赖关系

### 3. 依赖注入最佳实践
- **构造器注入** > **方法参数注入** > **字段注入**
- 避免在配置类中使用字段注入
- 使用`@Qualifier`明确指定Bean
- 合理使用`@Lazy`延迟加载

## 常见问题

### 1. 为什么方法参数注入不会循环依赖？
- 方法参数注入是在Bean创建完成后调用方法时才解析依赖
- 字段注入是在Bean创建过程中就需要解析依赖
- 时序不同避免了循环依赖

### 2. 是否会影响Bean的单例性？
- 不会，Spring保证`@Bean`方法只被调用一次
- 后续调用会返回缓存的实例
- 方法参数注入不影响Bean的生命周期

### 3. 如何检测循环依赖？
- Spring启动时会自动检测并报告循环依赖
- 使用IDE的依赖分析工具
- 定期进行代码审查

## 总结

这个问题的核心是Spring Bean的循环依赖。通过将字段注入改为方法参数注入，我们成功解决了循环依赖问题。

**关键要点**：
- 避免在配置类中使用字段注入自己定义的Bean
- 使用方法参数注入替代字段注入
- 理解Spring Bean创建时序
- 遵循依赖注入最佳实践
