# Bean 冲突问题修正说明

## 问题描述

启动时出现以下错误：
```
BeanDefinitionStoreException: Failed to parse configuration class
ConflictingBeanDefinitionException: Annotation-specified bean name 'globalExceptionHandler' 
for bean class [com.example.eps.ins.common.handler.GlobalExceptionHandler] 
conflicts with existing, non-compatible bean definition of same name and class 
[com.example.eps.ins.common.exception.GlobalExceptionHandler]
```

## 问题原因

项目中存在两个同名的 `GlobalExceptionHandler` 类：

1. **原有的异常处理器**：
   - 路径：`com.example.eps.ins.common.handler.GlobalExceptionHandler`
   - 继承自：`BaseExceptionHandler`
   - 用途：项目原有的全局异常处理

2. **新创建的异常处理器**：
   - 路径：`com.example.eps.ins.common.exception.GlobalExceptionHandler`
   - 用途：处理 Socket 超时异常

Spring 在扫描组件时发现两个同名的 Bean，导致冲突。

## 解决方案

### 1. 删除重复的异常处理器
删除新创建的 `com.example.eps.ins.common.exception.GlobalExceptionHandler` 类。

### 2. 扩展现有的异常处理器
将超时异常处理逻辑添加到现有的 `BaseExceptionHandler` 中。

## 修正后的代码结构

### BaseExceptionHandler 扩展

```java
@Slf4j
public class BaseExceptionHandler {
    
    // 原有的异常处理方法...
    
    /**
     * 处理客户端连接中断异常
     */
    @ExceptionHandler(value = ClientAbortException.class)
    @ResponseStatus(HttpStatus.REQUEST_TIMEOUT)
    public EpsResponse handleClientAbortException(ClientAbortException e, HttpServletRequest request) {
        String requestUri = request != null ? request.getRequestURI() : "unknown";
        String method = request != null ? request.getMethod() : "unknown";
        
        log.warn("客户端连接中断: {} {}, 原因: {}", method, requestUri, e.getMessage());
        return new EpsResponse().message("客户端连接中断");
    }

    /**
     * 处理Socket超时异常
     */
    @ExceptionHandler(value = SocketTimeoutException.class)
    @ResponseStatus(HttpStatus.REQUEST_TIMEOUT)
    public EpsResponse handleSocketTimeoutException(SocketTimeoutException e, HttpServletRequest request) {
        String requestUri = request != null ? request.getRequestURI() : "unknown";
        String method = request != null ? request.getMethod() : "unknown";
        
        log.error("Socket连接超时: {} {}, 原因: {}", method, requestUri, e.getMessage());
        return new EpsResponse().message("请求超时，请稍后重试");
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(value = MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.PAYLOAD_TOO_LARGE)
    public EpsResponse handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
        String requestUri = request != null ? request.getRequestURI() : "unknown";
        String method = request != null ? request.getMethod() : "unknown";
        
        log.error("文件上传大小超限: {} {}, 最大允许: {}", method, requestUri, e.getMaxUploadSize());
        
        long maxSizeMB = e.getMaxUploadSize() / 1024 / 1024;
        return new EpsResponse().message("上传文件过大，最大允许: " + maxSizeMB + "MB");
    }

    /**
     * 处理I/O异常
     */
    @ExceptionHandler(value = IOException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public EpsResponse handleIOException(IOException e, HttpServletRequest request) {
        String requestUri = request != null ? request.getRequestURI() : "unknown";
        String method = request != null ? request.getMethod() : "unknown";
        
        // 如果是ClientAbortException的根本原因，记录为警告
        if (e.getCause() instanceof ClientAbortException || 
            e.getMessage().contains("ClientAbortException")) {
            log.warn("I/O异常(客户端中断): {} {}, 原因: {}", method, requestUri, e.getMessage());
            return new EpsResponse().message("客户端连接中断");
        }
        
        log.error("I/O异常: {} {}, 原因: {}", method, requestUri, e.getMessage(), e);
        return new EpsResponse().message("I/O操作异常");
    }
}
```

### GlobalExceptionHandler 继承关系

```java
@RestControllerAdvice
@Order(value = Ordered.HIGHEST_PRECEDENCE)
public class GlobalExceptionHandler extends BaseExceptionHandler {
    // 继承 BaseExceptionHandler 的所有异常处理方法
}
```

## 修正的内容

### 1. 添加的导入包
```java
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.SocketTimeoutException;
```

### 2. 新增的异常处理方法
- `handleClientAbortException()` - 处理客户端连接中断
- `handleSocketTimeoutException()` - 处理 Socket 超时
- `handleMaxUploadSizeExceededException()` - 处理文件上传大小超限
- `handleIOException()` - 处理 I/O 异常

### 3. 响应格式统一
所有新增的异常处理方法都返回 `EpsResponse` 对象，保持与项目现有异常处理的一致性。

## 优势

### 1. 避免 Bean 冲突
- 不创建新的全局异常处理器
- 扩展现有的异常处理基类

### 2. 保持一致性
- 使用项目统一的 `EpsResponse` 响应格式
- 遵循现有的异常处理模式

### 3. 继承机制
- 所有继承 `BaseExceptionHandler` 的类都自动获得新的异常处理能力
- 便于维护和扩展

## 测试验证

### 1. 启动测试
确保应用能够正常启动，没有 Bean 冲突错误。

### 2. 异常处理测试
- 测试客户端连接中断场景
- 测试 Socket 超时场景
- 测试文件上传大小超限场景

### 3. 响应格式验证
确保异常响应格式与项目其他接口保持一致。

## 注意事项

### 1. HttpServletRequest 空值处理
在异常处理方法中添加了 `request` 参数的空值检查，避免空指针异常。

### 2. 日志级别区分
- 客户端中断：使用 `WARN` 级别
- 服务器异常：使用 `ERROR` 级别

### 3. 异常分类处理
对 I/O 异常进行了细分，区分客户端中断和服务器异常。

现在应用应该能够正常启动，并且具备完整的超时异常处理能力！
