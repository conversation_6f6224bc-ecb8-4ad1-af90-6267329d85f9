# 设备管理API使用说明

## 概述
设备管理Controller已经升级，现在`getDeviceList`方法支持自动将获取到的设备信息同步到数据库中。

## API接口说明

### 1. 查询设备列表并自动入库
**接口地址**: `GET /device/list`

**参数说明**:
- `pageIndex` (可选): 页码，从1开始
- `pageSize` (可选): 每页大小
- `autoSync` (可选): 是否自动同步到数据库，默认为true

**使用示例**:
```bash
# 查询第1页，每页10条，自动同步到数据库
GET /device/list?pageIndex=1&pageSize=10&autoSync=true

# 查询设备列表但不同步到数据库
GET /device/list?pageIndex=1&pageSize=10&autoSync=false

# 使用默认参数（自动同步）
GET /device/list
```

**响应示例**:
```json
{
  "success": true,
  "statusCode": 200,
  "apiStatus": "success",
  "syncStatus": "success",
  "syncCount": 10,
  "pageInfo": {
    "pageIndex": 1,
    "pageSize": 10,
    "total": 100
  },
  "data": [...],
  "message": "设备列表查询成功，已自动同步到数据库"
}
```

### 2. 批量同步所有设备信息
**接口地址**: `POST /device/sync-all`

**参数说明**:
- `maxPages` (可选): 最大同步页数，默认10页
- `pageSize` (可选): 每页大小，默认100

**使用示例**:
```bash
# 同步前20页，每页50条
POST /device/sync-all?maxPages=20&pageSize=50

# 使用默认参数
POST /device/sync-all
```

### 3. 查询本地数据库设备列表
**接口地址**: `GET /device/local`

**参数说明**:
- `pageNum`: 页码
- `pageSize`: 每页大小
- 其他Device实体的查询条件

### 4. 获取设备同步状态统计
**接口地址**: `GET /device/sync-status`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalDevices": 1000,
    "syncedCount": 950,
    "unsyncedCount": 30,
    "errorCount": 20,
    "syncRate": "95.00%"
  },
  "message": "查询设备同步状态统计成功"
}
```

### 5. 重新同步指定状态的设备
**接口地址**: `POST /device/resync`

**参数说明**:
- `syncStatus`: 要重新同步的设备状态
  - 0: 未同步
  - -1: 同步失败

## 同步状态说明

- `syncStatus = 1`: 已成功同步
- `syncStatus = 0`: 未同步
- `syncStatus = -1`: 同步失败

## 使用建议

1. **首次使用**: 建议先调用`POST /device/sync-all`进行全量同步
2. **日常使用**: 使用`GET /device/list`进行增量同步
3. **状态监控**: 定期调用`GET /device/sync-status`查看同步状态
4. **错误处理**: 对于同步失败的设备，可使用`POST /device/resync`重新同步

## 注意事项

1. 批量同步时会在每页之间添加100ms延迟，避免API调用过于频繁
2. 自动同步功能默认开启，如不需要可设置`autoSync=false`
3. 同步过程中的异常会被记录到日志中，不会影响设备列表的正常返回
4. 建议在低峰期进行大批量同步操作

## 错误处理

所有接口都包含完善的错误处理机制：
- API调用失败时会返回相应的错误信息
- 同步过程中的异常不会影响设备数据的正常返回
- 详细的错误信息会记录在日志中，便于问题排查
