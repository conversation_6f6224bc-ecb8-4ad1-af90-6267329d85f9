# Socket 超时异常解决方案

## 问题描述

在处理安全监控事件时，出现以下错误：
```
ClientAbortException: java.net.SocketTimeoutException
I/O error while reading input message
```

## 错误原因分析

### 1. 主要原因
- **Base64 图片上传耗时过长**：将 base64 数据上传到 FastDFS 需要时间
- **客户端超时设置过短**：客户端在服务器处理完成前断开连接
- **网络不稳定**：网络波动导致连接中断
- **大文件传输**：base64 图片数据过大

### 2. 触发场景
- 处理包含大尺寸 base64 图片的安全监控事件
- 网络环境不稳定时的文件上传
- 高并发情况下的资源竞争

## 解决方案

### 1. 服务器端配置优化

#### Tomcat 超时配置
```yaml
server:
  tomcat:
    connection-timeout: 60000      # 连接超时 60秒
    max-connections: 8192          # 最大连接数
    max-threads: 200              # 最大线程数
    min-spare-threads: 10         # 最小空闲线程数
    accept-count: 100             # 接受队列长度
    max-http-post-size: 104857600 # 最大POST大小 100MB
  servlet:
    session:
      timeout: 30m                # 会话超时 30分钟
```

#### Spring 配置优化
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 100MB        # 最大文件大小
      max-request-size: 100MB     # 最大请求大小
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
```

### 2. 全局异常处理

创建 `GlobalExceptionHandler` 处理各种超时异常：

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(ClientAbortException.class)
    public ResponseEntity<Map<String, Object>> handleClientAbortException(
            ClientAbortException e, HttpServletRequest request) {
        // 客户端连接中断处理
        log.warn("客户端连接中断: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                .body(buildErrorResponse(499, "客户端连接中断"));
    }
    
    @ExceptionHandler(SocketTimeoutException.class)
    public ResponseEntity<Map<String, Object>> handleSocketTimeoutException(
            SocketTimeoutException e, HttpServletRequest request) {
        // Socket超时处理
        log.error("Socket连接超时: {}", e.getMessage());
        return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                .body(buildErrorResponse(408, "请求超时，请稍后重试"));
    }
}
```

### 3. 文件上传优化

#### 添加大小检查和超时监控
```java
public String uploadBase64Image(String base64Data, String prefix) {
    try {
        byte[] imageBytes = decodeBase64Image(base64Data);
        
        // 检查图片大小限制
        long imageSizeKB = imageBytes.length / 1024;
        if (imageSizeKB > 10240) { // 10MB 限制
            log.warn("图片过大 {}KB，可能导致上传超时", imageSizeKB);
        }
        
        // 监控上传耗时
        long startTime = System.currentTimeMillis();
        Map<String, Object> uploadResult = fdfsClientService.uploadFile(
                imageBytes, imageBytes.length, fileName, extension);
        long uploadTime = System.currentTimeMillis() - startTime;
        
        log.info("FastDFS 上传耗时: {}ms", uploadTime);
        
        return processUploadResult(uploadResult);
        
    } catch (Exception e) {
        if (e.getCause() instanceof SocketTimeoutException) {
            log.error("FastDFS 上传超时: {}", prefix);
        }
        return base64Data; // 失败时返回原始数据
    }
}
```

### 4. 异步处理方案

#### 异步上传配置
```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    @Bean("fileUploadExecutor")
    public Executor fileUploadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("FileUpload-");
        executor.setKeepAliveSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
```

#### 异步上传方法
```java
@Async("fileUploadExecutor")
public CompletableFuture<String> uploadBase64ImageAsync(String base64Data, String prefix) {
    try {
        String result = uploadBase64Image(base64Data, prefix);
        return CompletableFuture.completedFuture(result);
    } catch (Exception e) {
        CompletableFuture<String> future = new CompletableFuture<>();
        future.completeExceptionally(e);
        return future;
    }
}
```

### 5. 客户端优化建议

#### 增加客户端超时设置
```javascript
// JavaScript 示例
fetch('/risk/push/message', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
    timeout: 120000  // 2分钟超时
})
```

#### Java 客户端示例
```java
// RestTemplate 配置
@Bean
public RestTemplate restTemplate() {
    HttpComponentsClientHttpRequestFactory factory = 
        new HttpComponentsClientHttpRequestFactory();
    factory.setConnectTimeout(60000);    // 连接超时 60秒
    factory.setReadTimeout(120000);      // 读取超时 120秒
    return new RestTemplate(factory);
}
```

## 监控和预防

### 1. 添加监控指标
```java
// 上传耗时监控
@Component
public class UploadMetrics {
    private final MeterRegistry meterRegistry;
    
    public void recordUploadTime(long duration) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("file.upload.duration")
                .description("File upload duration")
                .register(meterRegistry));
    }
}
```

### 2. 日志监控
```yaml
logging:
  level:
    com.example.eps.ins.common.utils.Base64ImageUploadUtil: INFO
    org.apache.catalina.connector.ClientAbortException: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### 3. 健康检查
```java
@Component
public class FastDFSHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        try {
            // 测试 FastDFS 连接
            // 返回健康状态
            return Health.up()
                    .withDetail("fastdfs", "Available")
                    .build();
        } catch (Exception e) {
            return Health.down()
                    .withDetail("fastdfs", "Unavailable")
                    .withException(e)
                    .build();
        }
    }
}
```

## 最佳实践

### 1. 分层处理
- **接口层**：快速响应，避免长时间阻塞
- **服务层**：异步处理耗时操作
- **存储层**：优化上传策略

### 2. 降级策略
- 上传失败时保留原始 base64 数据
- 提供重试机制
- 实现熔断器模式

### 3. 性能优化
- 图片压缩处理
- 分片上传大文件
- 缓存机制避免重复上传

### 4. 错误处理
- 详细的错误日志记录
- 用户友好的错误提示
- 自动重试机制

通过以上解决方案，可以有效解决 Socket 超时异常问题，提高系统的稳定性和用户体验。
