# JSON解析流程说明

## 原始JSON数据结构

根据您提供的数据，JSON结构如下：

```json
{
  "content": "{\"type\":\"warn\",\"params\":\"{\\\"CityEventListObject\\\": {\\\"CityEventObject\\\": [...]}}\"}",
  "deviceId": 1940708612932972546,
  "deviceType": "GATEWAY",
  "ip": "************",
  "serial": "5CF838712B91",
  "status": "ONLINE"
}
```

## 关键问题

**`content` 字段是一个JSON字符串，不是JSON对象！**

这意味着需要进行**两次JSON解析**：

1. 第一次解析：解析整个payload，得到根对象
2. 第二次解析：解析 `content` 字段中的JSON字符串

## 解析步骤

### 步骤1：解析根JSON
```java
JsonNode rootNode = MAPPER.readTree(jsonPayload);
```

得到：
- `deviceId`: 1940708612932972546
- `deviceType`: "GATEWAY"
- `ip`: "************"
- `serial`: "5CF838712B91"
- `status`: "ONLINE"
- `content`: 一个JSON字符串

### 步骤2：解析content字符串
```java
JsonNode contentNode = rootNode.path("content");
String contentStr = contentNode.asText(); // 获取字符串内容
JsonNode contentJsonNode = MAPPER.readTree(contentStr); // 再次解析
```

解析后的content结构：
```json
{
  "type": "warn",
  "params": "{\"CityEventListObject\": {\"CityEventObject\": [...]}}"
}
```

### 步骤3：解析params字符串
```java
JsonNode paramsNode = contentJsonNode.path("params");
String paramsStr = paramsNode.asText(); // 又是一个JSON字符串！
JsonNode parsedParamsNode = MAPPER.readTree(paramsStr); // 第三次解析
```

最终得到事件数据：
```json
{
  "CityEventListObject": {
    "CityEventObject": [
      {
        "Id": "12117524842470481b24041cec0848b0ab57de105cce3c5d",
        "EventType": 121,
        "HaveAlarm": 1,
        "CreateTime": "20250714171047",
        // ... 其他字段
      }
    ]
  }
}
```

## 修正后的代码逻辑

```java
@PostMapping(value = "/message", consumes = "application/json")
public Map<String, Object> handleSafetyHelmetEvent(
        @RequestBody String jsonPayload,
        @RequestHeader(value = "X-Request-Time", required = false) String requestTime) {
    
    try {
        // 1. 第一次解析：解析根JSON
        JsonNode rootNode = MAPPER.readTree(jsonPayload);
        
        // 2. 提取设备信息
        Map<String, Object> deviceInfo = extractDeviceInfo(rootNode);
        
        // 3. 第二次解析：解析content字符串
        JsonNode contentNode = rootNode.path("content");
        if (!contentNode.isMissingNode()) {
            String contentStr = contentNode.asText(); // 获取JSON字符串
            if (contentStr != null && !contentStr.isEmpty()) {
                JsonNode contentJsonNode = MAPPER.readTree(contentStr); // 解析字符串
                String contentType = contentJsonNode.path("type").asText();
                
                if ("warn".equals(contentType)) {
                    // 4. 第三次解析：解析params字符串
                    processWarningEvent(contentJsonNode, deviceInfo, jsonPayload);
                }
            }
        }
        
        // 返回成功响应
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("msg", "success");
        return response;
        
    } catch (Exception e) {
        log.error("处理安全监控事件异常", e);
        Map<String, Object> response = new HashMap<>();
        response.put("code", 500);
        response.put("msg", "处理失败: " + e.getMessage());
        return response;
    }
}

private void processWarningEvent(JsonNode contentJsonNode, Map<String, Object> deviceInfo, String rawJsonData) {
    try {
        JsonNode paramsNode = contentJsonNode.path("params");
        
        if (!paramsNode.isMissingNode() && paramsNode.isTextual()) {
            // params是JSON字符串，需要解析
            String paramsStr = paramsNode.asText();
            if (paramsStr != null && !paramsStr.isEmpty()) {
                JsonNode parsedParamsNode = MAPPER.readTree(paramsStr); // 第三次解析
                
                JsonNode cityEventListNode = parsedParamsNode.path("CityEventListObject");
                if (!cityEventListNode.isMissingNode()) {
                    JsonNode cityEventArrayNode = cityEventListNode.path("CityEventObject");
                    
                    if (cityEventArrayNode.isArray()) {
                        for (JsonNode eventNode : cityEventArrayNode) {
                            // 处理每个事件
                            processIndividualEvent(eventNode, deviceInfo, rawJsonData);
                        }
                    }
                }
            }
        }
    } catch (Exception e) {
        log.error("处理告警事件异常", e);
    }
}
```

## 测试方法

我添加了一个测试接口 `/risk/push/test-parse`，您可以用它来测试JSON解析是否正确：

```bash
POST /risk/push/test-parse
Content-Type: application/json

{您的JSON数据}
```

这个接口会详细输出每一步的解析结果，帮助您验证解析逻辑。

## 关键点总结

1. **多层JSON字符串**：数据中包含多层嵌套的JSON字符串
2. **需要多次解析**：至少需要3次JSON解析才能获取到事件数据
3. **字符串转义**：注意JSON字符串中的转义字符处理
4. **类型检查**：使用 `isTextual()` 检查节点是否为字符串类型
5. **异常处理**：每次解析都要做好异常处理

现在的代码应该能够正确解析您提供的JSON数据结构了。
