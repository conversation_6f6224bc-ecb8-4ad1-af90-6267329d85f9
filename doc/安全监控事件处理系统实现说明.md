# 安全监控事件处理系统实现说明

## 概述

本系统完善了 `handleSafetyHelmetEvent` 方法的后续逻辑，实现了完整的安全监控事件处理流程，包括数据持久化、事件分类处理、告警通知等功能。

## 系统架构

### 1. 数据层 (Entity & Mapper)
- **SafetyMonitorEvent.java** - 安全监控事件实体类
- **SafetyMonitorEventMapper.java** - 数据访问接口
- **SafetyMonitorEventMapper.xml** - MyBatis映射文件

### 2. 服务层 (Service)
- **SafetyMonitorEventService.java** - 事件服务接口
- **SafetyMonitorEventServiceImpl.java** - 事件服务实现
- **AlarmNotificationService.java** - 告警通知服务接口
- **AlarmNotificationServiceImpl.java** - 告警通知服务实现

### 3. 控制层 (Controller)
- **RiskPushController.java** - 风险推送控制器（已完善）
- **SafetyMonitorEventController.java** - 事件查询控制器

## 主要功能

### 1. 事件接收与解析
- 接收来自第三方系统的JSON格式安全监控事件
- 解析复杂的嵌套JSON结构
- 提取设备信息、事件详情、检测结果等

### 2. 数据持久化
- 将解析后的事件数据存储到数据库
- 支持事件去重（基于外部事件ID）
- 记录原始JSON数据用于问题排查

### 3. 事件分类处理
- **安全帽检测事件 (121)** - 检测人员是否佩戴安全帽
- **反光衣检测事件 (122)** - 检测人员是否穿着反光衣
- **人员入侵检测事件 (123)** - 检测是否有人员入侵禁区
- **通用安全事件** - 处理其他类型的安全事件

### 4. 告警通知
- 根据事件类型发送不同的告警通知
- 支持多种通知方式：系统消息、邮件、短信
- 记录告警状态和告警时间

### 5. 事件查询与统计
- 分页查询安全监控事件
- 按事件类型、设备、时间范围等条件筛选
- 统计分析功能：按事件类型统计、按设备统计告警数量
- 查询未处理事件和最近告警事件

## 数据库设计

### 主表：t_safety_monitor_event

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| external_event_id | varchar(100) | 外部事件ID（唯一） |
| device_id | bigint | 设备ID |
| device_serial | varchar(100) | 设备序列号 |
| device_ip | varchar(50) | 设备IP地址 |
| device_type | varchar(50) | 设备类型 |
| camera_id | varchar(50) | 摄像头ID |
| camera_name | varchar(100) | 摄像头名称 |
| event_type | int | 事件类型 |
| event_type_name | varchar(50) | 事件类型名称 |
| confidence | double | 置信度 |
| detection_score | double | 检测分数 |
| have_alarm | tinyint | 是否有告警 |
| alarm_times | int | 告警次数 |
| object_name | varchar(100) | 检测对象名称 |
| bounding_box | text | 边界框坐标（JSON） |
| img_url | varchar(500) | 图片URL |
| start_time | datetime | 事件开始时间 |
| end_time | datetime | 事件结束时间 |
| process_status | tinyint | 处理状态 |
| alarm_status | tinyint | 告警状态 |
| raw_json_data | longtext | 原始JSON数据 |

## API接口

### 1. 事件推送接口
```
POST /risk/push/message
Content-Type: application/json
```

### 2. 事件查询接口
```
GET /safety/monitor/event/page - 分页查询事件
GET /safety/monitor/event/{id} - 查询事件详情
GET /safety/monitor/event/alarms - 查询告警事件
GET /safety/monitor/event/unprocessed - 查询未处理事件
```

### 3. 统计分析接口
```
GET /safety/monitor/event/statistics/by-type - 按事件类型统计
GET /safety/monitor/event/statistics/by-device - 按设备统计告警
```

### 4. 事件处理接口
```
POST /safety/monitor/event/{id}/process - 处理单个事件
POST /safety/monitor/event/batch-process - 批量处理事件
```

## 配置说明

### 1. 数据库配置
执行 `src/main/resources/sql/safety_monitor_event.sql` 创建相关表和索引。

### 2. 依赖注入
确保在Spring容器中正确注册以下服务：
- SafetyMonitorEventService
- AlarmNotificationService

### 3. 告警通知配置
在 `AlarmNotificationServiceImpl` 中配置具体的通知方式：
- 邮件服务配置
- 短信服务配置
- 系统消息配置

## 使用示例

### 1. 事件推送示例
```json
{
  "content": {
    "type": "warn",
    "params": "{\"CityEventListObject\": {...}}"
  },
  "deviceId": 1940708612932972546,
  "deviceType": "GATEWAY",
  "ip": "************",
  "serial": "5CF838712B91",
  "status": "ONLINE"
}
```

### 2. 查询事件示例
```bash
# 查询安全帽检测事件
GET /safety/monitor/event/page?eventType=121&haveAlarm=1

# 查询最近10条告警事件
GET /safety/monitor/event/alarms?limit=10

# 统计事件类型分布
GET /safety/monitor/event/statistics/by-type
```

## 扩展功能

### 1. 实时监控
可以基于WebSocket实现实时事件推送到前端。

### 2. 规则引擎
可以添加规则引擎来自定义告警条件和处理逻辑。

### 3. 数据分析
可以集成数据分析工具进行更深入的安全趋势分析。

### 4. 移动端推送
可以集成移动端推送服务实现手机告警通知。

## 注意事项

1. **性能优化**：对于高频事件推送，建议使用异步处理。
2. **数据清理**：定期清理历史数据，避免表过大影响性能。
3. **异常处理**：完善异常处理机制，确保系统稳定性。
4. **日志记录**：详细记录关键操作日志，便于问题排查。
5. **安全性**：对敏感数据进行加密存储。

## 测试建议

1. **单元测试**：为服务层方法编写单元测试。
2. **集成测试**：测试完整的事件处理流程。
3. **性能测试**：测试高并发场景下的系统性能。
4. **告警测试**：验证各种告警通知方式的有效性。
