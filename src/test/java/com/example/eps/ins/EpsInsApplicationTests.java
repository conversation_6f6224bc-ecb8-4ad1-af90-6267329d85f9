package com.example.eps.ins;

import com.example.eps.ins.common.entity.system.Message;
import com.example.eps.ins.mapper.MessageMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
class EpsInsApplicationTests {

    @Resource
    MessageMapper messageMapper;
    @Test
    void contextLoads() {
        List<Message> messages = messageMapper.selectList(null);
        for (Message message : messages) {
            System.out.println(message);
        }
    }
}
