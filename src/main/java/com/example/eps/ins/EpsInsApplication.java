package com.example.eps.ins;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.reactive.ReactiveManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoReactiveRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoReactiveAutoConfiguration;
import org.springframework.boot.autoconfigure.security.reactive.ReactiveSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.security.reactive.ReactiveUserDetailsServiceAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.security.access.method.AbstractMethodSecurityMetadataSource;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Arrays;


@MapperScan("com.example.eps.ins.mapper")
@EnableTransactionManagement
@EnableRetry
@SpringBootApplication(exclude = {
        ReactiveSecurityAutoConfiguration.class,
        UserDetailsServiceAutoConfiguration.class,
        ReactiveUserDetailsServiceAutoConfiguration.class,
        MongoReactiveAutoConfiguration.class,
        MongoAutoConfiguration.class,
        MongoReactiveRepositoriesAutoConfiguration.class,
        ReactiveManagementWebSecurityAutoConfiguration.class
})
public class EpsInsApplication {

    public static void main(String[] args) {
        try {
            ConfigurableApplicationContext run = SpringApplication.run(EpsInsApplication.class, args);
            String[] beanNames = run.getBeanNamesForType(AbstractMethodSecurityMetadataSource.class);
            System.out.println("Found  AbstractMethodSecurityMetadataSource beans: " + Arrays.toString(beanNames));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
