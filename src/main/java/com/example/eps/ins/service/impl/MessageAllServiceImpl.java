package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.dto.report.model.MessageAllVo;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.entity.system.AllMessageUser;
import com.example.eps.ins.common.entity.system.MessageAll;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.*;
import com.example.eps.ins.service.MessageAllService;
import com.example.eps.ins.mapper.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;

/** @Author: Zhanghongyin @Date: Created in 2023/6/6 11:12 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class MessageAllServiceImpl extends ServiceImpl<MessageAllMapper, MessageAll>
    implements MessageAllService {

  private final MessageAllMapper messageAllMapper;
  private final IndustryMapper industryMapper;
  private final UserMapper userMapper;
  private final EnterpriseMapper enterpriseMapper;
  private final MessageAllUserMapper messageAllUserMapper;

  private static final Integer MESSAGE_STATUS_OK=1;
  private static final Integer MESSAGE_STATUS_NO=0;

  @Override
  public IPage<MessageAll> findMessages(MessageAllVo dto) {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    Enterprise enterprise =
        enterpriseMapper.selectOne(
            new QueryWrapper<Enterprise>().eq("creator_id", currentUser.getUserId()));
    LambdaQueryWrapper<MessageAll> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(dto.getPublishTimeSlot()) && dto.getPublishTimeSlot().size() > 0) {
      queryWrapper.apply(
          "create_time BETWEEN '"
              + dto.getPublishTimeSlot().get(0)
              + "' and '"
              + dto.getPublishTimeSlot().get(1)
              + "'");
    }
    queryWrapper.like(MessageAll::getIndustryId, enterprise.getIndustryId());
    queryWrapper.orderByDesc(MessageAll::getCreateTime);
    Page<MessageAll> page = new Page<>(dto.getPageNum(), dto.getPageSize());
    Page<MessageAll> resp = this.page(page, queryWrapper);
    resp.getRecords()
        .forEach(
            a -> {
              AllMessageUser allMessageUser =
                  messageAllUserMapper.selectOne(
                      new QueryWrapper<AllMessageUser>()
                          .eq("message_id", a.getId())
                          .eq("user_id", currentUser.getUserId()));
              a.setIndustryName(industryMapper.getNames(a.getId()));
              a.setCreateName(userMapper.selectById(a.getCreateId()).getNickName());
              if (ObjectUtils.isEmpty(allMessageUser)) {
                a.setStatus(MESSAGE_STATUS_NO);
              } else {
                a.setStatus(MESSAGE_STATUS_OK);
              }
            });
    return resp;
  }

  @Override
  public MessageAll detail(Long id) {
    return messageAllMapper.selectById(id);
  }

  @Override
  public void lookMessage(Long id) {
    AllMessageUser allMessageUser = new AllMessageUser();
    allMessageUser.setMessageId(id);
    allMessageUser.setCreateTime(new Date());
    allMessageUser.setUserId(EpsUtil.getCurrentUser().getUserId());
    messageAllUserMapper.insert(allMessageUser);
  }

  @Override
  public void createMessage(MessageAll dto) {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    if (ObjectUtils.isEmpty(dto.getId())){
      dto.setCreateTime(new Date());
      dto.setCreateId(currentUser.getUserId());
      messageAllMapper.insert(dto);
    }else {
      messageAllMapper.updateById(dto);
    }
  }

  @Override
  public void deleteMessage(MessageAllVo dto) {
    messageAllMapper.deleteById(dto.getId());
  }
}
