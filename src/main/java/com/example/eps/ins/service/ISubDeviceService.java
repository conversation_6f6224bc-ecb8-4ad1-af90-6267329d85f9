package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.dto.device.SubDeviceInfo;
import com.example.eps.ins.common.dto.riskPush.CameraResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.device.SubDevice;

import java.util.List;

/**
 * 子设备信息 Service
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface ISubDeviceService extends IService<SubDevice> {

    /**
     * 根据父设备ID查询子设备列表
     *
     * @param parentDeviceId 父设备ID
     * @return 子设备列表
     */
    List<SubDevice> findByParentDeviceId(Long parentDeviceId);

    /**
     * 根据外部子设备ID查询子设备
     *
     * @param externalSubDeviceId 外部子设备ID
     * @return 子设备信息
     */
    SubDevice findByExternalSubDeviceId(Long externalSubDeviceId);

    /**
     * 根据父设备ID和序列号查询子设备
     *
     * @param parentDeviceId 父设备ID
     * @param serial 子设备序列号
     * @return 子设备信息
     */
    SubDevice findByParentDeviceIdAndSerial(Long parentDeviceId, String serial);

    /**
     * 分页查询子设备列表
     *
     * @param request 查询请求
     * @param subDevice 查询条件
     * @return 子设备分页列表
     */
    IPage<SubDevice> findSubDevices(QueryRequest request, SubDevice subDevice);

    /**
     * 同步子设备信息
     *
     * @param parentDeviceId 父设备ID
     * @param subDeviceInfoList 子设备信息列表
     * @return 同步结果
     */
    boolean syncSubDevices(Long parentDeviceId, List<SubDeviceInfo> subDeviceInfoList);

    /**
     * 保存或更新子设备信息
     *
     * @param parentDeviceId 父设备ID
     * @param subDeviceInfo 子设备信息
     * @return 子设备实体
     */
    SubDevice saveOrUpdateSubDevice(Long parentDeviceId, SubDeviceInfo subDeviceInfo);

    /**
     * 批量保存子设备信息
     *
     * @param subDeviceList 子设备列表
     * @return 保存结果
     */
    boolean batchSaveSubDevices(List<SubDevice> subDeviceList);

    /**
     * 根据同步状态查询子设备列表
     *
     * @param syncStatus 同步状态
     * @return 子设备列表
     */
    List<SubDevice> findBySyncStatus(Integer syncStatus);

    /**
     * 更新子设备同步状态
     *
     * @param subDeviceId 子设备ID
     * @param syncStatus 同步状态
     * @return 更新结果
     */
    boolean updateSyncStatus(Long subDeviceId, Integer syncStatus);

    /**
     * 根据父设备ID删除所有子设备
     *
     * @param parentDeviceId 父设备ID
     * @return 删除结果
     */
    boolean deleteByParentDeviceId(Long parentDeviceId);

    /**
     * 根据父设备ID和状态查询子设备列表
     *
     * @param parentDeviceId 父设备ID
     * @param status 设备状态
     * @return 子设备列表
     */
    List<SubDevice> findByParentDeviceIdAndStatus(Long parentDeviceId, String status);

    /**
     * 根据企业ID查询相机列表（带企业和设备信息）
     *
     * @param enterpriseId 企业ID
     * @return 相机列表
     */
    List<CameraResponse> findCamerasByEnterpriseId(Long enterpriseId);
}
