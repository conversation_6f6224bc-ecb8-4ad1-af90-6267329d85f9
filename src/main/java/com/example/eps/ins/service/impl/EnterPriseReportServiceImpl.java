package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.eps.ins.common.dto.report.model.*;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.entity.enterprise.*;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.constant.NumberConstant;
import com.example.eps.ins.common.dto.report.req.CompanyView;
import com.example.eps.ins.common.dto.report.resp.*;
import com.example.eps.ins.mapper.*;
import com.example.eps.ins.service.EnterPriseReportService;
import com.example.eps.ins.common.utils.DateTimeUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/** @Author: Zhanghongyin @Date: Created in 2022/11/8 15:04 @Version: 1.0 */
@Service
@RequiredArgsConstructor
public class EnterPriseReportServiceImpl implements EnterPriseReportService {

  private static final Integer LIVE_TWO = 2;
  private static final Integer LIVE_THREE = 3;
  private final EnterPriseReportMapper enterPriseReportMapper;
  private final RegionMapper regionMapper;
  private final RiskMapper riskMapper;
  private final TRptRiskCensusMapper tRptRiskCensusMapper;
  private final PresentSituationMapper presentSituationMapper;
  private final ComplexWorkMapper complexWorkMapper;
  private final EmergencyPlanMapper emergencyPlanMapper;
  private final EmergencyDrillMapper emergencyDrillMapper;
  private final EquipmentMaintainMapper equipmentMaintainMapper;
  private final FiniteSpaceWorkMapper finiteSpaceWorkMapper;
  private final HiddenDangerListMapper hiddenDangerListMapper;
  private final HiddenDangerMapper hiddenDangerMapper;
  private final OutsourceWorkMapper outsourceWorkMapper;
  private final SpecialCertificateMapper specialCertificateMapper;
  private final SpecialPeopleMapper specialPeopleMapper;
  private final TrainEducationMapper trainEducationMapper;
  private final WarningForCardMapper warningForCardMapper;
  private final EnterpriseUserMapper enterpriseUserMapper;

  @Override
  public CakeResp getRiskEnterPrise(Long regionId) {

    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
        return enterPriseReportMapper.getRiskEnterPrise(null, regionId);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
        return enterPriseReportMapper.getRiskEnterPrise(regionId, null);
    } else {
        return enterPriseReportMapper.getRiskEnterPrise(null, null);
    }
  }

  @Override
  public List<RiskListResp> getRisk(Long regionId) {
    // 当前登录人地区id
    Long userRegionId = EpsUtil.getCurrentUser().getRegionId();
    Integer year = Integer.valueOf(DateTimeUtils.getYear());
    // 获取当前登录人该地址所属地区
    Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
    // 获取参数地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    // 所有的风险
    List<Long> integers = riskMapper.selectByParent(NumberConstant.RISK_PARENT_ID);
    List<RiskListResp> listReps = new ArrayList<>();
    if (region.getLevel().equals(NumberConstant.LEVE_ONE)) {
      NumberConstant.regionCompareUserRegion(userRegion,region);
      // 市区和街道
      for (Long integer : integers) {
        // 获取数据
        RiskListResp riskListResp =
                tRptRiskCensusMapper.riskAll(null, year, integer, null);
        // 判断是否为空
        if (ObjectUtils.isEmpty(riskListResp) || ObjectUtils.isEmpty(riskListResp.getRisk())) {
          RiskListResp riskListResp1 = new RiskListResp();
          String name = riskMapper.getRiskName(integer);
          riskListResp1.setRisk(name);
          riskListResp1.setNum(NumberConstant.ZERO);
          listReps.add(riskListResp1);
        } else {
          listReps.add(riskListResp);
        }
      }
    }else if (region.getLevel().equals(NumberConstant.LEVE_TWO)){
      NumberConstant.regionCompareUserRegion(userRegion,region);
      //获取辖区内镇街
      List<Long> regions = regionMapper.selectSonId(region.getId());
      regions.add(region.getId());
      // 市区和街道
      for (Long integer : integers) {
        // 获取数据
        RiskListResp riskListResp =
                tRptRiskCensusMapper.selectLiveTwo(regions, year, integer,null);
        // 判断是否为空
        if (ObjectUtils.isEmpty(riskListResp) || ObjectUtils.isEmpty(riskListResp.getRisk())) {
          RiskListResp riskListResp1 = new RiskListResp();
          String name = riskMapper.getRiskName(integer);
          riskListResp1.setRisk(name);
          riskListResp1.setNum(NumberConstant.ZERO);
          listReps.add(riskListResp1);
        } else {
          listReps.add(riskListResp);
        }
      }
    } else {
      // 市区和街道
      for (Long integer : integers) {
        // 获取数据
        RiskListResp riskListResp =
                tRptRiskCensusMapper.riskAll(regionId, null, integer, null);
        // 判断是否为空
        if (ObjectUtils.isEmpty(riskListResp) || ObjectUtils.isEmpty(riskListResp.getRisk())) {
          RiskListResp riskListResp1 = new RiskListResp();
          String name = riskMapper.getRiskName(integer);
          riskListResp1.setRisk(name);
          riskListResp1.setNum(NumberConstant.ZERO);
          listReps.add(riskListResp1);
        } else {
          listReps.add(riskListResp);
        }
      }
    }
    return listReps;
  }

  @Override
  public CakeResp getUpAndDown(Long regionId) {
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getUpAndDown(null, regionId);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getUpAndDown(regionId, null);
    } else {
      return enterPriseReportMapper.getUpAndDown(null, null);
    }
  }

  @Override
  public EnterpriseProportionResp getThreeNow(Long regionId) {
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getThreeNow(null, regionId);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getThreeNow(regionId, null);
    } else {
      return enterPriseReportMapper.getThreeNow(null, null);
    }
  }

  @Override
  public EnterpriseProportionResp getDangerStandard(Long regionId) {
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getDangerStandard(null, regionId);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getDangerStandard(regionId, null);
    } else {
      return enterPriseReportMapper.getDangerStandard(null, null);
    }
  }

  @Override
  public EnterpriseProportionResp getPlanReport(Long regionId) {
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getPlanReport(null, regionId);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getPlanReport(regionId, null);
    } else {
      return enterPriseReportMapper.getPlanReport(null, null);
    }
  }

  @Override
  public EnterpriseProportionResp getHiddenReport(Long regionId) {
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getHiddenReport(null, regionId);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getHiddenReport(regionId, null);
    } else {
      return enterPriseReportMapper.getHiddenReport(null, null);
    }
  }

  @Override
  public List<EnterpriseHistogramResp> getDrillReport(CompanyView companyView) {
    Long regionId = companyView.getRegionId();
    SimpleDateFormat format=new SimpleDateFormat("yyyy");
    String year = format.format(new Date());
    if (!ObjectUtils.isEmpty(companyView.getYear())){
      year=companyView.getYear().toString();
    }
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getDrillReport(null, regionId,year);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getDrillReport(regionId, null,year);
    } else {
      return enterPriseReportMapper.getDrillReport(null, null,year);
    }
  }

  @Override
  public List<HistogramResp> getListReport(CompanyView companyView) {
    Long regionId = companyView.getRegionId();
    SimpleDateFormat format=new SimpleDateFormat("yyyy");
    String year = format.format(new Date());
    if (!ObjectUtils.isEmpty(companyView.getYear())){
      year=companyView.getYear().toString();
    }
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getListReport(null, regionId,year);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getListReport(regionId, null,year);
    } else {
      return enterPriseReportMapper.getListReport(null, null,year);
    }
  }

  @Override
  public List<EnterpriseHistogramResp> getTrainReport(CompanyView companyView) {
    Long regionId = companyView.getRegionId();
    SimpleDateFormat format=new SimpleDateFormat("yyyy");
    String year = format.format(new Date());
    if (!ObjectUtils.isEmpty(companyView.getYear())){
      year=companyView.getYear().toString();
    }
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getTrainReport(null, regionId,year);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getTrainReport(regionId, null,year);
    } else {
      return enterPriseReportMapper.getTrainReport(null, null,year);
    }
  }

  @Override
  public List<EnterpriseHistogramResp> getMaintainReport(CompanyView companyView) {
    Long regionId = companyView.getRegionId();
    SimpleDateFormat format=new SimpleDateFormat("yyyy");
    String year = format.format(new Date());
    if (!ObjectUtils.isEmpty(companyView.getYear())){
      year=companyView.getYear().toString();
    }
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getMaintainReport(null, regionId,year);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getMaintainReport(regionId, null,year);
    } else {
      return enterPriseReportMapper.getMaintainReport(null, null,year);
    }
  }

  @Override
  public EnterpriseProportionResp getOrderCard(Long regionId) {
// 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return enterPriseReportMapper.getOrderCard(null, regionId);
    } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      return enterPriseReportMapper.getOrderCard(regionId, null);
    } else {
      return enterPriseReportMapper.getOrderCard(null, null);
    }
  }
  @Override
  public IPage<PresentSituation> findPresentSituationLists(
          PresentSituationVo presentSituation) {
//    Long userId = EpsUtil.getCurrentUser().getUserId();
    presentSituation.setPageNum((presentSituation.getPageNum() - 1) * presentSituation.getPageSize());
//    if (!ObjectUtils.isEmpty(presentSituation.getCreatorId())) {
//      userId = presentSituation.getCreatorId();
//    }
    LambdaQueryWrapper<PresentSituation> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(presentSituation.getCreated()) && presentSituation.getCreated().size()>0){
      queryWrapper.in(PresentSituation::getCreatorId, presentSituation.getCreated());
    }
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(PresentSituation::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(PresentSituation::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(presentSituation.getPresentType())) {
      queryWrapper.eq(PresentSituation::getPresentType, presentSituation.getPresentType());
    }
    queryWrapper.orderByDesc(PresentSituation::getCreateTime);
    Integer integer = presentSituationMapper.selectCount(queryWrapper);
    queryWrapper.last("limit " + presentSituation.getPageNum() + "," + presentSituation.getPageSize());
    List<PresentSituation> presentSituations = presentSituationMapper.selectList(queryWrapper);
    Page<PresentSituation> page = new Page<>(presentSituation.getPageNum(), presentSituation.getPageSize());
    presentSituations.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(presentSituations);
    page.setTotal(integer);
    return page;
  }

  @Override
  public IPage<ComplexWork> findComplexWork( ComplexWorkVo outsourceWork) {
//    Long userId = EpsUtil.getCurrentUser().getUserId();
//    if (!ObjectUtils.isEmpty(outsourceWork.getCreatorId())) {
//      userId = outsourceWork.getCreatorId();
//    }
    outsourceWork.setPageNum((outsourceWork.getPageNum() - 1) * outsourceWork.getPageSize());
    LambdaQueryWrapper<ComplexWork> queryWrapper = new LambdaQueryWrapper<>();
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(ComplexWork::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(ComplexWork::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(outsourceWork.getCreated()) && outsourceWork.getCreated().size()>0){
      queryWrapper.in(ComplexWork::getCreatorId, outsourceWork.getCreated());
    }
    if (!ObjectUtils.isEmpty(outsourceWork.getComplexType())) {
      queryWrapper.eq(ComplexWork::getComplexType, outsourceWork.getComplexType());
    }
    queryWrapper.orderByDesc(ComplexWork::getCreateTime);
    Integer integer = complexWorkMapper.selectCount(queryWrapper);
    queryWrapper.last("limit " + outsourceWork.getPageNum() + "," + outsourceWork.getPageSize());
    List<ComplexWork> complexWorks = complexWorkMapper.selectList(queryWrapper);
    Page<ComplexWork> page = new Page<>(outsourceWork.getPageNum(), outsourceWork.getPageSize());
    complexWorks.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(complexWorks);
    page.setTotal(integer);
    return page;
  }

  @Override
  public IPage<EmergencyPlan> findEmergencyPlans(
          EmergencyPlanVO emergencyPlan) {
    emergencyPlan.setPageNum((emergencyPlan.getPageNum() - 1) * emergencyPlan.getPageSize());
    LambdaQueryWrapper<EmergencyPlan> queryWrapper = new LambdaQueryWrapper<>();
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(EmergencyPlan::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(EmergencyPlan::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(emergencyPlan.getCreated()) && emergencyPlan.getCreated().size()>0){
      queryWrapper.in(EmergencyPlan::getCreatorId, emergencyPlan.getCreated());
    }
    if (!ObjectUtils.isEmpty(emergencyPlan.getEmergencyPlanName())) {
      queryWrapper.like(EmergencyPlan::getEmergencyPlanName, emergencyPlan.getEmergencyPlanName());
    }
    queryWrapper.orderByDesc(EmergencyPlan::getCreateTime);
    Integer integer = emergencyPlanMapper.selectCount(queryWrapper);
    queryWrapper.last("limit " + emergencyPlan.getPageNum() + "," + emergencyPlan.getPageSize());
    List<EmergencyPlan> emergencyPlans = emergencyPlanMapper.selectList(queryWrapper);
    Page<EmergencyPlan> page = new Page<>(emergencyPlan.getPageNum(), emergencyPlan.getPageSize());
    emergencyPlans.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(emergencyPlans);
    page.setTotal(integer);
    return page;
  }

  @Override
  public IPage<Emergencydrill> findEmergencyDrills(
          EmergencydrillVO emergencydrill) {
    emergencydrill.setPageNum((emergencydrill.getPageNum() - 1) * emergencydrill.getPageSize());
    LambdaQueryWrapper<Emergencydrill> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(emergencydrill.getCreated()) && emergencydrill.getCreated().size()>0){
      queryWrapper.in(Emergencydrill::getCreatorId, emergencydrill.getCreated());
    }
    if (!ObjectUtils.isEmpty(emergencydrill.getEmergencyDrillName())) {
      queryWrapper.like(
              Emergencydrill::getEmergencyDrillName, emergencydrill.getEmergencyDrillName());
    }
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(Emergencydrill::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(Emergencydrill::getRegionId,regionId.getId());
    }
    queryWrapper.orderByDesc(Emergencydrill::getCreateTime);
    Integer integer = emergencyDrillMapper.selectCount(queryWrapper);
    queryWrapper.last("limit " + emergencydrill.getPageNum() + "," + emergencydrill.getPageSize());
    List<Emergencydrill> emergencydrills = emergencyDrillMapper.selectList(queryWrapper);
    Page<Emergencydrill> page = new Page<>(emergencydrill.getPageNum(), emergencydrill.getPageSize());
    emergencydrills.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(emergencydrills);
    page.setTotal(integer);
    return page;
  }


  @Override
  public IPage<EquipmentMaintain> findEquipmentMaintains(
          EquipmentMaintainVO equipmentMaintain) {
    LambdaQueryWrapper<EquipmentMaintain> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(equipmentMaintain.getCreated()) && equipmentMaintain.getCreated().size()>0){
      queryWrapper.in(EquipmentMaintain::getCreatorId, equipmentMaintain.getCreated());
    }
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(EquipmentMaintain::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(EquipmentMaintain::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(equipmentMaintain.getEquipmentName())) {
      queryWrapper.like(EquipmentMaintain::getEquipmentName, equipmentMaintain.getEquipmentName());
    }
    queryWrapper.orderByDesc(EquipmentMaintain::getCreateTime);
    Integer integer = equipmentMaintainMapper.selectCount(queryWrapper);
    equipmentMaintain.setPageNum((equipmentMaintain.getPageNum() - 1) * equipmentMaintain.getPageSize());
    queryWrapper.last("limit " + equipmentMaintain.getPageNum() + "," + equipmentMaintain.getPageSize());
    List<EquipmentMaintain> equipmentMaintains = equipmentMaintainMapper.selectList(queryWrapper);
    Page<EquipmentMaintain> page = new Page<>(equipmentMaintain.getPageNum(), equipmentMaintain.getPageSize());
    equipmentMaintains.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(equipmentMaintains);
    page.setTotal(integer);
    return page;
  }

  @Override
  public IPage<FiniteSpaceWork> findFiniteSpaceWorks( FiniteSpaceWorkVO finiteSpaceWork) {
//    Long userId = EpsUtil.getCurrentUser().getUserId();
//    if (!ObjectUtils.isEmpty(finiteSpaceWork.getCreatorId())){
//      userId=finiteSpaceWork.getCreatorId();
//    }
    LambdaQueryWrapper<FiniteSpaceWork> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(finiteSpaceWork.getCreated()) && finiteSpaceWork.getCreated().size()>0){
      queryWrapper.in(FiniteSpaceWork::getCreatorId, finiteSpaceWork.getCreated());
    }
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(FiniteSpaceWork::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(FiniteSpaceWork::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(finiteSpaceWork.getSpaceName())) {
      queryWrapper.like(FiniteSpaceWork::getSpaceName, finiteSpaceWork.getSpaceName());
    }
    queryWrapper.orderByDesc(FiniteSpaceWork::getCreateTime);
    Integer integer = finiteSpaceWorkMapper.selectCount(queryWrapper);
    finiteSpaceWork.setPageNum((finiteSpaceWork.getPageNum() - 1) * finiteSpaceWork.getPageSize());
    queryWrapper.last("limit " + finiteSpaceWork.getPageNum() + "," + finiteSpaceWork.getPageSize());
    List<FiniteSpaceWork> list = finiteSpaceWorkMapper.selectList(queryWrapper);
    Page<FiniteSpaceWork> page = new Page<>(finiteSpaceWork.getPageNum(), finiteSpaceWork.getPageSize());
    list.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(list);
    page.setTotal(integer);
    return page;
  }

//  @Override
//  public IPage<HiddenDangerList> findHiddenDangerList( HiddenDangerList hiddenDangerList) {
//    Long userId=hiddenDangerList.getCreatorId();
//    LambdaQueryWrapper<HiddenDangerList> queryWrapper = new LambdaQueryWrapper<>();
//    queryWrapper.eq(HiddenDangerList::getCreatorId, userId);
//    if (!ObjectUtils.isEmpty(hiddenDangerList.getStartDate())) {
//      queryWrapper.between(HiddenDangerList::getHiddenDangerInspect,hiddenDangerList.getStartDate().get(0),
//              hiddenDangerList.getStartDate().get(1));
//    }
//    Region regionId = getRegionId();
//    if (regionId.getLevel().equals(LIVE_TWO)){
//      List<Long> list = regionMapper.selectSon(regionId.getId());
//      list.add(regionId.getId());
//      queryWrapper.in(HiddenDangerList::getRegionId,list);
//    }else if (regionId.getLevel().equals(LIVE_THREE)){
//      queryWrapper.eq(HiddenDangerList::getRegionId,regionId.getId());
//    }
//    if (!ObjectUtils.isEmpty(hiddenDangerList.getHiddenDangerStatus())) {
//      queryWrapper.like(HiddenDangerList::getHiddenDangerStatus, hiddenDangerList.getHiddenDangerStatus());
//    }
//    if (!ObjectUtils.isEmpty(hiddenDangerList.getHiddenStatus())) {
//      queryWrapper.eq(HiddenDangerList::getHiddenStatus, hiddenDangerList.getHiddenStatus());
//    }
//    if (!ObjectUtils.isEmpty(hiddenDangerList.getHiddenDangerCycle())) {
//      queryWrapper.eq(HiddenDangerList::getHiddenDangerCycle, hiddenDangerList.getHiddenDangerCycle());
//    }
//    queryWrapper.orderByDesc(HiddenDangerList::getCreateTime);
//    Integer integer = hiddenDangerListMapper.selectCount(queryWrapper);
//    request.setPageNum((request.getPageNum() - 1) * request.getPageSize());
//    queryWrapper.last("limit " + request.getPageNum() + "," + request.getPageSize());
//    List<HiddenDangerList> list = hiddenDangerListMapper.selectList(queryWrapper);
//    Page<HiddenDangerList> page = new Page<>(request.getPageNum(), request.getPageSize());
//    list.forEach(a->{
//      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
//      if(a.getHiddenDangerCycle().equals(EnterpriseDeclarationConstant.HIDDEN_DANGER_ONE)){
//        a.setHiddenDangerInspect(a.getHiddenDangerInspect()+"~"+a.getHiddenDangerEnd());
//      }
//    });
//    page.setRecords(list);
//    page.setTotal(integer);
//    return page;
//  }

  @Override
  public IPage<HiddenDanger> findHiddenDanger( HiddenDangerVo emergencyPlan) {
//    Long userId = emergencyPlan.getCreatorId();
    LambdaQueryWrapper<HiddenDanger> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(emergencyPlan.getCreated()) && emergencyPlan.getCreated().size()>0){
      queryWrapper.in(HiddenDanger::getCreatorId, emergencyPlan.getCreated());
    }
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(HiddenDanger::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(HiddenDanger::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(emergencyPlan.getHiddenDangerName())) {
      queryWrapper.like(HiddenDanger::getHiddenDangerName, emergencyPlan.getHiddenDangerName());
    }
    queryWrapper.orderByDesc(HiddenDanger::getCreateTime);
    Integer integer = hiddenDangerMapper.selectCount(queryWrapper);
    emergencyPlan.setPageNum((emergencyPlan.getPageNum() - 1) * emergencyPlan.getPageSize());
    queryWrapper.last("limit " + emergencyPlan.getPageNum() + "," + emergencyPlan.getPageSize());
    List<HiddenDanger> list = hiddenDangerMapper.selectList(queryWrapper);
    Page<HiddenDanger> page = new Page<>(emergencyPlan.getPageNum(), emergencyPlan.getPageSize());
    list.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(list);
    page.setTotal(integer);
    return page;
  }


  @Override
  public IPage<OutsourceWork> findOutsourceWork( OutsourceWorkVO outsourceWork) {
//    Long userId = EpsUtil.getCurrentUser().getUserId();
//    if (!ObjectUtils.isEmpty(outsourceWork.getCreatorId())){
//      userId=outsourceWork.getCreatorId();
//    }
    LambdaQueryWrapper<OutsourceWork> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(outsourceWork.getCreated()) && outsourceWork.getCreated().size()>0){
      queryWrapper.in(OutsourceWork::getCreatorId, outsourceWork.getCreated());
    }
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(OutsourceWork::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(OutsourceWork::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(outsourceWork.getWorkType())) {
      queryWrapper.eq(OutsourceWork::getWorkType, outsourceWork.getWorkType());
    }
    queryWrapper.orderByDesc(OutsourceWork::getCreateTime);
    Integer integer = outsourceWorkMapper.selectCount(queryWrapper);
    outsourceWork.setPageNum((outsourceWork.getPageNum() - 1) * outsourceWork.getPageSize());
    queryWrapper.last("limit " + outsourceWork.getPageNum() + "," + outsourceWork.getPageSize());
    List<OutsourceWork> list = outsourceWorkMapper.selectList(queryWrapper);
    Page<OutsourceWork> page = new Page<>(outsourceWork.getPageNum(), outsourceWork.getPageSize());
    list.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(list);
    page.setTotal(integer);
    return page;
  }


  @Override
  public IPage<SpecialCertificate> findSpecialCertificates(
          SpecialCertificateVo specialCertificate) {
//    Long userId = EpsUtil.getCurrentUser().getUserId();
//    if (!ObjectUtils.isEmpty(specialCertificate.getCreatorId())){
//      userId=specialCertificate.getCreatorId();
//    }
    LambdaQueryWrapper<SpecialCertificate> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(specialCertificate.getCreated()) && specialCertificate.getCreated().size()>0){
      queryWrapper.in(SpecialCertificate::getCreatorId, specialCertificate.getCreated());
    }
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(SpecialCertificate::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(SpecialCertificate::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(specialCertificate.getSpecialType())) {
      queryWrapper.eq(SpecialCertificate::getSpecialType, specialCertificate.getSpecialType());
    }
    queryWrapper.orderByDesc(SpecialCertificate::getCreateTime);
    Integer integer = specialCertificateMapper.selectCount(queryWrapper);
    specialCertificate.setPageNum((specialCertificate.getPageNum() - 1) * specialCertificate.getPageSize());
    queryWrapper.last("limit " + specialCertificate.getPageNum() + "," + specialCertificate.getPageSize());
    List<SpecialCertificate> list = specialCertificateMapper.selectList(queryWrapper);
    Page<SpecialCertificate> page = new Page<>(specialCertificate.getPageNum(), specialCertificate.getPageSize());
    list.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(list);
    page.setTotal(integer);
    return page;
  }


  @Override
  public IPage<SpecialPeople> findSpecialPeoples(
          SpecialPeopleVo emergencyPlan) {
//    Long userId = emergencyPlan.getCreatorId();
    LambdaQueryWrapper<SpecialPeople> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(emergencyPlan.getCreated()) && emergencyPlan.getCreated().size()>0){
      queryWrapper.in(SpecialPeople::getCreatorId, emergencyPlan.getCreated());
    }
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(SpecialPeople::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(SpecialPeople::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(emergencyPlan.getSpecialName())) {
      queryWrapper.like(SpecialPeople::getSpecialName, emergencyPlan.getSpecialName());
    }
    queryWrapper.orderByDesc(SpecialPeople::getCreateTime);
    Integer integer = specialPeopleMapper.selectCount(queryWrapper);
    emergencyPlan.setPageNum((emergencyPlan.getPageNum() - 1) * emergencyPlan.getPageSize());
    queryWrapper.last("limit " + emergencyPlan.getPageNum() + "," + emergencyPlan.getPageSize());
    List<SpecialPeople> list = specialPeopleMapper.selectList(queryWrapper);
    Page<SpecialPeople> page = new Page<>(emergencyPlan.getPageNum(), emergencyPlan.getPageSize());
    list.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(list);
    page.setTotal(integer);
    return page;
  }


  @Override
  public IPage<TrainEducation> findTrainEducations(TrainEducationVo trainEducation) {
//    Long userId = trainEducation.getCreatorId();
    LambdaQueryWrapper<TrainEducation> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(trainEducation.getCreated()) && trainEducation.getCreated().size()>0){
      queryWrapper.in(TrainEducation::getCreatorId, trainEducation.getCreated());
    }
    Region regionId = getRegionId();
    if (regionId.getLevel().equals(LIVE_TWO)){
      List<Long> list = regionMapper.selectSon(regionId.getId());
      list.add(regionId.getId());
      queryWrapper.in(TrainEducation::getRegionId,list);
    }else if (regionId.getLevel().equals(LIVE_THREE)){
      queryWrapper.eq(TrainEducation::getRegionId,regionId.getId());
    }
    if (!ObjectUtils.isEmpty(trainEducation.getTrainTheme())) {
      queryWrapper.like(
              TrainEducation::getTrainTheme, trainEducation.getTrainTheme());
    }
    queryWrapper.orderByDesc(TrainEducation::getCreateTime);
    Integer integer = trainEducationMapper.selectCount(queryWrapper);
    trainEducation.setPageNum((trainEducation.getPageNum() - 1) * trainEducation.getPageSize());
    queryWrapper.last("limit " + trainEducation.getPageNum() + "," + trainEducation.getPageSize());
    List<TrainEducation> list = trainEducationMapper.selectList(queryWrapper);
    Page<TrainEducation> page = new Page<>(trainEducation.getPageNum(), trainEducation.getPageSize());
    list.forEach(a->{
      a.setEnterpriseName(enterpriseUserMapper.selectById(a.getCreatorId()).getEnterpriseName());
    });
    page.setRecords(list);
    page.setTotal(integer);
    return page;
  }

  @Override
  public IPage<WarningForCard> findWarningForCards(
          QueryRequest request, WarningForCard warningForCard) {
    request.setPageNum((request.getPageNum() - 1) * request.getPageSize());
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    Long regionId = currentUser.getRegionId();
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    List<WarningForCard> list = warningForCardMapper.selectAllSystem(region.getLevel(),warningForCard.getCode(),
            warningForCard.getName(),currentUser.getUserId(),regionId,request.getPageNum(),request.getPageSize());
    List<WarningForCard> total = warningForCardMapper.selectAllSystem(region.getLevel(),warningForCard.getCode(),
            warningForCard.getName(),currentUser.getUserId(),regionId,null,null);
    IPage<WarningForCard> page = new Page<>(request.getPageNum(), request.getPageSize());
    page.setRecords(list);
    page.setTotal(total.size());
    return page;
  }

  private Region getRegionId(){
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    return regionMapper.selectById(currentUser.getRegionId());
  }
}
