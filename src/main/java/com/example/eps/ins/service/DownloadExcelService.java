package com.example.eps.ins.service;

import com.example.eps.ins.common.dto.report.model.RiskContentVo;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.model.EnterpriseVo;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2023/1/11 15:16
 * @Version: 1.0
 */
public interface DownloadExcelService {
    /**
     * 导出excel
     * @param httpServletResponse
     * @param enterprise
     */
    void download(HttpServletResponse httpServletResponse, Enterprise enterprise);

    void message(HttpServletResponse httpServletResponse, MessageUser messageUser);

    /**
     * 导出隐患
     * @param httpServletResponse
     * @param enterprise
     */
    void hidden(HttpServletResponse httpServletResponse, EnterpriseVo enterprise);

    void warningForCard(HttpServletResponse httpServletResponse, WarningForCard warningForCard);

    void riskContent(HttpServletResponse httpServletResponse, RiskContentVo riskContentVo);
}
