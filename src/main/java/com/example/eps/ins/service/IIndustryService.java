package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Industry;

import java.util.List;

/**
 * 行业表 Service接口
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:40
 */
public interface IIndustryService extends IService<Industry> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param industry industry
     * @return IPage<Industry>
     */
    IPage<Industry> findIndustrys(QueryRequest request, Industry industry);

    /**
     * 查询（所有）
     *
     * @return List<Industry>
     */
    List<Industry> findIndustries();

    /**
     * 新增
     *
     * @param industry industry
     */
    void createIndustry(Industry industry);

    /**
     * 修改
     *
     * @param industry industry
     */
    void updateIndustry(Industry industry);

    /**
     * 删除
     *
     * @param industry industry
     */
    void deleteIndustry(Industry industry);
}
