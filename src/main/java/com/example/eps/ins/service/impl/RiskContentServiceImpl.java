package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.constant.EpsConstant;
import com.example.eps.ins.common.dto.report.model.RiskContentVo;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.entity.enterprise.RiskContent;
import com.example.eps.ins.common.entity.enterprise.RiskContentUser;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.EnterpriseMapper;
import com.example.eps.ins.mapper.RegionMapper;
import com.example.eps.ins.mapper.RiskContentMapper;
import com.example.eps.ins.mapper.RiskContentUserMapper;
import com.example.eps.ins.service.RiskContentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.*;

/** @Author: Zhanghongyin @Date: Created in 2023/6/10 11:24 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class RiskContentServiceImpl extends ServiceImpl<RiskContentMapper, RiskContent>
    implements RiskContentService {
  private final RiskContentMapper riskContentMapper;
  private final RegionMapper regionMapper;
  private final RiskContentUserMapper riskContentUserMapper;
  private final EnterpriseMapper enterpriseMapper;
  private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;



  @Override
  public List<RiskContent> getList(RiskContentVo dto) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    Enterprise enterprise =
        enterpriseMapper.selectOne(new QueryWrapper<Enterprise>().eq("creator_id", userId));
    List<Long> risks = enterpriseMapper.getRisks(enterprise.getId());
    if (ObjectUtils.isEmpty(risks)) {
      risks.add(EpsConstant.NOT_IN);
    }
    QueryWrapper<RiskContent> queryWrapper = new QueryWrapper<>();
    queryWrapper
        .eq("industry_id", enterprise.getIndustryId())
        .or()
        .eq("industry_id", EpsConstant.INDUSTRY_ID_ZEOR)
        .or()
        .in("risk_id", risks);
    return riskContentMapper.selectList(queryWrapper);
  }

  @Override
  public IPage<RiskContent> getSystemList(RiskContentVo dto) {
    dto.setPageNum((dto.getPageNum() - 1) * dto.getPageSize());
    Long userId = EpsUtil.getCurrentUser().getUserId();
    Enterprise enterprise =
            enterpriseMapper.selectOne(new QueryWrapper<Enterprise>().eq("creator_id", userId));
    List<Long> risks = enterpriseMapper.getRisks(enterprise.getId());
    if (ObjectUtils.isEmpty(risks)) {
      risks.add(0L);
    }
    Integer count =
            riskContentMapper.selectCount(
                    new QueryWrapper<RiskContent>()
                            .eq("industry_id", enterprise.getIndustryId())
                            .or()
                            .eq("industry_id", 0)
                            .or()
                            .in("risk_id", risks)
                            .like("content_text", dto.getContentText()));
    List<RiskContent> industryId =
            riskContentMapper.selectList(
                    new QueryWrapper<RiskContent>()
                            .eq("industry_id", enterprise.getIndustryId())
                            .or()
                            .eq("industry_id", 0)
                            .or()
                            .in("risk_id", risks)
                            .like("content_text", dto.getContentText()));
    Page<RiskContent> page = new Page<>();
    page.setTotal(count);
    page.setRecords(industryId);
    return page;
  }


  @Override
  public void add(RiskContentVo dto) {
    // 获取所有的企业申报信息
    List<EnterpriseDeclarationResponse> enterpriseDeclarationResponses =
        enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
    // 获取所有申报状态
    List<Integer> collect =
        enterpriseDeclarationResponses.stream()
            .map(EnterpriseDeclarationResponse::getStatus)
            .collect(Collectors.toList());
    if (ObjectUtils.isEmpty(enterpriseDeclarationResponses)) {
      throw new EpsException("请完善企业信息后创建企业应急预案信息！");
    }
    if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)) {
      throw new EpsException("企业信息暂未审核通过，请审核通过后创建企业应急预案信息！");
    }
    Long userId = EpsUtil.getCurrentUser().getUserId();
    Enterprise enterprise =
        enterpriseMapper.selectOne(new QueryWrapper<Enterprise>().eq("creator_id", userId));
    Long riskId = dto.getRiskId();
//    RiskContentUser count =
//        riskContentUserMapper.selectOne(
//            new QueryWrapper<RiskContentUser>()
//                .eq("content_id", riskId)
//                .eq("enterprise_id", enterprise.getId()));
//    if (ObjectUtils.isEmpty(count)) {
      RiskContentUser riskContentUser = new RiskContentUser();
      riskContentUser.setContentId(riskId);
      riskContentUser.setEnterpriseId(enterprise.getId());
      riskContentUser.setContentTextUser(dto.getContentTextUser());
      riskContentUser.setRegionId(enterprise.getRegionTownId());
      riskContentUser.setCreateTime(new Date());
      riskContentUserMapper.insert(riskContentUser);
//    }else {
//      count.setContentTextUser(dto.getContentTextUser());
//      count.setRegionId(enterprise.getRegionTownId());
//      riskContentUserMapper.updateById(count);
//    }
  }

  @Override
  public IPage<RiskContent> getAllList(RiskContentVo dto) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    Enterprise enterprise =
        enterpriseMapper.selectOne(new QueryWrapper<Enterprise>().eq("creator_id", userId));
    dto.setPageNum((dto.getPageNum() - 1) * dto.getPageSize());
    List<RiskContent> riskContents=enterpriseMapper
            .selectRiskContentAll(enterprise.getId(),dto.getContentText()
                    ,dto.getPageNum(),dto.getPageSize());
    List<RiskContent> total=enterpriseMapper
            .selectRiskContentAll(enterprise.getId(),dto.getContentText()
                    ,null,null);
    IPage<RiskContent> page = new Page<>(dto.getPageNum(), dto.getPageSize());
    page.setRecords(riskContents);
    page.setTotal(total.size());
    return page;
  }

  @Override
  public void delete(Long id) {
    riskContentUserMapper.delete(new QueryWrapper<RiskContentUser>().eq("content_id", id));
  }

  @Override
  public IPage<Enterprise> enterpriseList(RiskContentVo dto) {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    List<Long> selectSon=new ArrayList<>();
    Region region = regionMapper.selectById(currentUser.getRegionId());
    if (region.getLevel().equals(REGION_TWO_LIVE)) {
      // 获取子id
      selectSon = regionMapper.selectSon(currentUser.getRegionId());
    }else if (region.getLevel().equals(REGION_THREE_LIVE)){
      selectSon.add(region.getId());
    }
    Page<Enterprise> page = new Page<>(dto.getPageNum(), dto.getPageSize());
    List<Enterprise> list=enterpriseMapper.enterpriseList(dto.getEnterpriseName(),
            selectSon,
            dto.getPageSize(),(dto.getPageNum()-1)*dto.getPageSize());
    page.setRecords(list);
    page.setTotal(enterpriseMapper.enterpriseList(dto.getEnterpriseName(),
            selectSon,null,null).size());
    return page;
  }
}
