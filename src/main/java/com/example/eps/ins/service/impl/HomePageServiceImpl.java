package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.constant.NumberConstant;
import com.example.eps.ins.common.dto.report.resp.*;
import com.example.eps.ins.mapper.HomePageMapper;
import com.example.eps.ins.mapper.RegionMapper;
import com.example.eps.ins.mapper.RiskMapper;
import com.example.eps.ins.service.HomePageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/** @Author: <PERSON><PERSON>yin @Date: Created in 2021/12/14 15:11 @Version: 1.0 */
@Service
@RequiredArgsConstructor
public class HomePageServiceImpl implements HomePageService {
  private final RegionMapper regionMapper;
  private final HomePageMapper homePageMapper;
  private final RiskMapper riskMapper;
  @Override
  public EnterpriseViewResp getList() {
    Long regionId = getRegionId();
    SimpleDateFormat format=new SimpleDateFormat("yyyy");
    String format1 = format.format(new Date());
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      return homePageMapper.selectByHomeAllLiveTwo(regionId,format1,null);
    }else if (region.getLevel().equals(NumberConstant.LEVE_THREE)){
      return homePageMapper.selectByHomeAllLiveTwo(null,format1,regionId);
    }
    return homePageMapper.selectByHomeAllLiveTwo(null,format1,null);
  }

  @Override
  public List<AccidentYearResp> getYear() {
    // 获取当前登录人地区id
    Long regionId = getRegionId();
    // 根据地区id查询地区表数据
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    // 判断是否是镇街
    if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      // 如果是镇街，则将parentId赋值给regionId
      regionId = region.getParentId();
    }
    List<AccidentYearResp> accidentYearReps = new ArrayList<>();
    // 根据地区id查询表中存在的年份
    List<Integer> year = homePageMapper.getYears(regionId);
    for (Integer integer : year) {
      // 根据年份和地区id查询返回数据
      AccidentYearResp accidentYear = homePageMapper.getAccidentYear(regionId, integer);
      accidentYearReps.add(accidentYear);
    }
    return accidentYearReps;
  }

  @Override
  public EnforcementHomeResp getLawEnforcement() {
    // 获取当前登录人地区id
    Long regionId = getRegionId();
    // 根据地区id查询地区表数据
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    EnforcementHomeResp enforcementAll;
    EnforcementHomeResp rectifyAll;
    // 判断是否是镇街
    if (region.getLevel().equals(NumberConstant.LEVE_ONE)) {
      enforcementAll =
              homePageMapper.getDataViewAll( null);
      rectifyAll = homePageMapper.getRectifyAll( null);
    }else if (region.getLevel().equals(NumberConstant.LEVE_TWO)){
      //获取辖区内镇街
      List<Long> regions = regionMapper.selectSonId(region.getId());
      regions.add(region.getId());
      enforcementAll =
              homePageMapper.getDataViewOrTwoLive(regions);
      rectifyAll = homePageMapper.getRectifyOrTwoLive(regions);
    } else {
      // 区县执法数据
      enforcementAll = homePageMapper.getDataViewAll(regionId);
      rectifyAll = homePageMapper.getRectifyAll(regionId);
      }
      if (ObjectUtils.isEmpty(rectifyAll)) {
        enforcementAll.setRectifyNumber(NumberConstant.ZERO);
        return enforcementAll;
      }
      enforcementAll.setRectifyNumber(rectifyAll.getRectifyNumber());
      return enforcementAll;
  }

  @Override
  public List<RiskListResp> getRisk() {
    // 获取当前登录人地区id
    Long regionId = getRegionId();
    SimpleDateFormat format=new SimpleDateFormat("yyyy");
    String format1 = format.format(new Date());
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
      // 所有的风险
      List<Long> integers = riskMapper.selectByParent(NumberConstant.RISK_PARENT_ID);
      List<RiskListResp> listReps = new ArrayList<>();
      // 市区和街道
      for (Long integer : integers) {
        // 获取数据
        RiskListResp riskListResp =
                homePageMapper.riskAllTwo(regionId, integer,format1);
        // 判断是否为空
        if (ObjectUtils.isEmpty(riskListResp) || ObjectUtils.isEmpty(riskListResp.getRisk())) {
          RiskListResp riskListResp1 = new RiskListResp();
          String name = riskMapper.getRiskName(integer);
          riskListResp1.setRisk(name);
          riskListResp1.setNum(NumberConstant.ZERO);
          listReps.add(riskListResp1);
        } else {
          listReps.add(riskListResp);
        }
      }
      return listReps;
    }else {
      // 所有的风险
      List<Long> integers = riskMapper.selectByParent(NumberConstant.RISK_PARENT_ID);
      List<RiskListResp> listReps = new ArrayList<>();
      // 市区和街道
      for (Long integer : integers) {
        // 获取数据
        RiskListResp riskListResp =
                homePageMapper.riskAll(regionId, integer,format1);
        // 判断是否为空
        if (ObjectUtils.isEmpty(riskListResp) || ObjectUtils.isEmpty(riskListResp.getRisk())) {
          RiskListResp riskListResp1 = new RiskListResp();
          String name = riskMapper.getRiskName(integer);
          riskListResp1.setRisk(name);
          riskListResp1.setNum(NumberConstant.ZERO);
          listReps.add(riskListResp1);
        } else {
          listReps.add(riskListResp);
        }
      }
      return listReps;
    }
  }

  @Override
  public List<TRptIndustryScatterResp> getIndustry() {
    Long regionId = getRegionId();
    // 获取当前登录人该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
      regionId = region.getParentId();
    }
    return homePageMapper.getCity(regionId);
  }

  private Long getRegionId() {
    // 当前登录人地区id
    return EpsUtil.getCurrentUser().getRegionId();
  }

  @Override
  public List<AccidentSortResp> getSort() {
    //当前登录人地区id
    Long regionId = getRegionId();
    // 获取当前登录人该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_THREE)){
      regionId=region.getParentId();
    }
    // 获取事故表中所有的事故
    List<AccidentSortResp> accidentResps=homePageMapper.getAccidentSort(regionId);
    // 删除集合中的空元素
    accidentResps.removeAll(Collections.singleton(null));
    // 排序返回
    accidentResps.sort((o1, o2) -> o2.getAccidentNum().compareTo(o1.getAccidentNum()));
    return accidentResps;
  }
}
