package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.ComplexWork;

import java.io.IOException;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023/1/9 13:38
 * @Version: 1.0
 */
public interface ComplexWorkService extends IService<ComplexWork> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param complexWork complexWork
     * @return IPage<complexWork>
     */
    IPage<ComplexWork> findComplexWork(QueryRequest request, ComplexWork complexWork);

    /**
     * 新增
     * @param complexWork complexWork
     * @throws IOException IOException
     */
    String createComplexWork(ComplexWork complexWork);

    /**
     * 详情
     * @param id  id
     * @return
     */
    ComplexWork findComplexWork(Long id);


    /**
     * 删除
     *
     */
    void deleteComplexWork(Long id);

    /**
     * 删除文件
     * @param id
     * @param filePath
     */
    void deleteFile(Long id, String filePath);
}
