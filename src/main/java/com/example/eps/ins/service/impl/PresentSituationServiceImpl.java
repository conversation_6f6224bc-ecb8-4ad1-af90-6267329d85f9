package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.PresentSituation;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.PresentSituationMapper;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.PresentSituationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/** @Author: Zhanghongyin @Date: Created in 2022/10/11 9:52 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class PresentSituationServiceImpl extends ServiceImpl<PresentSituationMapper, PresentSituation>
    implements PresentSituationService {
  private final FdfsClientService fdfsClientService;
  private final PresentSituationMapper presentSituationMapper;
  private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

  @Override
  public IPage<PresentSituation> findPresentSituations(
      QueryRequest request, PresentSituation presentSituation) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    if (!ObjectUtils.isEmpty(presentSituation.getCreatorId())){
      userId=presentSituation.getCreatorId();
    }
    LambdaQueryWrapper<PresentSituation> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(PresentSituation::getCreatorId, userId);
    if (!ObjectUtils.isEmpty(presentSituation.getPresentType())) {
      queryWrapper.eq(PresentSituation::getPresentType, presentSituation.getPresentType());
    }
    queryWrapper.orderByDesc(PresentSituation::getCreateTime);
    Page<PresentSituation> page = new Page<>(request.getPageNum(), request.getPageSize());
    return this.page(page, queryWrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String createPresentSituation(PresentSituation presentSituation) {
    //获取所有的企业申报信息
    List<EnterpriseDeclarationResponse> enterprise =
            enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
    //获取所有申报状态
    List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
    if (ObjectUtils.isEmpty(enterprise)){
      throw new EpsException("请完善企业信息后创建安全三同时及现状评价信息！");
    }
    if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
      throw new EpsException("企业信息暂未审核通过，请审核通过后创建三同时及现状评价信息！");
    }
    // 保存消息
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    if (ObjectUtils.isEmpty(presentSituation.getPresentFile())){
      presentSituation.setPresentFile("[]");
    }
    if (ObjectUtils.isEmpty(presentSituation.getPresentImage())){
      presentSituation.setPresentImage("[]");
    }
    presentSituation.setCreateTime(new Date());
    if (ObjectUtils.isEmpty(presentSituation.getId())) {
      presentSituation.setCreatorId(currentUser.getUserId());
      presentSituation.setRegionId(enterprise.get(0).getRegionTownId());
      this.save(presentSituation);
    } else {
      this.update(presentSituation, new QueryWrapper<PresentSituation>()
              .eq("id", presentSituation.getId()));
    }
    return "";
  }

  @Override
  public PresentSituation findPresentSituation(Long id) {
    return presentSituationMapper.selectById(id);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deletePresentSituation(Long id) {
    LambdaQueryWrapper<PresentSituation> userWarpper = new LambdaQueryWrapper<>();
    userWarpper.eq(PresentSituation::getId, id);
    PresentSituation emergencyPlan = this.getById(id);
    if (emergencyPlan == null) {
      return;
    }
    JSONArray fileArray = JSON.parseArray(emergencyPlan.getPresentFile());
    JSONArray imageArray = JSON.parseArray(emergencyPlan.getPresentImage());
    presentSituationMapper.delete(userWarpper);
    for (Object obj : fileArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
    for (Object obj : imageArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
  }

  @Override
  public void deleteFile(Long id, String filePath) {
    if (!ObjectUtils.isEmpty(id)) {
      PresentSituation presentSituation = presentSituationMapper.selectById(id);
      if (ObjectUtils.isEmpty(presentSituation)) {
        return;
      }
      JSONArray fileArray = JSON.parseArray(presentSituation.getPresentFile());
      JSONArray imageArray = JSON.parseArray(presentSituation.getPresentImage());
      JSONArray fileJson = getArray(filePath, fileArray);
      JSONArray imageJson = getArray(filePath, imageArray);
      presentSituation.setPresentFile(fileJson.toString());
      presentSituation.setPresentImage(imageJson.toString());
      presentSituationMapper.updateById(presentSituation);
    }
    fdfsClientService.deleteFile(filePath);
  }

  private JSONArray getArray(String filePath, JSONArray fileArray) {
    JSONArray json = new JSONArray();
    for (Object obj : fileArray) {
      String filePath1 = ((JSONObject) obj).getString("filePath");
      if (filePath1.equals(filePath)){
        continue;
      }
      Map<String, Object> map=new HashMap<>();
      map.put("fileUrl",((JSONObject) obj).getString("fileUrl"));
      map.put("filePath",((JSONObject) obj).getString("filePath"));
      map.put("fileName",((JSONObject) obj).getString("fileName"));
      json.add(EpsUtil.toJsonObj(map));
    }
    return json;
  }
}
