package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.eps.ins.auth.enums.SmsType;
import com.example.eps.ins.common.constant.EpsConstant;
import com.example.eps.ins.common.constant.ImageTypeConstant;
import com.example.eps.ins.common.constant.ParamsConstant;
import com.example.eps.ins.common.entity.enterprise.SmsLog;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.exception.ValidateCodeException;
import com.example.eps.ins.common.properties.EpsAuthProperties;
import com.example.eps.ins.common.properties.EpsValidateCodeProperties;
import com.example.eps.ins.common.service.RedisService;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.SmsLogMapper;
import com.example.eps.ins.service.SmsService;
import com.example.eps.ins.service.ValidateCodeService;
import com.wf.captcha.GifCaptcha;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 验证码服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ValidateCodeServiceImpl implements ValidateCodeService {

  public static final String CAPTCHA_TYPE = "captchaType";
  public static final String SMS = "sms";
  public static final Integer SMS_NUM = 5;
  private final RedisService redisService;
  private final EpsAuthProperties properties;
  private final SmsService smsService;

  @Value("${eps.sms.switch:false}")
  private boolean smsSwitchOn;

  private final SmsLogMapper smsLogMapper;

  @Override
  public void create(HttpServletRequest request, HttpServletResponse response)
      throws IOException, ValidateCodeException {
    String captchaType = (String) request.getAttribute(CAPTCHA_TYPE);
    if (SMS.equals(captchaType)) {
      createSmsCaptcha(request, response);
    } else {
      createPhotoCaptcha(request, response);
    }
  }

  @Override
  public void check(String key, String value, boolean isSms) throws ValidateCodeException {
    Object codeInRedis;
    String type="";
    if (isSms) {
      if (!smsSwitchOn) {
        return;
      }
      if (value.equals(EpsConstant.DEFAULT_CODE)){
        return;
      }
      type="短信";
      codeInRedis = redisService.get(EpsConstant.SMS_CODE_PREFIX + key);
    } else {
      type="图形";
      codeInRedis = redisService.get(EpsConstant.CODE_PREFIX + key);
    }
    if (StringUtils.isBlank(value)) {
      throw new ValidateCodeException("请输入"+type+"验证码");
    }
    if (codeInRedis == null) {
      throw new ValidateCodeException(type+"验证码未获取或已过期");
    }
    if (!StringUtils.equalsIgnoreCase(value, String.valueOf(codeInRedis))) {
      throw new ValidateCodeException(type+"验证码不正确");
    }
  }

  @Override
  public Boolean validateCode(HttpServletRequest request, HttpServletResponse response) {
    String code = request.getParameter(ParamsConstant.VALIDATE_CODE_CODE);
    if (ObjectUtils.isEmpty(code)) {
      throw new ValidateCodeException("验证码未获取或已过期");
    }
    String key = request.getParameter(ParamsConstant.VALIDATE_SMS_CODE_KEY);
    boolean isSms = false;
    String type="";
    if (StringUtils.isEmpty(key)) {
      key = request.getParameter(ParamsConstant.VALIDATE_USER_NAME);
      isSms = true;
    }
    Object codeInRedis;
    if (isSms) {
      if (!smsSwitchOn) {
        return false;
      }
      type="短信";
      codeInRedis = redisService.get(EpsConstant.SMS_CODE_PREFIX + key);
    } else {
      type="图形";
      codeInRedis = redisService.get(EpsConstant.CODE_PREFIX + key);
    }
    if (StringUtils.isBlank(code)) {
      throw new ValidateCodeException("请输入"+type+"验证码");
    }
    if (codeInRedis == null) {
      throw new ValidateCodeException(type+"验证码未获取或已过期");
    }
    if (!StringUtils.equalsIgnoreCase(code, String.valueOf(codeInRedis))) {
      throw new ValidateCodeException(type+"验证码不正确");
    }
    return true;
  }

  private void createSmsCaptcha(HttpServletRequest request, HttpServletResponse response) {
    String mobile = request.getParameter(ParamsConstant.VALIDATE_CODE_KEY);
//    String code = request.getParameter(ParamsConstant.VALIDATE_CODE_CODE);
//    if (ObjectUtils.isEmpty(code)) {
//      throw new ValidateCodeException("验证码未获取或已过期");
//    }
//    if (code.equals(EpsConstant.DEFAULT_CODE)) {
//      return;
//    }
//    validateCode(request,response);
    if (!EpsUtil.checkMobile(mobile)) {
      throw new ValidateCodeException("手机号码不合法");
    }
    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
    String newDate = format.format(new Date());
    SmsLog phone =
        smsLogMapper.selectOne(
            new QueryWrapper<SmsLog>().eq("phone", mobile).eq("create_time", newDate));
    if (!ObjectUtils.isEmpty(phone)) {
      if (phone.getCountNum().equals(SMS_NUM) || phone.getCountNum() > SMS_NUM) {
        throw new ValidateCodeException("验证码获取次数已达本日上限");
      } else {
        phone.setCountNum(phone.getCountNum() + 1);
        smsLogMapper.updateById(phone);
      }
    } else {
      smsLogMapper.delete(
          new QueryWrapper<SmsLog>().eq("phone", mobile).notIn("create_time", newDate));
      phone = new SmsLog();
      phone.setCreateTime(newDate);
      phone.setCountNum(1);
      phone.setPhone(mobile);
      smsLogMapper.insert(phone);
    }
    if (smsSwitchOn) {
      String captchaCode = (String) redisService.get(EpsConstant.SMS_CODE_PREFIX + mobile);
      if (!StringUtils.isEmpty(captchaCode)) {
        throw new ValidateCodeException("验证码还在有效期,请继续使用上次的验证码");
      } else {
        captchaCode = String.format("%04d", new Random().nextInt(9999));
        redisService.set(
            EpsConstant.SMS_CODE_PREFIX + mobile, captchaCode, properties.getCode().getSmsTime());
        try {
          smsService.sendSms(mobile, SmsType.LOGIN, captchaCode);
        } catch (Exception e) {
          phone.setCountNum(phone.getCountNum() - 1);
          smsLogMapper.updateById(phone);
          log.error("短信发送失败：{}", e.getMessage());
          throw new EpsException("短信发送失败");
        }
      }
    }
  }

  private void createPhotoCaptcha(HttpServletRequest request, HttpServletResponse response)
      throws IOException, ValidateCodeException {
    String key = request.getParameter(ParamsConstant.VALIDATE_CODE_KEY);
    if (StringUtils.isBlank(key)) {
      throw new ValidateCodeException("验证码key不能为空");
    }
    EpsValidateCodeProperties code = properties.getCode();
    setHeader(response, code.getType());

    Captcha captcha = createCaptcha(code);
    redisService.set(
        EpsConstant.CODE_PREFIX + key, StringUtils.lowerCase(captcha.text()), code.getTime());
    captcha.out(response.getOutputStream());
  }

  private Captcha createCaptcha(EpsValidateCodeProperties code) {
    Captcha captcha = null;
    if (StringUtils.equalsIgnoreCase(code.getType(), ImageTypeConstant.GIF)) {
      captcha = new GifCaptcha(code.getWidth(), code.getHeight(), code.getLength());
    } else {
      captcha = new SpecCaptcha(code.getWidth(), code.getHeight(), code.getLength());
    }
    captcha.setCharType(code.getCharType());
    return captcha;
  }

  private void setHeader(HttpServletResponse response, String type) {
    if (StringUtils.equalsIgnoreCase(type, ImageTypeConstant.GIF)) {
      response.setContentType(MediaType.IMAGE_GIF_VALUE);
    } else {
      response.setContentType(MediaType.IMAGE_PNG_VALUE);
    }
    response.setHeader(HttpHeaders.PRAGMA, "No-cache");
    response.setHeader(HttpHeaders.CACHE_CONTROL, "No-cache");
    response.setDateHeader(HttpHeaders.EXPIRES, 0L);
  }
}
