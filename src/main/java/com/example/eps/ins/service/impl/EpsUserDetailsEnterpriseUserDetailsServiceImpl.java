package com.example.eps.ins.service.impl;

import com.example.eps.ins.auth.component.UserDetailTypeEnum;
import com.example.eps.ins.common.entity.enterprise.EnterpriseUser;
import com.example.eps.ins.service.EpsUserDetailsCanProcessor;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 企业端用户获取逻辑.
 */
@Service("enterpriseUserDetailsService")
@RequiredArgsConstructor
public class EpsUserDetailsEnterpriseUserDetailsServiceImpl implements UserDetailsService, EpsUserDetailsCanProcessor {
    private final EnterpriseUserServiceImpl enterpriseUserService;
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        EnterpriseUser enterpriseUser = new EnterpriseUser();
//        enterpriseUser.setCreditCode(username);
        List<EnterpriseUser> list = enterpriseUserService.findEnterpriseUsers(enterpriseUser);
        if(list == null || list.size() == 0){
            throw new UsernameNotFoundException("没有找到企业用户");
        }
//        return User.builder().username(list.get(0).getCreditCode())
//                .password(list.get(0).getPassword()).authorities(AuthorityUtils.NO_AUTHORITIES).build();
//        return User.builder().username(list.get(0).getCreditCode())
//                .password(list.get(0).getPassword()).authorities(AuthorityUtils.NO_AUTHORITIES).build();
        return null;
    }

//    public List<EnterpriseUser> getEnterpriseUserByPhone(String phone){
//        enterpriseUserService.findEnterpriseUsers()
//    }

    @Override
    public boolean canProcessor(UserDetailTypeEnum userDetailTypeEnum) {
        return userDetailTypeEnum == UserDetailTypeEnum.ENTERPRISE_USER;
    }
}
