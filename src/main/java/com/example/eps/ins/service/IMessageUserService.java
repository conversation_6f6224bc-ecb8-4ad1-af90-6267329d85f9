package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.MessageUser;

/**
 * @Description: <消息用户关联表服务类>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
public interface IMessageUserService extends IService<MessageUser> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param dto     MessageUser
     * @return IPage<MessageUser>
     */
    IPage<MessageUser> findMessageUsers(QueryRequest request, MessageUser dto);

    /**
     * 新增
     *
     * @param dto MessageUser
     */
    void createMessageUser(MessageUser dto);

    /**
     * 修改
     *
     * @param dto MessageUser
     */
    void updateMessageUser(MessageUser dto);

    /**
     * 删除
     *
     * @param dto MessageUser
     */
    void deleteMessageUser(MessageUser dto);
}