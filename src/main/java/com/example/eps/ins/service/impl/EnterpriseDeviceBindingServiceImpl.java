package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.dto.device.DeviceBindingRequest;
import com.example.eps.ins.common.dto.device.DeviceBindingResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.device.Device;
import com.example.eps.ins.common.entity.device.EnterpriseDeviceBinding;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.mapper.EnterpriseDeviceBindingMapper;
import com.example.eps.ins.service.IDeviceService;
import com.example.eps.ins.service.IEnterpriseDeviceBindingService;
import com.example.eps.ins.service.IEnterpriseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 企业设备绑定关系 Service实现
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnterpriseDeviceBindingServiceImpl extends ServiceImpl<EnterpriseDeviceBindingMapper, EnterpriseDeviceBinding> 
        implements IEnterpriseDeviceBindingService {

    private final EnterpriseDeviceBindingMapper bindingMapper;
    private final IDeviceService deviceService;
    private final IEnterpriseService enterpriseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EnterpriseDeviceBinding bindDevice(DeviceBindingRequest request, Long creatorId) {
        log.info("开始绑定设备，企业ID: {}, 外部设备ID: {}, 设备名称: {}", 
                request.getEnterpriseId(), request.getExternalDeviceId(), request.getDeviceName());

        // 1. 验证企业是否存在
        if (!validateEnterpriseExists(request.getEnterpriseId())) {
            throw new RuntimeException("企业不存在，企业ID: " + request.getEnterpriseId());
        }

        // 2. 验证外部设备是否存在
        if (!validateExternalDeviceExists(request.getExternalDeviceId())) {
            throw new RuntimeException("外部设备不存在，外部设备ID: " + request.getExternalDeviceId());
        }

        // 3. 检查是否已经绑定
        EnterpriseDeviceBinding existingBinding = findByEnterpriseIdAndExternalDeviceId(
                request.getEnterpriseId(), request.getExternalDeviceId());
        
        if (existingBinding != null) {
            if (existingBinding.getBindingStatus() == 1) {
                throw new RuntimeException("设备已经绑定到该企业");
            } else if (existingBinding.getBindingStatus() == 2) {
                // 重新绑定已解绑的设备
                existingBinding.setDeviceName(request.getDeviceName());
                existingBinding.setDeviceModel(request.getDeviceModel());
                existingBinding.setBindingStatus(1);
                existingBinding.setBindingTime(new Date());
                existingBinding.setUnbindingTime(null);
                existingBinding.setCreateTime(new Date());
                existingBinding.setRemark(request.getRemark());
                this.updateById(existingBinding);
                log.info("重新绑定设备成功，绑定ID: {}", existingBinding.getId());
                return existingBinding;
            }
        }

        // 4. 检查设备是否已被其他企业绑定
        if (isDeviceAlreadyBound(request.getExternalDeviceId(), request.getEnterpriseId())) {
            throw new RuntimeException("设备已被其他企业绑定");
        }

        // 5. 查找对应的设备记录
        Device device = deviceService.findByExternalDeviceId(request.getExternalDeviceId());
        
        // 6. 创建绑定关系
        EnterpriseDeviceBinding binding = new EnterpriseDeviceBinding();
        binding.setEnterpriseId(request.getEnterpriseId());
        binding.setExternalDeviceId(request.getExternalDeviceId());
        binding.setDeviceId(device != null ? device.getId() : null);
        binding.setDeviceName(request.getDeviceName());
        binding.setDeviceModel(request.getDeviceModel());
        binding.setBindingStatus(1); // 已绑定
        binding.setBindingTime(new Date());
        binding.setCreatorId(creatorId);
        binding.setRemark(request.getRemark());

        // 7. 保存绑定关系
        this.save(binding);
        
        log.info("设备绑定成功，绑定ID: {}", binding.getId());
        return binding;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindDevice(Long bindingId) {
        log.info("开始解绑设备，绑定ID: {}", bindingId);

        EnterpriseDeviceBinding binding = this.getById(bindingId);
        if (binding == null) {
            throw new RuntimeException("绑定关系不存在");
        }

        if (binding.getBindingStatus() != 1) {
            throw new RuntimeException("设备未处于绑定状态");
        }

        // 更新绑定状态为已解绑
        int result = bindingMapper.updateBindingStatus(bindingId, 2, new Date());
        
        if (result > 0) {
            log.info("设备解绑成功，绑定ID: {}", bindingId);
            return true;
        } else {
            log.error("设备解绑失败，绑定ID: {}", bindingId);
            return false;
        }
    }

    @Override
    public EnterpriseDeviceBinding findByEnterpriseIdAndExternalDeviceId(Long enterpriseId, Long externalDeviceId) {
        return bindingMapper.selectByEnterpriseIdAndExternalDeviceId(enterpriseId, externalDeviceId);
    }

    @Override
    public List<EnterpriseDeviceBinding> findByEnterpriseId(Long enterpriseId) {
        return bindingMapper.selectByEnterpriseId(enterpriseId);
    }

    @Override
    public List<EnterpriseDeviceBinding> findByExternalDeviceId(Long externalDeviceId) {
        return bindingMapper.selectByExternalDeviceId(externalDeviceId);
    }

    @Override
    public List<EnterpriseDeviceBinding> findByDeviceId(Long deviceId) {
        return bindingMapper.selectByDeviceId(deviceId);
    }

    @Override
    public IPage<DeviceBindingResponse> findBindingsWithDetails(QueryRequest request, EnterpriseDeviceBinding binding) {
        Page<DeviceBindingResponse> page = new Page<>(request.getPageNum(), request.getPageSize());
        return bindingMapper.selectBindingPageWithDetails(page, binding);
    }

    @Override
    public List<EnterpriseDeviceBinding> findByEnterpriseIdAndStatus(Long enterpriseId, Integer bindingStatus) {
        return bindingMapper.selectByEnterpriseIdAndStatus(enterpriseId, bindingStatus);
    }

    @Override
    public List<EnterpriseDeviceBinding> findByBindingStatus(Integer bindingStatus) {
        return bindingMapper.selectByBindingStatus(bindingStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveBindings(List<EnterpriseDeviceBinding> bindingList) {
        if (bindingList == null || bindingList.isEmpty()) {
            return true;
        }
        return bindingMapper.batchInsert(bindingList) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBindingStatus(Long bindingId, Integer bindingStatus) {
        Date unbindingTime = (bindingStatus == 2) ? new Date() : null;
        return bindingMapper.updateBindingStatus(bindingId, bindingStatus, unbindingTime) > 0;
    }

    @Override
    public int countByEnterpriseId(Long enterpriseId) {
        return bindingMapper.countByEnterpriseId(enterpriseId);
    }

    @Override
    public int countByEnterpriseIdAndStatus(Long enterpriseId, Integer bindingStatus) {
        return bindingMapper.countByEnterpriseIdAndStatus(enterpriseId, bindingStatus);
    }

    @Override
    public boolean isDeviceAlreadyBound(Long externalDeviceId, Long excludeEnterpriseId) {
        List<EnterpriseDeviceBinding> bindings = findByExternalDeviceId(externalDeviceId);
        
        return bindings.stream()
                .anyMatch(binding -> binding.getBindingStatus() == 1 && 
                          !binding.getEnterpriseId().equals(excludeEnterpriseId));
    }

    @Override
    public boolean validateEnterpriseExists(Long enterpriseId) {
        try {
            Enterprise enterprise = enterpriseService.getById(enterpriseId);
            return enterprise != null;
        } catch (Exception e) {
            log.error("验证企业存在性失败，企业ID: {}", enterpriseId, e);
            return false;
        }
    }

    @Override
    public boolean validateExternalDeviceExists(Long externalDeviceId) {
        try {
            Device device = deviceService.findByExternalDeviceId(externalDeviceId);
            return device != null;
        } catch (Exception e) {
            log.error("验证外部设备存在性失败，外部设备ID: {}", externalDeviceId, e);
            return false;
        }
    }
}
