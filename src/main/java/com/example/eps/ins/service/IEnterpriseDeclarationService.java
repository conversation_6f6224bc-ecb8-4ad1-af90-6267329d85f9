package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.eps.ins.common.bean.EnterpriseDeclarationReview;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.BasicRisk;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.github.dreamyoung.mprelation.IService;
import com.example.eps.ins.common.bean.EnterpriseDeclarationRequest;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.enterprise.EnterpriseDeclaration;

import java.util.List;

/**
 * 企业信息申报表 Service接口
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
public interface IEnterpriseDeclarationService extends IService<EnterpriseDeclaration> {

    /**
     * 查询当前企业用户的最后一次申报信息（详情）
     *
     * @return EnterpriseDeclaration.
     */
    EnterpriseDeclarationResponse currentUserEnterpriseDeclaration();

    /**
     * 查询当前企业用户的所以申报信息（详情）
     *
     * @return EnterpriseDeclaration.
     */
    List<EnterpriseDeclarationResponse> currentUserEnterpriseDeclarationList();
    /**
     * 查询当前企业用户的最后一次审核通过信息（详情）
     *
     * @return EnterpriseDeclaration.
     */
    EnterpriseDeclarationResponse currentUserEnterpriseDeclarationSecond();
    /**
     * 暂存
     *
     * @param enterpriseDeclaration enterpriseDeclaration
     * @return id
     */
    Long saveEnterpriseDeclaration(EnterpriseDeclarationRequest enterpriseDeclaration);

    /**
     * 新增
     *
     * @param enterpriseDeclaration enterpriseDeclaration
     * @return id
     */
    Long createEnterpriseDeclaration(EnterpriseDeclarationRequest enterpriseDeclaration);

    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param enterpriseDeclaration enterpriseDeclaration
     * @return IPage<EnterpriseDeclaration>
     */
    IPage<EnterpriseDeclaration> findEnterpriseDeclarations(QueryRequest request, EnterpriseDeclaration enterpriseDeclaration);

    /**
     * 查询当前企业用户的所以申报信息（详情）
     *
     * @return EnterpriseDeclaration.
     */
    List<EnterpriseDeclarationResponse> currentUserEnterpriseDeclarationList(Long userId);

    /**
     * 查询（详情）
     *
     * @param id The enterprise declaration ID.
     * @return EnterpriseDeclaration.
     */
    EnterpriseDeclarationResponse queryEnterpriseDeclaration(Long id);

    /**
     * 初审
     *
     * @param review
     */
    void firstReviewEnterpriseDeclaration(EnterpriseDeclarationReview review);

    /**
     * 复审
     *
     * @param review
     */
    void secondReviewEnterpriseDeclaration(EnterpriseDeclarationReview review);

}
