package com.example.eps.ins.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

public interface IAttachmentService {

    /**
     * 保存附件到本地文件中.
     *
     * @param multipartFiles 多个{@link MultipartFile}对象。
     * @return 附件的文件列表.
     */
    List<String> save(List<MultipartFile> multipartFiles);

    /**
     * 附件下载
     *
     * @param filePath
     */
    File download(String filePath);

}
