package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.example.eps.ins.common.config.WebSocket;
import com.example.eps.ins.common.dto.report.SubMessage;
import com.example.eps.ins.service.MsgService;
import com.example.eps.ins.common.utils.RedisUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/17 11:40
 * @Version: 1.0
 */
@Service
public class MsgServiceImpl implements MsgService {
    @Resource
    RedisUtil redisUtil;

    @Override
    public void sendMessage(SubMessage subMessage, WebSocket webSocket) {
        try {
            String s = JSONObject.toJSONString(subMessage);
            webSocket.sendMessageTo(s, subMessage.getReceiveId());
            String key = subMessage.getSendId() + ">" + subMessage.getReceiveId() + "&" + subMessage.getUserStatus();
            //TODO:专家咨询数据库存储已读未读
            redisUtil.listRightPush(key, subMessage);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Override
    public List<SubMessage> getList(SubMessage subMessage) {
        String sendId = subMessage.getSendId();
        Integer enterprise = subMessage.getUserStatus();
        int admin=0;
        if (enterprise.equals(0)){
            admin=1;
        }
        if (!ObjectUtils.isEmpty(subMessage.getStatus())){
            //TODO：
        }
        // 存储发送者消息
        Object sendObj = redisUtil.listRange(sendId+">"+subMessage.getReceiveId()+"&"+ enterprise, 0,
                redisUtil.listLen(sendId+">"+subMessage.getReceiveId()+"&"+ enterprise));
        Object resultObj = redisUtil.listRange(subMessage.getReceiveId()+">"+sendId+"&"+ admin, 0,
                redisUtil.listLen(subMessage.getReceiveId()+">"+sendId+"&"+ admin));
        // 将Object转为List
        List<SubMessage> sendList = castList(sendObj, SubMessage.class);
        List<SubMessage> resultList = castList(resultObj, SubMessage.class);
        sendList.addAll(resultList);
        //根据集合中的时间属性排序
        sendList.sort(Comparator.comparing(SubMessage::getCreateTime));
        return sendList;
    }

    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }

}
