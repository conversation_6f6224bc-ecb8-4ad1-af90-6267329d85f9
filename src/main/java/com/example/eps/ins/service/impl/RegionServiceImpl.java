package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.RegionVO;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.service.RedisService;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.RegionMapper;
import com.example.eps.ins.service.IRegionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.example.eps.ins.common.constant.RedisConstant.REGION_VO;
import static com.example.eps.ins.common.constant.RegionConstant.REGION_LEVEL_AREA;
import static com.example.eps.ins.common.constant.RegionConstant.REGION_LEVEL_TOWN;

/**
 * 地区表 Service实现
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:39
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class RegionServiceImpl extends ServiceImpl<RegionMapper, Region> implements IRegionService {

    private final RegionMapper regionMapper;
    private final RedisService redisService;

    @Override
    public IPage<Region> findRegions(QueryRequest request, Region region) {
        LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
        // TODO 设置查询条件
        Page<Region> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    public List<Region> findRegions(int parentId) {
        LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
        if(parentId!=0){
            queryWrapper.eq(Region::getParentId,parentId);
        }
        queryWrapper.orderByAsc(Region::getParentId).orderByAsc(Region::getId);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public RegionVO getAllRegions() {
        RegionVO regionVO = (RegionVO) redisService.get(REGION_VO);
        if (null == regionVO) {
            LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNotNull(Region::getDept);
            queryWrapper.ne(Region::getDept,"");
            List<Region> regions = this.baseMapper.selectList(queryWrapper);
            Map<Long, List<Region>> regionMap = getRegionMap(regions);
            regionVO = new RegionVO();
            setChildren(regionVO, regionMap);
            redisService.set(REGION_VO, regionVO);
        }
        return regionVO;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRegion(Region region) {
        this.save(region);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRegion(Region region) {
        this.saveOrUpdate(region);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRegion(Region region) {
        LambdaQueryWrapper<Region> wapper = new LambdaQueryWrapper<>();
        // TODO 设置删除条件
        this.remove(wapper);
    }
    

    /**
     * 根据parentId构造地区树
     * @param regionVo 地区
     * @param regionMap key为地区ID，value为子地区列表List
     */
    private void setChildren(RegionVO regionVo, Map<Long, List<Region>> regionMap) {
        if(null == regionVo.getValue()){
            Region rootRegion = regionMap.get(0L).get(0);
            regionVo.setLabel(rootRegion.getName());
            regionVo.setValue(rootRegion.getId());
        }
        List<Region> regions = regionMap.get(regionVo.getValue());
        if (null != regions) {
            List<RegionVO> regionVOS = new ArrayList<>();
            regionVo.setChildren(regionVOS);
            for (Region region : regions) {
                RegionVO childrenRegionVO = new RegionVO();
                childrenRegionVO.setLabel(region.getName());
                childrenRegionVO.setValue(region.getId());
                setChildren(childrenRegionVO, regionMap);
                regionVOS.add(childrenRegionVO);
            }
        }
    }

    @Override
    public RegionVO getAllRegionsFilterDept() {
        RegionVO regionVO =new RegionVO();
        LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(Region::getDept);
        queryWrapper.ne(Region::getDept, "");
        List<Region> regions = this.baseMapper.selectList(queryWrapper);
        Map<Long, List<Region>> regionMap = getRegionMap(regions);
        setChildren(regionVO, regionMap);
        return regionVO;
    }



    @Override
    public RegionVO getCurrentUserRegions() {
        Long regionId = EpsUtil.getCurrentUser().getRegionId();
        Region region = this.baseMapper.selectById(regionId);
        RegionVO regionVO = new RegionVO();
        regionVO.setLabel(region.getName());
        regionVO.setName(region.getDept());
        regionVO.setValue(region.getId());
        if (REGION_LEVEL_TOWN == region.getLevel()) {
            return regionVO;
        }
        List<Region> regions = this.baseMapper.selectList(new QueryWrapper<>());
        Map<Long, List<Region>> regionMap = getRegionMap(regions);
        setChildren(regionVO, regionMap);
        return regionVO;
    }

    @Override
    public RegionVO getCurrentUserForEnterprise() {
        Long regionId = EpsUtil.getCurrentUser().getRegionId();
        Region region = this.baseMapper.selectById(regionId);
        RegionVO regionVO = new RegionVO();
        regionVO.setLabel(region.getName());
        regionVO.setName(region.getDept());
        regionVO.setValue(region.getId());
        if (REGION_LEVEL_TOWN == region.getLevel()) {
//            Region parentRegion = this.baseMapper.selectById(region.getParentId());
//            RegionVO parentRegionVO = new RegionVO();
//            parentRegionVO.setLabel(parentRegion.getName());
//            parentRegionVO.setName(parentRegion.getDept());
//            parentRegionVO.setValue(parentRegion.getId());
//            List<RegionVO> children = new ArrayList<>();
//            children.add(regionVO);
//            parentRegionVO.setChildren(children);
//            return parentRegionVO;
            return regionVO;
        }
        List<Region> regions = this.baseMapper.selectList(new QueryWrapper<>());
        Map<Long, List<Region>> regionMap = getRegionMap(regions);
        setChildren(regionVO, regionMap);
        return regionVO;
    }
    @Override
    public RegionVO currentUserRegion() {
        Long regionId = EpsUtil.getCurrentUser().getRegionId();
        Region region = this.baseMapper.selectById(regionId);
        RegionVO regionVO = new RegionVO();
        regionVO.setLabel(region.getName());
        regionVO.setName(region.getDept());
        regionVO.setValue(region.getId());
        if (REGION_LEVEL_TOWN == region.getLevel()) {
            Region parentRegion = this.baseMapper.selectById(region.getParentId());
            RegionVO parentRegionVO = new RegionVO();
            parentRegionVO.setLabel(parentRegion.getName());
            parentRegionVO.setName(parentRegion.getDept());
            parentRegionVO.setValue(parentRegion.getId());
            List<RegionVO> children = new ArrayList<>();
            children.add(regionVO);
            parentRegionVO.setChildren(children);
            return parentRegionVO;
        }
        List<Region> regions = this.baseMapper.selectList(new QueryWrapper<>());
        Map<Long, List<Region>> regionMap = getRegionMap(regions);
        setChildren(regionVO, regionMap);
        return regionVO;
    }
    @Override
    public RegionVO getCurrentUserRegionsByMessage() {
        Long regionId = EpsUtil.getCurrentUser().getRegionId();
        Region region = this.baseMapper.selectById(regionId);
        RegionVO regionVO = new RegionVO();
        if (REGION_LEVEL_AREA == region.getLevel()) {
            regionVO.setLabel(region.getName());
            regionVO.setName(region.getDept());
            regionVO.setValue(region.getId());
            return regionVO;
        } else if (REGION_LEVEL_TOWN == region.getLevel()) {
            LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Region::getId, region.getParentId());
            region = this.baseMapper.selectOne(queryWrapper);
            regionVO.setLabel(region.getName());
            regionVO.setName(region.getDept());
            regionVO.setValue(region.getId());
            return regionVO;
        } else {
            regionVO.setLabel(region.getName());
            regionVO.setName(region.getDept());
            regionVO.setValue(region.getId());
            LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Region::getLevel, REGION_LEVEL_AREA);
            List<Region> regions = this.baseMapper.selectList(queryWrapper);
            List<RegionVO> regionVOS = new ArrayList<>();
            for (Region r : regions) {
                RegionVO childrenRegionVO = new RegionVO();
                childrenRegionVO.setLabel(r.getName());
                childrenRegionVO.setValue(r.getId());
                childrenRegionVO.setName(r.getDept());
                regionVOS.add(childrenRegionVO);
            }
            regionVO.setChildren(regionVOS);
            return regionVO;
        }
    }


    @Override
    public RegionVO getCurrentUserRegionsByLawEnforcement() {
        Long regionId = EpsUtil.getCurrentUser().getRegionId();
        Region region = this.baseMapper.selectById(regionId);
        RegionVO regionVO = new RegionVO();
        if (REGION_LEVEL_AREA == region.getLevel()) {
            LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Region::getId, region.getParentId());
            Region regionParent = this.baseMapper.selectOne(queryWrapper);
            RegionVO regionParentVO = new RegionVO();

            regionParentVO.setLabel(regionParent.getName());
            regionParentVO.setName(regionParent.getDept());
            regionParentVO.setValue(regionParent.getId());
            regionVO.setLabel(region.getName());
            regionVO.setName(region.getDept());
            regionVO.setValue(region.getId());

            List<RegionVO> regionVOS = new ArrayList<>();
            regionVOS.add(regionVO);
            regionParentVO.setChildren(regionVOS);
            return regionParentVO;
        } else if (REGION_LEVEL_TOWN == region.getLevel()) {
            LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Region::getId, region.getParentId());
            Region regionArea = this.baseMapper.selectOne(queryWrapper);

            LambdaQueryWrapper<Region> queryAreaWrapper = new LambdaQueryWrapper<>();
            queryAreaWrapper.eq(Region::getId, regionArea.getParentId());
            Region regionParent = this.baseMapper.selectOne(queryWrapper);
            RegionVO regionParentVO = new RegionVO();

            regionParentVO.setLabel(regionParent.getName());
            regionParentVO.setName(regionParent.getDept());
            regionParentVO.setValue(regionParent.getId());
            regionVO.setLabel(regionArea.getName());
            regionVO.setName(regionArea.getDept());
            regionVO.setValue(regionArea.getId());

            List<RegionVO> regionVOS = new ArrayList<>();
            regionVOS.add(regionVO);
            regionParentVO.setChildren(regionVOS);
            return regionParentVO;
        } else {
            regionVO.setLabel(region.getName());
            regionVO.setName(region.getDept());
            regionVO.setValue(region.getId());
            LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Region::getLevel, REGION_LEVEL_AREA);
            List<Region> regions = this.baseMapper.selectList(queryWrapper);
            List<RegionVO> regionVOS = new ArrayList<>();
            for (Region r : regions) {
                RegionVO childrenRegionVO = new RegionVO();
                childrenRegionVO.setLabel(r.getName());
                childrenRegionVO.setValue(r.getId());
                childrenRegionVO.setName(r.getDept());
                regionVOS.add(childrenRegionVO);
            }
            regionVO.setChildren(regionVOS);

            return regionVO;
        }
    }

    @Override
    public List<Long> getCurrentUserRegionIds() {
        Long regionId = EpsUtil.getCurrentUser().getRegionId();
        Region region = this.baseMapper.selectById(regionId);
        if (null == region) {
            throw new EpsException("当前用户所属组织不存在");
        }
        return getRegionIds(regionId);
    }

    @Override
    public List<Region> findRegions(Region region) {
        LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>(region);
        List<Region> regions = this.baseMapper.selectList(queryWrapper);
        return regions;
    }

    @Override
    public List<Region> getRegions() {
//        List<Region> regions = (List<Region>) redisService.get(ALL_REGION);
//        if (null == regions) {
//            regions = this.baseMapper.selectList(new QueryWrapper<>());
//            redisService.set(ALL_REGION, regions);
//        }
        return this.baseMapper.selectList(new QueryWrapper<>());
    }

    @Override
    public List<Long> getRegionIds(Long id) {
        List<Region> regions = getRegions();
        Map<Long, List<Region>> regionMap = getRegionMap(regions);
        List<Long> regionIds = new ArrayList<>();
        regionIds.add(id);
        getChildRegionIds(id, regionIds, regionMap);
        return regionIds;
    }

    @Override
    public Map<Long, String> getDeptMap() {
        QueryWrapper<Region> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT dept,id");

        List<Region> regions = this.baseMapper.selectList(queryWrapper);
        Map<Long, String> deptMap = new HashMap<>();
        for (Region region : regions) {
            deptMap.put(region.getId(), region.getDept());
        }
        return deptMap;
    }

    /**
     * 获取子地区ID列表
     *
     * @param id
     * @param regionIds
     * @param regionMap
     * @return
     */
    private void getChildRegionIds(Long id, List<Long> regionIds, Map<Long, List<Region>> regionMap) {
        if (regionMap.containsKey(id)) {
            List<Region> regions = regionMap.get(id);
            for (Region region : regions) {
                regionIds.add(region.getId());
                getChildRegionIds(region.getId(), regionIds, regionMap);
            }
        }
    }

    /**
     * 遍历地区列表 存放key为地区ID，value为子地区列表List，供生成地区树使用
     *
     * @param regions
     * @return
     */
    private Map<Long, List<Region>> getRegionMap(List<Region> regions) {
        Map<Long, List<Region>> regionMap = new HashMap();
        for (Region region : regions) {
            List<Region> regionList = regionMap.get(region.getParentId());
            if (null == regionList) {
                regionList = new ArrayList<>();
            }
            regionList.add(region);
            regionMap.put(region.getParentId(), regionList);
        }
        return regionMap;
    }
    
}
