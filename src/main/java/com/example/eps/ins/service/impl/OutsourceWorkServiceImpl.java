package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.OutsourceWork;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.OutsourceWorkMapper;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.OutsourceWorkService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/9 13:38
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class OutsourceWorkServiceImpl extends ServiceImpl<OutsourceWorkMapper,OutsourceWork>
        implements OutsourceWorkService {

    private final OutsourceWorkMapper outsourceWorkMapper;
    private final FdfsClientService fdfsClientService;
    private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

    @Override
    public IPage<OutsourceWork> findOutsourceWork(QueryRequest request, OutsourceWork outsourceWork) {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        if (!ObjectUtils.isEmpty(outsourceWork.getCreatorId())){
            userId=outsourceWork.getCreatorId();
        }
        LambdaQueryWrapper<OutsourceWork> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OutsourceWork::getCreatorId, userId);
        if (!ObjectUtils.isEmpty(outsourceWork.getWorkType())) {
            queryWrapper.eq(OutsourceWork::getWorkType, outsourceWork.getWorkType());
        }
        queryWrapper.orderByDesc(OutsourceWork::getCreateTime);
        Page<OutsourceWork> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createOutsourceWork(OutsourceWork outsourceWork) {
        //获取所有的企业申报信息
        List<EnterpriseDeclarationResponse> enterprise =
                enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
        //获取所有申报状态
        List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(enterprise)){
            throw new EpsException("请完善企业信息后创建企业外委作业信息！");
        }
        if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
            throw new EpsException("企业信息暂未审核通过，请审核通过后创建外委作业信息！");
        }
        if (ObjectUtils.isEmpty(outsourceWork.getWorkFile())){
            outsourceWork.setWorkFile("[]");
        }
        outsourceWork.setCreateTime(new Date());
        if (ObjectUtils.isEmpty(outsourceWork.getId())) {
            CurrentUser currentUser = EpsUtil.getCurrentUser();
            outsourceWork.setCreatorId(currentUser.getUserId());
            outsourceWork.setRegionId(enterprise.get(0).getRegionTownId());
            this.save(outsourceWork);
        } else {
            outsourceWork.setCreatorId(outsourceWork.getCreatorId());
            this.update(outsourceWork, new QueryWrapper<OutsourceWork>()
                    .eq("id", outsourceWork.getId()));
        }
        return "";
    }

    @Override
    public OutsourceWork findOutsourceWork(Long id) {
        return outsourceWorkMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOutsourceWork(Long id) {
        LambdaQueryWrapper<OutsourceWork> userWarpper = new LambdaQueryWrapper<>();
        userWarpper.eq(OutsourceWork::getId, id);
        OutsourceWork outsourceWork = this.getById(id);
        if (outsourceWork == null) {
            return;
        }
        JSONArray jsonArray = JSON.parseArray(outsourceWork.getWorkFile());
        outsourceWorkMapper.delete(userWarpper);
        for (Object obj : jsonArray) {
            fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(Long id, String filePath) {
        if (!ObjectUtils.isEmpty(id)) {
            OutsourceWork outsourceWork = outsourceWorkMapper.selectById(id);
            if (ObjectUtils.isEmpty(outsourceWork)) {
                return;
            }
            JSONArray jsonArray = JSON.parseArray(outsourceWork.getWorkFile());
            JSONArray json = new JSONArray();
            for (Object obj : jsonArray) {
                String filePath1 = ((JSONObject) obj).getString("filePath");
                if (filePath1.equals(filePath)) {
                    continue;
                }
                Map<String, Object> map = new HashMap<>();
                map.put("fileUrl", ((JSONObject) obj).getString("fileUrl"));
                map.put("filePath", ((JSONObject) obj).getString("filePath"));
                map.put("fileName", ((JSONObject) obj).getString("fileName"));
                json.add(EpsUtil.toJsonObj(map));
            }
            outsourceWork.setWorkFile(json.toString());
            outsourceWorkMapper.updateById(outsourceWork);
        }
        fdfsClientService.deleteFile(filePath);
    }
}
