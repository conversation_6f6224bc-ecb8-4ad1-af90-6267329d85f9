package com.example.eps.ins.service.impl;

import com.example.eps.ins.common.entity.safety.SafetyMonitorEvent;
import com.example.eps.ins.service.AlarmNotificationService;
import com.example.eps.ins.service.IMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;

/**
 * 告警通知服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlarmNotificationServiceImpl implements AlarmNotificationService {

    private final IMessageService messageService;

    @Override
    public boolean sendSafetyHelmetAlarm(SafetyMonitorEvent event) {
        try {
            String title = "🚨 安全帽违规告警";
            String content = buildSafetyHelmetAlarmContent(event);
            
            log.warn("发送安全帽违规告警: 设备={}, 摄像头={}, 告警次数={}", 
                    event.getDeviceSerial(), event.getCameraName(), event.getAlarmTimes());
            
            // 发送系统消息通知（这里需要根据实际业务确定接收人）
            // TODO: 根据设备所属区域或企业确定通知对象
            // sendSystemMessage(userId, title, content);
            
            // 发送邮件通知
            // TODO: 根据配置获取邮件接收人
            // sendEmailNotification(email, title, content);
            
            // 发送短信通知
            // TODO: 根据配置获取短信接收人
            // sendSmsNotification(phone, buildSmsContent(event));
            
            log.info("安全帽违规告警通知发送完成: {}", event.getExternalEventId());
            return true;
            
        } catch (Exception e) {
            log.error("发送安全帽违规告警失败: {}", event.getExternalEventId(), e);
            return false;
        }
    }

    @Override
    public boolean sendReflectiveVestAlarm(SafetyMonitorEvent event) {
        try {
            String title = "🚨 反光衣违规告警";
            String content = buildReflectiveVestAlarmContent(event);
            
            log.warn("发送反光衣违规告警: 设备={}, 摄像头={}, 告警次数={}", 
                    event.getDeviceSerial(), event.getCameraName(), event.getAlarmTimes());
            
            // TODO: 实现具体的通知逻辑
            
            log.info("反光衣违规告警通知发送完成: {}", event.getExternalEventId());
            return true;
            
        } catch (Exception e) {
            log.error("发送反光衣违规告警失败: {}", event.getExternalEventId(), e);
            return false;
        }
    }

    @Override
    public boolean sendPersonIntrusionAlarm(SafetyMonitorEvent event) {
        try {
            String title = "🚨 人员入侵告警";
            String content = buildPersonIntrusionAlarmContent(event);
            
            log.warn("发送人员入侵告警: 设备={}, 摄像头={}, 告警次数={}", 
                    event.getDeviceSerial(), event.getCameraName(), event.getAlarmTimes());
            
            // TODO: 实现具体的通知逻辑
            
            log.info("人员入侵告警通知发送完成: {}", event.getExternalEventId());
            return true;
            
        } catch (Exception e) {
            log.error("发送人员入侵告警失败: {}", event.getExternalEventId(), e);
            return false;
        }
    }

    @Override
    public boolean sendGenericSafetyAlarm(SafetyMonitorEvent event) {
        try {
            String title = "🚨 安全监控告警";
            String content = buildGenericSafetyAlarmContent(event);
            
            log.warn("发送通用安全告警: 设备={}, 事件类型={}, 告警次数={}", 
                    event.getDeviceSerial(), event.getEventTypeName(), event.getAlarmTimes());
            
            // TODO: 实现具体的通知逻辑
            
            log.info("通用安全告警通知发送完成: {}", event.getExternalEventId());
            return true;
            
        } catch (Exception e) {
            log.error("发送通用安全告警失败: {}", event.getExternalEventId(), e);
            return false;
        }
    }

    @Override
    public boolean sendEmailNotification(String to, String subject, String content) {
        try {
            // TODO: 实现邮件发送逻辑
            // 可以使用Spring Mail或其他邮件服务
            log.info("发送邮件通知: to={}, subject={}", to, subject);
            return true;
        } catch (Exception e) {
            log.error("发送邮件通知失败: to={}, subject={}", to, subject, e);
            return false;
        }
    }

    @Override
    public boolean sendSmsNotification(String phone, String message) {
        try {
            // TODO: 实现短信发送逻辑
            // 可以集成阿里云短信、腾讯云短信等服务
            log.info("发送短信通知: phone={}, message={}", phone, message);
            return true;
        } catch (Exception e) {
            log.error("发送短信通知失败: phone={}, message={}", phone, message, e);
            return false;
        }
    }

    @Override
    public boolean sendSystemMessage(Long userId, String title, String content) {
        try {
            // TODO: 使用现有的消息服务发送系统内部消息
            // messageService.sendMessage(userId, title, content);
            log.info("发送系统消息: userId={}, title={}", userId, title);
            return true;
        } catch (Exception e) {
            log.error("发送系统消息失败: userId={}, title={}", userId, title, e);
            return false;
        }
    }

    /**
     * 构建安全帽违规告警内容
     */
    private String buildSafetyHelmetAlarmContent(SafetyMonitorEvent event) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        StringBuilder content = new StringBuilder();
        content.append("=== 安全帽违规告警 ===\n");
        content.append("事件ID: ").append(event.getExternalEventId()).append("\n");
        content.append("告警时间: ").append(sdf.format(event.getCreateTime())).append("\n");
        content.append("设备信息: ").append(event.getDeviceSerial()).append(" (").append(event.getDeviceIp()).append(")\n");
        content.append("摄像头: ").append(event.getCameraName() != null ? event.getCameraName() : event.getCameraId()).append("\n");
        content.append("违规对象: ").append(event.getObjectName() != null ? event.getObjectName() : "未戴安全帽").append("\n");
        content.append("检测置信度: ").append(event.getDetectionScore() != null ? String.format("%.2f%%", event.getDetectionScore() * 100) : "N/A").append("\n");
        content.append("累计告警次数: ").append(event.getAlarmTimes() != null ? event.getAlarmTimes() : 0).append("\n");
        
        if (event.getStartTime() != null) {
            content.append("事件开始时间: ").append(sdf.format(event.getStartTime())).append("\n");
        }
        if (event.getEndTime() != null) {
            content.append("事件结束时间: ").append(sdf.format(event.getEndTime())).append("\n");
        }
        if (event.getImgUrl() != null) {
            content.append("图片地址: ").append(event.getImgUrl()).append("\n");
        }
        
        content.append("\n请及时处理该违规行为，确保作业人员安全！");
        
        return content.toString();
    }

    /**
     * 构建反光衣违规告警内容
     */
    private String buildReflectiveVestAlarmContent(SafetyMonitorEvent event) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        StringBuilder content = new StringBuilder();
        content.append("=== 反光衣违规告警 ===\n");
        content.append("事件ID: ").append(event.getExternalEventId()).append("\n");
        content.append("告警时间: ").append(sdf.format(event.getCreateTime())).append("\n");
        content.append("设备信息: ").append(event.getDeviceSerial()).append(" (").append(event.getDeviceIp()).append(")\n");
        content.append("摄像头: ").append(event.getCameraName() != null ? event.getCameraName() : event.getCameraId()).append("\n");
        content.append("违规对象: ").append(event.getObjectName() != null ? event.getObjectName() : "未穿反光衣").append("\n");
        content.append("检测置信度: ").append(event.getDetectionScore() != null ? String.format("%.2f%%", event.getDetectionScore() * 100) : "N/A").append("\n");
        content.append("累计告警次数: ").append(event.getAlarmTimes() != null ? event.getAlarmTimes() : 0).append("\n");
        content.append("\n请及时处理该违规行为，确保作业人员安全！");
        
        return content.toString();
    }

    /**
     * 构建人员入侵告警内容
     */
    private String buildPersonIntrusionAlarmContent(SafetyMonitorEvent event) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        StringBuilder content = new StringBuilder();
        content.append("=== 人员入侵告警 ===\n");
        content.append("事件ID: ").append(event.getExternalEventId()).append("\n");
        content.append("告警时间: ").append(sdf.format(event.getCreateTime())).append("\n");
        content.append("设备信息: ").append(event.getDeviceSerial()).append(" (").append(event.getDeviceIp()).append(")\n");
        content.append("摄像头: ").append(event.getCameraName() != null ? event.getCameraName() : event.getCameraId()).append("\n");
        content.append("入侵区域: ").append(event.getObjectName() != null ? event.getObjectName() : "禁入区域").append("\n");
        content.append("检测置信度: ").append(event.getDetectionScore() != null ? String.format("%.2f%%", event.getDetectionScore() * 100) : "N/A").append("\n");
        content.append("累计告警次数: ").append(event.getAlarmTimes() != null ? event.getAlarmTimes() : 0).append("\n");
        content.append("\n请立即处理该入侵行为，确保区域安全！");
        
        return content.toString();
    }

    /**
     * 构建通用安全告警内容
     */
    private String buildGenericSafetyAlarmContent(SafetyMonitorEvent event) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        StringBuilder content = new StringBuilder();
        content.append("=== 安全监控告警 ===\n");
        content.append("事件ID: ").append(event.getExternalEventId()).append("\n");
        content.append("事件类型: ").append(event.getEventTypeName()).append("\n");
        content.append("告警时间: ").append(sdf.format(event.getCreateTime())).append("\n");
        content.append("设备信息: ").append(event.getDeviceSerial()).append(" (").append(event.getDeviceIp()).append(")\n");
        content.append("摄像头: ").append(event.getCameraName() != null ? event.getCameraName() : event.getCameraId()).append("\n");
        content.append("检测对象: ").append(event.getObjectName() != null ? event.getObjectName() : "未知").append("\n");
        content.append("检测置信度: ").append(event.getDetectionScore() != null ? String.format("%.2f%%", event.getDetectionScore() * 100) : "N/A").append("\n");
        content.append("累计告警次数: ").append(event.getAlarmTimes() != null ? event.getAlarmTimes() : 0).append("\n");
        content.append("\n请及时处理该安全事件！");
        
        return content.toString();
    }

    /**
     * 构建短信内容
     */
    private String buildSmsContent(SafetyMonitorEvent event) {
        return String.format("【安全监控】%s违规告警！设备:%s，摄像头:%s，累计告警%d次。请及时处理！", 
                event.getEventTypeName(),
                event.getDeviceSerial(),
                event.getCameraName() != null ? event.getCameraName() : event.getCameraId(),
                event.getAlarmTimes() != null ? event.getAlarmTimes() : 0);
    }
}
