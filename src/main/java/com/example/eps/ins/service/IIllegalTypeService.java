package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.IllegalType;

import java.util.List;

/**
 * 风险类型 Service接口
 *
 * <AUTHOR>
 * @date 2022-01-24 15:39:12
 */
public interface IIllegalTypeService extends IService<IllegalType> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param illegalType illegalType
     * @return IPage<IllegalType>
     */
    List<IllegalType> findIllegalTypes(QueryRequest request, IllegalType illegalType);

    /**
     * 查询（所有）
     *
     * @param illegalType illegalType
     * @return List<IllegalType>
     */
    List<IllegalType> findIllegalTypes(IllegalType illegalType);

    /**
     * 查询检查类型（所有）
     *
     * @param illegalType illegalType
     * @return List<IllegalType>
     */
    List<IllegalType> findIllegalCategory(IllegalType illegalType);

    /**
     * 新增
     *
     * @param illegalType illegalType
     */
    void createIllegalType(IllegalType illegalType);

    /**
     * 修改
     *
     * @param illegalType illegalType
     */
    void updateIllegalType(IllegalType illegalType);

    /**
     * 删除
     *
     * @param illegalType illegalType
     */
    void deleteIllegalType(IllegalType illegalType);
}
