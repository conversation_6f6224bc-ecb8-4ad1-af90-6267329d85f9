package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Risk;
import com.example.eps.ins.mapper.RiskMapper;
import com.example.eps.ins.service.IRiskService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 风险类型表 Service实现
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class RiskServiceImpl extends ServiceImpl<RiskMapper, Risk> implements IRiskService {

    private final RiskMapper riskMapper;

    @Override
    public IPage<Risk> findRisks(QueryRequest request, Risk risk) {
        LambdaQueryWrapper<Risk> queryWrapper = new LambdaQueryWrapper<>();
        // TODO 设置查询条件
        Page<Risk> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }
    @Override
    public List<Risk> findRisksSystem(Long parentId, boolean queryAll) {
        LambdaQueryWrapper<Risk> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(Risk::getParentId);
        if (queryAll) {
            List<Risk> risks = this.baseMapper.selectList(queryWrapper);
            return findAllChild(parentId, risks);
        } else {
            queryWrapper.eq(Risk::getParentId, parentId);
            return this.baseMapper.selectList(queryWrapper);
        }
    }
    private List<Risk> findAllChild(Long parentId, List<Risk> risks) {
        List<Risk> riskList = new ArrayList<>();
        for (Risk risk : risks) {
            if (risk.getParentId() == parentId) {
                riskList.add(risk);
                riskList.addAll(findAllChild(risk.getId(), risks));
            }
        }
        return riskList;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRisk(Risk risk) {
        this.save(risk);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRisk(Risk risk) {
        this.saveOrUpdate(risk);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRisk(Risk risk) {
        LambdaQueryWrapper<Risk> wapper = new LambdaQueryWrapper<>();
        // TODO 设置删除条件
        this.remove(wapper);
    }
}
