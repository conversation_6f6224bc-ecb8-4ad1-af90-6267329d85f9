package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.bean.MessageRequest;
import com.example.eps.ins.common.dto.report.model.MessageVo;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.Message;
import com.example.eps.ins.common.entity.system.MessageUser;

import java.util.List;
import java.util.Map;

/**
 * @Description: <消息通知表服务类>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
public interface IMessageService extends IService<Message> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param dto     Message
     * @return IPage<Message>
     */
    IPage<Message> findMessages(QueryRequest request, Message dto);

    IPage<Message> findMessagesByCurrentUser(MessageRequest request);

    Message findMessage(long id);
    /**
     * 未读消息
     * @param messageUser MessageUser
     * @return IPage<MessageUser>
     */
    IPage<MessageUser> findNoSee(QueryRequest request, MessageUser messageUser);

    Map<String,List<Message>> findMessageByCurrentUser();
    /**
     * 新增
     *
     * @param dto Message
     */
    String createMessage(MessageVo dto);

    /**
     * 修改
     *
     * @param dto Message
     */
    void updateMessage(Message dto);

    /**
     * 删除
     *
     */
    void deleteMessage(Long id);
    /**
     * 删除文件
     * @param id Long
     * @param filePath String
     */
    void deleteFile(Long id, String filePath);

    /**
     * 门户首页预警
     * @return List<WarningForCard>
     */
    List<WarningForCard> earlyWarningByUser();

    /**
     * 门户首页警告
     * @return List<WarningForCard>
     */
    List<WarningForCard> warningByUser();

}