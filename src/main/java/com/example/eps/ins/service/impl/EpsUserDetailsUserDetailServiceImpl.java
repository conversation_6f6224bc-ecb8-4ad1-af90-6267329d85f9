package com.example.eps.ins.service.impl;

import com.example.eps.ins.auth.component.UserDetailTypeEnum;
import com.example.eps.ins.common.constant.ParamsConstant;
import com.example.eps.ins.common.constant.SocialConstant;
import com.example.eps.ins.common.entity.EpsAuthUser;
import com.example.eps.ins.common.entity.system.SystemUser;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.controller.auth.manager.UserManager;
import com.example.eps.ins.mapper.UserMapper;
import com.example.eps.ins.service.EpsUserDetailsCanProcessor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service("adminUserDetailService")
@Primary
@RequiredArgsConstructor
public class EpsUserDetailsUserDetailServiceImpl implements UserDetailsService, EpsUserDetailsCanProcessor {

    private final PasswordEncoder passwordEncoder;
    private final UserManager userManager;
    private final UserMapper userMapper;
    protected final Log logger = LogFactory.getLog(getClass());

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        HttpServletRequest httpServletRequest = EpsUtil.getHttpServletRequest();
        SystemUser systemUser = userManager.findByName(username);
        if (systemUser != null) {
            String pwd = systemUser.getPassword();
            String loginType = (String) httpServletRequest.getAttribute(ParamsConstant.LOGIN_TYPE);
            if (StringUtils.equals(loginType, SocialConstant.SOCIAL_LOGIN)) {
                pwd = passwordEncoder.encode(SocialConstant.getSocialLoginPassword());
            }

            boolean notLocked = false;
            if (StringUtils.equals(SystemUser.STATUS_VALID, systemUser.getStatus()+"")) {
                notLocked = true;
            }

            String permissions = userManager.findUserPermissions(systemUser.getUsername());
            List<GrantedAuthority> grantedAuthorities = AuthorityUtils.NO_AUTHORITIES;
            if (StringUtils.isNotBlank(permissions)) {
                grantedAuthorities = AuthorityUtils.commaSeparatedStringToAuthorityList(permissions);
            }
            systemUser.setLastLoginTime(new Date());
            userMapper.updateById(systemUser);
            EpsAuthUser authUser = new EpsAuthUser(systemUser.getUsername(), pwd, true,
                    true, true, notLocked, grantedAuthorities);
            BeanUtils.copyProperties(systemUser, authUser);
            authUser.setDeptName(systemUser.getDept());
            return authUser;
        } else {
            throw new UsernameNotFoundException("");
        }
    }

    @Override
    public boolean canProcessor(UserDetailTypeEnum userDetailTypeEnum) {
        return userDetailTypeEnum == UserDetailTypeEnum.CONSOLE;
    }

}
