package com.example.eps.ins.service;

import com.aliyuncs.exceptions.ClientException;
import com.example.eps.ins.auth.enums.SmsType;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.properties.EpsSmsProperties;
import com.example.eps.ins.common.properties.SmsTemplate;
import com.example.eps.ins.common.utils.HttpClientUtil;
import com.example.eps.ins.common.utils.MD5;
import com.example.eps.ins.common.utils.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmsService {

    @Resource
    EpsSmsProperties epsSmsProperties;

    public void sendSms(String telephone, SmsType smsType, String content) throws Exception {
        SmsTemplate smsTemplate = null;
        if (smsType == SmsType.REGISTER) {
            smsTemplate = epsSmsProperties.getRegister();
        } else if (smsType == SmsType.LOGIN) {
            smsTemplate = epsSmsProperties.getLogin();
        } else if (smsType == SmsType.RESET_PASSWORD) {
            smsTemplate = epsSmsProperties.getResetPassword();
        }
        sendSms(telephone, String.format(smsTemplate.getTemplate(), content));
    }

    /**
     * 发送手机验证码
     *
     * @param telephone
     * @return
     * @throws ClientException
     */
    public void sendSms(String telephone, String content) throws Exception {
        Map params = new HashMap<String, String>();
        String timestamp = Long.toString(System.currentTimeMillis());
        params.put("account", epsSmsProperties.getAccount());
        params.put("timestamp", Long.toString(System.currentTimeMillis()));
        params.put("access_token", MD5.md5(timestamp + epsSmsProperties.getPassword()));
        params.put("receiver", telephone);
        params.put("smscontent", java.net.URLEncoder.encode(content, "UTF-8"));
        params.put("extcode", "");
        Response response = HttpClientUtil.doPostStream(epsSmsProperties.getUrl(), HttpClientUtil.getUrlParamsByMap(params));
        if (200 == response.getStatusCode()) {
            log.info("短信发送成功：{}，接收人{}，短信内容{}", response.getResult(), telephone, content);
        } else {
            log.error("短信发送失败：{}，接收人{}，短信内容{}", response.getResult(), telephone, content);
            throw new EpsException("短信发送失败");
        }
    }

}