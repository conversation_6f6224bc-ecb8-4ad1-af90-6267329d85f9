package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EnterpriseRisk;

import java.util.List;

/**
 * 企业风险表 Service接口
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:43
 */
public interface IEnterpriseRiskService extends IService<EnterpriseRisk> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param enterpriseRisk enterpriseRisk
     * @return IPage<EnterpriseRisk>
     */
    IPage<EnterpriseRisk> findEnterpriseRisks(QueryRequest request, EnterpriseRisk enterpriseRisk);

    /**
     * 查询（所有）
     *
     * @param enterpriseRisk enterpriseRisk
     * @return List<EnterpriseRisk>
     */
    List<EnterpriseRisk> findEnterpriseRisks(EnterpriseRisk enterpriseRisk);

    /**
     * 新增
     *
     * @param enterpriseRisk enterpriseRisk
     */
    void createEnterpriseRisk(EnterpriseRisk enterpriseRisk);

    /**
     * 修改
     *
     * @param enterpriseRisk enterpriseRisk
     */
    void updateEnterpriseRisk(EnterpriseRisk enterpriseRisk);

    /**
     * 删除
     *
     * @param enterpriseRisk enterpriseRisk
     */
    void deleteEnterpriseRisk(EnterpriseRisk enterpriseRisk);
}
