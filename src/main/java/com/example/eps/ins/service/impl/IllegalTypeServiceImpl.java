package com.example.eps.ins.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.IllegalTypeVO;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.IllegalType;
import com.example.eps.ins.common.utils.DataTransfer;
import com.example.eps.ins.mapper.IllegalTypeMapper;
import com.example.eps.ins.service.IIllegalTypeService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 风险类型 Service实现
 *
 * <AUTHOR>
 * @date 2022-01-24 15:39:12
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class IllegalTypeServiceImpl extends ServiceImpl<IllegalTypeMapper, IllegalType> implements IIllegalTypeService {

    private final IllegalTypeMapper illegalTypeMapper;

    @Override
    public List<IllegalType> findIllegalTypes(QueryRequest request, IllegalType illegalType) {
        LambdaQueryWrapper<IllegalType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(IllegalType::getParentId);
        // TODO 设置查询条件
        List<IllegalType> illegalTypes = this.baseMapper.selectList(queryWrapper);
        List records = DataTransfer.transferList(illegalTypes, IllegalTypeVO.class);
        setChildren(records);
        return records;
    }

    private void setChildren(List<IllegalTypeVO> records){
        if(null != records && !records.isEmpty()){
            for (IllegalTypeVO record : records){
                LambdaQueryWrapper<IllegalType> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(IllegalType::getParentId, record.getId());
                List<IllegalType> children = this.baseMapper.selectList(queryWrapper);
                record.setChildren(DataTransfer.transferList(children,IllegalTypeVO.class));
            }
        }
    }

    @Override
    public List<IllegalType> findIllegalTypes(IllegalType illegalType) {
        LambdaQueryWrapper<IllegalType> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(illegalType.getName())) {
            queryWrapper.eq(IllegalType::getName, illegalType.getName());
        }
        if (ObjectUtils.isNotEmpty(illegalType.getId())){
            queryWrapper.eq(IllegalType::getParentId,illegalType.getId());
        }
        queryWrapper.isNotNull(IllegalType::getParentId);
        return this.baseMapper.selectList(queryWrapper);
    }
    @Override
    public List<IllegalType> findIllegalCategory(IllegalType illegalType) {
        LambdaQueryWrapper<IllegalType> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(illegalType.getName())) {
            queryWrapper.eq(IllegalType::getName, illegalType.getName());
        }
        queryWrapper.isNull(IllegalType::getParentId);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createIllegalType(IllegalType illegalType) {
        this.save(illegalType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIllegalType(IllegalType illegalType) {
        this.saveOrUpdate(illegalType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIllegalType(IllegalType illegalType) {
        this.removeById(illegalType.getId());
    }
}
