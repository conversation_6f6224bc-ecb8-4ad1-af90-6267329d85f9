package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.mapper.MessageUserMapper;
import com.example.eps.ins.service.IMessageUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: <消息用户关联表服务实现类>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class MessageUserServiceImpl extends ServiceImpl<MessageUserMapper, MessageUser> implements IMessageUserService {

    private final MessageUserMapper iMessageUserMapper;

    @Override
    public IPage<MessageUser> findMessageUsers(QueryRequest request, MessageUser dto) {
        LambdaQueryWrapper<MessageUser> queryWrapper = new LambdaQueryWrapper<>();
        // TODO 设置查询条件
        Page<MessageUser> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMessageUser(MessageUser dto) {
        this.save(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMessageUser(MessageUser dto) {
        this.saveOrUpdate(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMessageUser(MessageUser dto) {
        LambdaQueryWrapper<MessageUser> wapper = new LambdaQueryWrapper<>();
        // TODO 设置删除条件
        this.remove(wapper);
    }
}