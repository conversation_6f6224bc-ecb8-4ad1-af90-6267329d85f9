package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.MessageRegion;

/**
 * @Description: <消息地区表服务类>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
public interface IMessageRegionService extends IService<MessageRegion> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param dto     MessageRegion
     * @return IPage<MessageRegion>
     */
    IPage<MessageRegion> findMessageRegions(QueryRequest request, MessageRegion dto);

    /**
     * 新增
     *
     * @param dto MessageRegion
     */
    void createMessageRegion(MessageRegion dto);

    /**
     * 修改
     *
     * @param dto MessageRegion
     */
    void updateMessageRegion(MessageRegion dto);

    /**
     * 删除
     *
     * @param dto MessageRegion
     */
    void deleteMessageRegion(MessageRegion dto);
}