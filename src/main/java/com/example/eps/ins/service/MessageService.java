package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.Message;

import java.util.List;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2022/10/31 16:58
 * @Version: 1.0
 */
public interface MessageService extends IService<Message> {

    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param dto     Message
     * @return IPage<Message>
     */
    IPage<Message> findMessages(QueryRequest request, Message dto);
    Message findMessage(long id);

    /**
     * 门户首页预警
     * @return
     */
    List<WarningForCard> earlyWarningByUser();

    /**
     * 门户首页警告
     * @return
     */
    List<WarningForCard> warningByUser();

    /**
     * 未读消息
     * @return
     */
    Integer findNoSee();
}
