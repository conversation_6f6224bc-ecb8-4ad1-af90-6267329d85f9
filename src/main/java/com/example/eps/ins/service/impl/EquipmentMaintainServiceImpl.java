package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EquipmentMaintain;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.EquipmentMaintainMapper;
import com.example.eps.ins.service.EquipmentMaintainService;
import com.example.eps.ins.service.FdfsClientService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/** @Author: Zhanghongyin @Date: Created in 2022/10/11 9:52 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class EquipmentMaintainServiceImpl extends ServiceImpl<EquipmentMaintainMapper, EquipmentMaintain>
    implements EquipmentMaintainService {

  private final FdfsClientService fdfsClientService;
  private final EquipmentMaintainMapper emergencyPlanMapper;
  private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

  @Override
  public IPage<EquipmentMaintain> findEquipmentMaintains(
      QueryRequest request, EquipmentMaintain equipmentMaintain) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    if (!ObjectUtils.isEmpty(equipmentMaintain.getCreatorId())){
      userId=equipmentMaintain.getCreatorId();
    }
    LambdaQueryWrapper<EquipmentMaintain> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(EquipmentMaintain::getCreatorId, userId);
    if (!ObjectUtils.isEmpty(equipmentMaintain.getEquipmentName())) {
      queryWrapper.like(EquipmentMaintain::getEquipmentName, equipmentMaintain.getEquipmentName());
    }
    queryWrapper.orderByDesc(EquipmentMaintain::getCreateTime);
    Page<EquipmentMaintain> page = new Page<>(request.getPageNum(), request.getPageSize());
    return this.page(page, queryWrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String createEquipmentMaintain(EquipmentMaintain equipmentMaintain) {
    List<EnterpriseDeclarationResponse> enterprise =
            enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
    List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
    if (ObjectUtils.isEmpty(enterprise)){
      throw new EpsException("请完善企业信息后创建企业设施设备维护保养记录！");
    }
    if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
      throw new EpsException("企业信息暂未审核通过，请审核通过后创建企业设施设备维护保养记录！");
    }
    // 保存消息
    EquipmentMaintain equipment = new EquipmentMaintain();
    equipment.setEquipmentName(equipmentMaintain.getEquipmentName());
    equipment.setEquipmentModel(equipmentMaintain.getEquipmentModel());
    equipment.setEquipmentAddress(equipmentMaintain.getEquipmentAddress());
    equipment.setMaintenanceDate(equipmentMaintain.getMaintenanceDate());
    equipment.setMaintenanceDetails(equipmentMaintain.getMaintenanceDetails());
    equipment.setMaintenanceCompany(equipmentMaintain.getMaintenanceCompany());
    equipment.setMaintenancePeople(equipmentMaintain.getMaintenancePeople());
    equipment.setMaintenanceType(equipmentMaintain.getMaintenanceType());
    if (ObjectUtils.isEmpty(equipmentMaintain.getMaintenanceImage())){
      equipment.setMaintenanceImage("[]");
    } else {
      equipment.setMaintenanceImage(equipmentMaintain.getMaintenanceImage());
    }
    if (ObjectUtils.isEmpty(equipmentMaintain.getMaintenanceFile())){
      equipment.setMaintenanceFile("[]");
    } else {
      equipment.setMaintenanceFile(equipmentMaintain.getMaintenanceFile());
    }
    equipment.setCreateTime(new Date());
    if (ObjectUtils.isEmpty(equipmentMaintain.getId())) {
      CurrentUser currentUser = EpsUtil.getCurrentUser();
      equipment.setCreatorId(currentUser.getUserId());
      equipment.setRegionId(enterprise.get(0).getRegionTownId());
      this.save(equipment);
    } else {
      equipment.setCreatorId(equipmentMaintain.getCreatorId());
      this.update(equipment, new QueryWrapper<EquipmentMaintain>()
              .eq("id", equipmentMaintain.getId()));
    }
    return "";
  }

  @Override
  public EquipmentMaintain findEquipmentMaintain(Long id) {
    return emergencyPlanMapper.selectById(id);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteEquipmentMaintain(Long id) {
    LambdaQueryWrapper<EquipmentMaintain> userWarpper = new LambdaQueryWrapper<>();
    userWarpper.eq(EquipmentMaintain::getId, id);
    EquipmentMaintain equipment = this.getById(id);
    if (equipment == null) {
      return;
    }
    JSONArray jsonArray = JSON.parseArray(equipment.getMaintenanceImage());
    JSONArray fileArray = JSON.parseArray(equipment.getMaintenanceFile());
    emergencyPlanMapper.delete(userWarpper);
    for (Object obj : jsonArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
    for (Object obj : fileArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
  }

  @Override
  public void deleteFile(Long id, String filePath) {
    if (!ObjectUtils.isEmpty(id)) {
      EquipmentMaintain equipmentMaintain = emergencyPlanMapper.selectById(id);
      if (ObjectUtils.isEmpty(equipmentMaintain)) {
        return;
      }
      JSONArray fileArray = JSON.parseArray(equipmentMaintain.getMaintenanceFile());
      JSONArray imageArray = JSON.parseArray(equipmentMaintain.getMaintenanceImage());
      JSONArray fileJson = getArray(filePath, fileArray);
      JSONArray imageJson = getArray(filePath, imageArray);
      equipmentMaintain.setMaintenanceFile(fileJson.toString());
      equipmentMaintain.setMaintenanceImage(imageJson.toString());
      emergencyPlanMapper.updateById(equipmentMaintain);
    }
    fdfsClientService.deleteFile(filePath);
  }
  private JSONArray getArray(String filePath, JSONArray fileArray) {
    JSONArray json = new JSONArray();
    for (Object obj : fileArray) {
      String filePath1 = ((JSONObject) obj).getString("filePath");
      if (filePath1.equals(filePath)){
        continue;
      }
      Map<String, Object> map=new HashMap<>();
      map.put("fileUrl",((JSONObject) obj).getString("fileUrl"));
      map.put("filePath",((JSONObject) obj).getString("filePath"));
      map.put("fileName",((JSONObject) obj).getString("fileName"));
      json.add(EpsUtil.toJsonObj(map));
    }
    return json;
  }
}
