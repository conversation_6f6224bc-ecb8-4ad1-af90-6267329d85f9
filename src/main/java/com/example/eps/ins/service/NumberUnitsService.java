package com.example.eps.ins.service;

import com.example.eps.ins.common.dto.report.req.CompanyView;
import com.example.eps.ins.common.dto.report.req.RiskListReq;
import com.example.eps.ins.common.dto.report.resp.*;

import java.util.List;

public interface NumberUnitsService {

    TRptCompanyViewResp companyView(CompanyView companyView);

    List<TRptIndustryScatterResp> getIndustry(CompanyView companyView);

    TRptStressThreeResp getThreeStress(CompanyView companyView);

    List<RiskListResp> riskList(RiskListReq riskListReq);

    List<RegionResp> getRegion();

}
