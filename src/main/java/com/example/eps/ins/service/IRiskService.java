package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Risk;

import java.util.List;

/**
 * 风险类型表 Service接口
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
public interface IRiskService extends IService<Risk> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param risk risk
     * @return IPage<Risk>
     */
    IPage<Risk> findRisks(QueryRequest request, Risk risk);

    /**
     * 查询（所有）
     * @param parentId
     * @param queryAll
     * @return List<Risk>
     */
    List<Risk> findRisksSystem(Long parentId, boolean queryAll);
    /**
     * 新增
     *
     * @param risk risk
     */
    void createRisk(Risk risk);

    /**
     * 修改
     *
     * @param risk risk
     */
    void updateRisk(Risk risk);

    /**
     * 删除
     *
     * @param risk risk
     */
    void deleteRisk(Risk risk);
}
