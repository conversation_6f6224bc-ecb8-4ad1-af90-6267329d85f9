package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.HiddenDangerList;
import com.example.eps.ins.common.entity.enterprise.HiddenDangerListInspect;

import java.io.IOException;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 9:51
 * @Version: 1.0
 */

public interface HiddenDangerListService extends IService<HiddenDangerList> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param hiddenDangerList hiddenDangerList
     * @return IPage<EmergencyPlan>
     */
    IPage<HiddenDangerList> findHiddenDangerList(QueryRequest request, HiddenDangerList hiddenDangerList);

    /**
     * 新增
     * @param hiddenDangerList hiddenDangerList
     * @throws IOException IOException
     */
    String createHiddenDangerList(HiddenDangerList hiddenDangerList);

    /**
     * 详情
     * @param id  id
     * @return
     */
    HiddenDangerList findHiddenDangerList(Long id);


    /**
     * 删除
     *
     */
    void deleteHiddenDangerList(Long id);

    /**
     * 删除
     *
     */
    void deleteFile(Long id, String filePath);
    /**
     * 删除隐患处理图片
     *
     */
    void deleteInspectFile(Long id, String filePath);
    /**
     * 隐患处理详情
     */
    HiddenDangerListInspect findHiddenDangerListInspect(Long id);
    /**
     * 隐患处理
     */
    void addInspect(HiddenDangerListInspect dto);
}
