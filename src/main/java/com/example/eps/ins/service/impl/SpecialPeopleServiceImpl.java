package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.SpecialPeople;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.SpecialPeopleMapper;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.SpecialPeopleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/** @Author: Zhanghongyin @Date: Created in 2022/10/11 9:52 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class SpecialPeopleServiceImpl extends ServiceImpl<SpecialPeopleMapper, SpecialPeople>
    implements SpecialPeopleService {

  private final FdfsClientService fdfsClientService;
  private final SpecialPeopleMapper emergencyPlanMapper;
  private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

  @Override
  public IPage<SpecialPeople> findSpecialPeoples(
      QueryRequest request, SpecialPeople emergencyPlan) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    if (!ObjectUtils.isEmpty(emergencyPlan.getCreatorId())){
      userId=emergencyPlan.getCreatorId();
    }
    LambdaQueryWrapper<SpecialPeople> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(SpecialPeople::getCreatorId, userId);
    if (!ObjectUtils.isEmpty(emergencyPlan.getSpecialName())) {
      queryWrapper.like(SpecialPeople::getSpecialName, emergencyPlan.getSpecialName());
    }
    queryWrapper.orderByDesc(SpecialPeople::getCreateTime);
    Page<SpecialPeople> page = new Page<>(request.getPageNum(), request.getPageSize());
    return this.page(page, queryWrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String createSpecialPeople(SpecialPeople specialPeople) {
    //获取所有的企业申报信息
    List<EnterpriseDeclarationResponse> enterprise =
            enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
    //获取所有申报状态
    List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
    if (ObjectUtils.isEmpty(enterprise)){
      throw new EpsException("请完善企业信息后创建企业特种人员持证上岗情况！");
    }
    if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
      throw new EpsException("企业信息暂未审核通过，请审核通过后创建企业特种人员持证上岗情况！");
    }
    // 保存消息
    SpecialPeople emergencyPlan = new SpecialPeople();
    emergencyPlan.setSpecialName(specialPeople.getSpecialName());
    emergencyPlan.setSpecialPost(specialPeople.getSpecialPost());
    emergencyPlan.setSpecialCertificate(specialPeople.getSpecialCertificate());
    emergencyPlan.setSpecialQualifications(specialPeople.getSpecialQualifications());
    emergencyPlan.setSpecialOffice(specialPeople.getSpecialOffice());
    emergencyPlan.setSpecialExpirationTime(specialPeople.getSpecialExpirationTime());
    emergencyPlan.setSpecialIdCard(specialPeople.getSpecialIdCard());
    emergencyPlan.setCreateTime(new Date());
    if (ObjectUtils.isEmpty(specialPeople.getId())) {
      CurrentUser currentUser = EpsUtil.getCurrentUser();
      emergencyPlan.setCreatorId(currentUser.getUserId());
      emergencyPlan.setRegionId(enterprise.get(0).getRegionTownId());
      this.save(emergencyPlan);
    } else {
      emergencyPlan.setCreatorId(specialPeople.getCreatorId());
      this.update(emergencyPlan, new QueryWrapper<SpecialPeople>()
              .eq("id", specialPeople.getId()));
    }
    return "";
  }

  @Override
  public SpecialPeople findSpecialPeople(Long id) {
    return emergencyPlanMapper.selectById(id);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteSpecialPeople(Long id) {
    LambdaQueryWrapper<SpecialPeople> userWarpper = new LambdaQueryWrapper<>();
    userWarpper.eq(SpecialPeople::getId, id);
    SpecialPeople emergencyPlan = this.getById(id);
    if (emergencyPlan == null) {
      return;
    }
    emergencyPlanMapper.delete(userWarpper);
  }

//  @Override
//  public void deleteFile(Long id, String filePath) {
//    if (!ObjectUtils.isEmpty(id)) {
//      SpecialPeople emergencyPlan = emergencyPlanMapper.selectById(id);
//      if (ObjectUtils.isEmpty(emergencyPlan)) {
//        return;
//      }
//      JSONArray jsonArray = JSON.parseArray(emergencyPlan.getEmergencyPlanFile());
//      JSONArray json = new JSONArray();
//      for (Object obj : jsonArray) {
//        String filePath1 = ((JSONObject) obj).getString("filePath");
//        if (filePath1.equals(filePath)) {
//          continue;
//        }
//        Map<String, Object> map = new HashMap<>();
//        map.put("fileUrl", ((JSONObject) obj).getString("fileUrl"));
//        map.put("filePath", ((JSONObject) obj).getString("filePath"));
//        map.put("fileName", ((JSONObject) obj).getString("fileName"));
//        json.add(EpsUtil.toJsonObj(map));
//      }
//      emergencyPlan.setEmergencyPlanFile(json.toString());
//      emergencyPlanMapper.updateById(emergencyPlan);
//    }
//    fdfsClientService.deleteFile(filePath);
//  }
}
