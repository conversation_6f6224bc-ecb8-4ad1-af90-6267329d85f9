package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EmergencyPlan;
import com.example.eps.ins.common.dto.enterprise.EmergencyPlanVo;

import java.io.IOException;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2022/10/11 9:51
 * @Version: 1.0
 */

public interface IEmergencyPlanService extends IService<EmergencyPlan> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param emergencyPlan enterpriseRisk
     * @return IPage<EmergencyPlan>
     */
    IPage<EmergencyPlan> findEmergencyPlans(QueryRequest request, EmergencyPlan emergencyPlan);

    /**
     * 新增
     * @param emergencyPlan emergencyPlan
     * @throws IOException IOException
     */
    String createEmergencyPlan(EmergencyPlanVo emergencyPlan);

    /**
     * 详情
     * @param id  id
     * @return
     */
    EmergencyPlan findEmergencyPlan(Long id);


    /**
     * 删除
     *
     */
    void deleteEmergencyPlan(Long id);

    /**
     * 删除文件
     * @param id
     * @param filePath
     */
    void deleteFile(Long id, String filePath);
}
