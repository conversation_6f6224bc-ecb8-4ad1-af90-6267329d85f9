package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.SystemUser;
import com.example.eps.ins.common.entity.system.UserDataPermission;
import com.example.eps.ins.common.entity.system.UserRole;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.UserMapper;
import com.example.eps.ins.service.IRegionService;
import com.example.eps.ins.service.IUserDataPermissionService;
import com.example.eps.ins.service.IUserRoleService;
import com.example.eps.ins.service.IUserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserServiceImpl extends ServiceImpl<UserMapper, SystemUser> implements IUserService {
    private final IRegionService regionService;
    private final IUserRoleService userRoleService;
    private final IUserDataPermissionService userDataPermissionService;
    private final PasswordEncoder passwordEncoder;

    @Override
    public SystemUser findByName(String username) {
        LambdaQueryWrapper<SystemUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemUser::getUsername, username);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public SystemUser findById(Long userId) {
        LambdaQueryWrapper<SystemUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemUser::getUserId, userId);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<SystemUser> findList(SystemUser user) {
        LambdaQueryWrapper<SystemUser> queryWrapper = new LambdaQueryWrapper<>(user);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public IPage<SystemUser> findUserDetailList(SystemUser user, QueryRequest request) {
        LambdaQueryWrapper<SystemUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SystemUser.class,i -> !i.getColumn().equals("password"));
        if (user.getRegionId() != null) {
            queryWrapper.eq(SystemUser::getRegionId, user.getRegionId());
        }

        if (StringUtils.isNoneBlank(user.getNickName())) {
            queryWrapper.like(SystemUser::getNickName, user.getNickName());
        }

        if (StringUtils.isNoneBlank(user.getMobile())) {
            queryWrapper.like(SystemUser::getMobile, user.getMobile());
        }

        if (StringUtils.isNoneBlank(user.getUsername())) {
            queryWrapper.like(SystemUser::getUsername, user.getUsername());
        }

        IPage<SystemUser> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<SystemUser> systemUserIPage = this.baseMapper.selectPage(page, queryWrapper);
        systemUserIPage.getRecords().forEach((tempUser)->{
            List<UserRole> userRoles = userRoleService.list(new LambdaQueryWrapper<UserRole>().eq(UserRole::getUserId, tempUser.getUserId()));
            if(null != userRoles){
                tempUser.setRoleIds( userRoles.stream().map(userRole->userRole.getRoleId()).collect(Collectors.toList()).toArray(new Long[]{}));
            }
        });
        return systemUserIPage;
    }

    @Override
    public SystemUser findUser(Long id) {
        return this.findById(id);
    }

    @Override
    public SystemUser findUserDetail(String username) {
        SystemUser param = new SystemUser();
        param.setUsername(username);
        List<SystemUser> users = this.baseMapper.findUserDetail(param);
        return CollectionUtils.isNotEmpty(users) ? users.get(0) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLoginTime(String username) {
        SystemUser user = new SystemUser();
        user.setLastLoginTime(new Date());

        this.baseMapper.update(user, new LambdaQueryWrapper<SystemUser>().eq(SystemUser::getUsername, username));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createUser(SystemUser user) {
        // 创建用户
        user.setCreateTime(new Date());
        user.setPassword(passwordEncoder.encode(SystemUser.DEFAULT_PWD));
        save(user);
        // 保存用户角色
        setUserRoles(user, user.getRoleIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(SystemUser user) {
        // 更新用户
        updateById(user);

        String[] userIds = {String.valueOf(user.getUserId())};
        userRoleService.deleteUserRolesByUserId(userIds);
        setUserRoles(user, user.getRoleIds());

        userDataPermissionService.deleteByUserIds(userIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUsers(String[] userIds) {
        List<String> list = Arrays.asList(userIds);
        removeByIds(list);
        // 删除用户角色
        this.userRoleService.deleteUserRolesByUserId(userIds);
        this.userDataPermissionService.deleteByUserIds(userIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProfile(SystemUser user) throws EpsException {
        user.setPassword(null);
        user.setUsername(null);
        user.setStatus(0);
        if (isCurrentUser(user.getUserId())) {
            updateById(user);
        } else {
            throw new EpsException("您无权修改别人的账号信息！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAvatar(String avatar) {
        SystemUser user = new SystemUser();
        String currentUsername = EpsUtil.getCurrentUsername();
        this.baseMapper.update(user, new LambdaQueryWrapper<SystemUser>().eq(SystemUser::getUsername, currentUsername));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(String password) {
        SystemUser user = new SystemUser();
        user.setPassword(passwordEncoder.encode(password));
        String currentUsername = EpsUtil.getCurrentUsername();
        this.baseMapper.update(user, new LambdaQueryWrapper<SystemUser>().eq(SystemUser::getUsername, currentUsername));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(Integer[] userIDs) {
        SystemUser params = new SystemUser();
        params.setPassword(new BCryptPasswordEncoder().encode(SystemUser.DEFAULT_PWD));

        List<Integer> list = Arrays.asList(userIDs);
        this.baseMapper.update(params, new LambdaQueryWrapper<SystemUser>().in(SystemUser::getUserId, list));

    }

    @Override
    public void updateSidebarTheme(String theme) {
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        if (currentUser != null) {
            Long userId = currentUser.getUserId();
            SystemUser user = new SystemUser();
            user.setUserId(userId);
            baseMapper.updateById(user);
        }
    }

    private void setUserRoles(SystemUser user, Long[] roles) {
        List<UserRole> userRoles = new ArrayList<>();
        Arrays.stream(roles).forEach(roleId -> {
            UserRole userRole = new UserRole();
            userRole.setUserId(user.getUserId());
            userRole.setRoleId(roleId);
            userRoles.add(userRole);
        });
        userRoleService.saveBatch(userRoles);
    }

    private void setUserDataPermissions(SystemUser user, String[] deptIds) {
        List<UserDataPermission> userDataPermissions = new ArrayList<>();
        Arrays.stream(deptIds).forEach(deptId -> {
            UserDataPermission permission = new UserDataPermission();
            permission.setDeptId(Long.valueOf(deptId));
            permission.setUserId(user.getUserId());
            userDataPermissions.add(permission);
        });
        userDataPermissionService.saveBatch(userDataPermissions);
    }

    private boolean isCurrentUser(Long id) {
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        return currentUser != null && id.equals(currentUser.getUserId());
    }
}
