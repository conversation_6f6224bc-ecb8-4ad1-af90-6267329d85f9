package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.Accident;
import com.example.eps.ins.mapper.AccidentMapper;
import com.example.eps.ins.service.IAccidentService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 事故统计表 Service实现
 *
 * <AUTHOR>
 * @date 2021-10-27 14:28:15
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class AccidentServiceImpl extends ServiceImpl<AccidentMapper, Accident> implements IAccidentService {

    private final AccidentMapper accidentMapper;

    @Override
    public IPage<Accident> findAccidents(QueryRequest request, Accident accident) {
        LambdaQueryWrapper<Accident> queryWrapper = new LambdaQueryWrapper<>();
        // TODO 设置查询条件
        if (!StringUtils.isEmpty(accident.getAccidentCode())) {
            queryWrapper.like(Accident::getAccidentCode, accident.getAccidentCode());
        }
        if (!StringUtils.isEmpty(accident.getAccidentUnit())) {
            queryWrapper.like(Accident::getAccidentUnit, accident.getAccidentUnit());
        }
        if (!StringUtils.isEmpty(accident.getAccidentCounty())) {
            queryWrapper.eq(Accident::getAccidentCounty, accident.getAccidentCounty());
        }
        if (accident.getAccidentTimeFrom() != null && accident.getAccidentTimeTo() != null) {
            queryWrapper.between(Accident::getAccidentTime, accident.getAccidentTimeFrom(), accident.getAccidentTimeTo());
        }
        Page<Accident> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    public List<Accident> findAccidents(Accident accident) {
        LambdaQueryWrapper<Accident> queryWrapper = new LambdaQueryWrapper<>();
        // TODO 设置查询条件
        if (accident.getAccidentCodeArray() != null && accident.getAccidentCodeArray().size() > 0) {
            queryWrapper.in(Accident::getAccidentCode, accident.getAccidentCodeArray());
        }
        if (!StringUtils.isEmpty(accident.getAccidentCode())) {
            queryWrapper.eq(Accident::getAccidentCode, accident.getAccidentCode());
        }
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<Accident> findAccidentCountryList() {
        QueryWrapper<Accident> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT ACCIDENT_COUNTY").orderByAsc("ACCIDENT_COUNTY");
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAccident(Accident accident) {
        this.save(accident);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccident(Accident accident) {
        this.saveOrUpdate(accident);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAccident(Accident accident) {
        LambdaQueryWrapper<Accident> wapper = new LambdaQueryWrapper<>();
        // TODO 设置删除条件
        if (accident.getAccidentId() != null) {
            wapper.eq(Accident::getAccidentId, accident.getAccidentId());
        }
        this.remove(wapper);
    }
}
