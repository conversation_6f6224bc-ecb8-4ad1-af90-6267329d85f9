package com.example.eps.ins.service;


import com.example.eps.ins.common.config.WebSocket;
import com.example.eps.ins.common.dto.report.SubMessage;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date: Created in 2023/1/17 11:16
 * @Version: 1.0
 */
public interface MsgService {
    /**
     * 发送消息
     * @param message
     * @param webSocket
     */
    void sendMessage(SubMessage message, WebSocket webSocket);

    /**
     * 聊天记录
     * @param message
     * @return
     */
    List<SubMessage> getList(SubMessage message);
}
