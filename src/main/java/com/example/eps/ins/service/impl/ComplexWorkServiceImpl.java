package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.ComplexWork;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.ComplexWorkMapper;
import com.example.eps.ins.service.ComplexWorkService;
import com.example.eps.ins.service.FdfsClientService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/9 13:38
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ComplexWorkServiceImpl extends ServiceImpl<ComplexWorkMapper, ComplexWork>
        implements ComplexWorkService {

    private final ComplexWorkMapper outsourceWorkMapper;
    private final FdfsClientService fdfsClientService;
    private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

    @Override
    public IPage<ComplexWork> findComplexWork(QueryRequest request, ComplexWork outsourceWork) {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        if (!ObjectUtils.isEmpty(outsourceWork.getCreatorId())){
            userId=outsourceWork.getCreatorId();
        }
        LambdaQueryWrapper<ComplexWork> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ComplexWork::getCreatorId, userId);
        if (!ObjectUtils.isEmpty(outsourceWork.getComplexType())) {
            queryWrapper.eq(ComplexWork::getComplexType, outsourceWork.getComplexType());
        }
        queryWrapper.orderByDesc(ComplexWork::getCreateTime);
        Page<ComplexWork> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createComplexWork(ComplexWork complexWork) {
        //获取所有的企业申报信息
        List<EnterpriseDeclarationResponse> enterprise =
                enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
        //获取所有申报状态
        List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(enterprise)){
            throw new EpsException("请完善企业信息后创建企业复工复产信息！");
        }
        if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
            throw new EpsException("企业信息暂未审核通过，请审核通过后创建复工复产信息！");
        }
        if (ObjectUtils.isEmpty(complexWork.getComplexFile())){
            complexWork.setComplexFile("[]");
        }
        if (ObjectUtils.isEmpty(complexWork.getComplexImage())){
            complexWork.setComplexImage("[]");
        }
        complexWork.setCreateTime(new Date());
        if (ObjectUtils.isEmpty(complexWork.getId())) {
            CurrentUser currentUser = EpsUtil.getCurrentUser();
            complexWork.setCreatorId(currentUser.getUserId());
            complexWork.setRegionId(enterprise.get(0).getRegionTownId());
            this.save(complexWork);
        } else {
            complexWork.setCreatorId(complexWork.getCreatorId());
            this.update(complexWork, new QueryWrapper<ComplexWork>()
                    .eq("id", complexWork.getId()));
        }
        return "";
    }

    @Override
    public ComplexWork findComplexWork(Long id) {
        return outsourceWorkMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteComplexWork(Long id) {
        LambdaQueryWrapper<ComplexWork> userWarpper = new LambdaQueryWrapper<>();
        userWarpper.eq(ComplexWork::getId, id);
        ComplexWork complexWork = this.getById(id);
        if (complexWork == null) {
            return;
        }
        JSONArray jsonArray = JSON.parseArray(complexWork.getComplexFile());
        JSONArray imageArray = JSON.parseArray(complexWork.getComplexImage());
        outsourceWorkMapper.delete(userWarpper);
        for (Object obj : jsonArray) {
            fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
        }
        for (Object obj : imageArray) {
            fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(Long id, String filePath) {
        if (!ObjectUtils.isEmpty(id)) {
            ComplexWork complexWork = outsourceWorkMapper.selectById(id);
            if (ObjectUtils.isEmpty(complexWork)) {
                return;
            }
            JSONArray fileArray = JSON.parseArray(complexWork.getComplexFile());
            JSONArray imageArray = JSON.parseArray(complexWork.getComplexImage());
            JSONArray fileJson = getArray(filePath, fileArray);
            JSONArray imageJson = getArray(filePath, imageArray);
            complexWork.setComplexFile(fileJson.toString());
            complexWork.setComplexImage(imageJson.toString());
            outsourceWorkMapper.updateById(complexWork);
        }
        fdfsClientService.deleteFile(filePath);
    }

    private JSONArray getArray(String filePath, JSONArray fileArray) {
        JSONArray json = new JSONArray();
        for (Object obj : fileArray) {
            String filePath1 = ((JSONObject) obj).getString("filePath");
            if (filePath1.equals(filePath)){
                continue;
            }
            Map<String, Object> map=new HashMap<>();
            map.put("fileUrl",((JSONObject) obj).getString("fileUrl"));
            map.put("filePath",((JSONObject) obj).getString("filePath"));
            map.put("fileName",((JSONObject) obj).getString("fileName"));
            json.add(EpsUtil.toJsonObj(map));
        }
        return json;
    }
}
