package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.constant.EnterpriseDeclarationConstant;
import com.example.eps.ins.common.entity.enterprise.HiddenDangerList;
import com.example.eps.ins.common.entity.enterprise.HiddenDangerListInspect;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.HiddenDangerListInspectMapper;
import com.example.eps.ins.mapper.HiddenDangerListMapper;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.HiddenDangerListService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/** @Author: Zhanghongyin @Date: Created in 2022/10/11 9:52 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class HiddenDangerListServiceImpl extends ServiceImpl<HiddenDangerListMapper, HiddenDangerList>
    implements HiddenDangerListService {

  private final FdfsClientService fdfsClientService;
  private final HiddenDangerListMapper hiddenDangerMapper;
  private final HiddenDangerListInspectMapper hiddenDangerListInspectMapper;
  private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

  @Override
  public IPage<HiddenDangerList> findHiddenDangerList(QueryRequest request, HiddenDangerList hiddenDangerList) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    if (!ObjectUtils.isEmpty(hiddenDangerList.getCreatorId())){
      userId=hiddenDangerList.getCreatorId();
    }
    LambdaQueryWrapper<HiddenDangerList> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(HiddenDangerList::getCreatorId, userId);
    if (!ObjectUtils.isEmpty(hiddenDangerList.getStartDate())) {
      queryWrapper.between(HiddenDangerList::getHiddenDangerInspect,hiddenDangerList.getStartDate().get(0),
              hiddenDangerList.getStartDate().get(1));
    }
    if (!ObjectUtils.isEmpty(hiddenDangerList.getHiddenDangerStatus())) {
      queryWrapper.like(HiddenDangerList::getHiddenDangerStatus, hiddenDangerList.getHiddenDangerStatus());
    }
    if (!ObjectUtils.isEmpty(hiddenDangerList.getHiddenDangerCycle())) {
      queryWrapper.eq(HiddenDangerList::getHiddenDangerCycle, hiddenDangerList.getHiddenDangerCycle());
    }
    if (!ObjectUtils.isEmpty(hiddenDangerList.getHiddenDangerContent())){
      queryWrapper.like(HiddenDangerList::getHiddenDangerContent,hiddenDangerList.getHiddenDangerContent());
    }
    queryWrapper.orderByDesc(HiddenDangerList::getCreateTime);
    Page<HiddenDangerList> page = new Page<>(request.getPageNum(), request.getPageSize());
    Page<HiddenDangerList> resp = this.page(page, queryWrapper);
    resp.getRecords().forEach(a->{
      if(a.getHiddenDangerCycle().equals(EnterpriseDeclarationConstant.HIDDEN_DANGER_ONE)){
        a.setHiddenDangerInspect(a.getHiddenDangerInspect()+"~"+a.getHiddenDangerEnd());
      }
    });
    return resp;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String createHiddenDangerList(HiddenDangerList hiddenDangerList) {
    //获取所有的企业申报信息
    List<EnterpriseDeclarationResponse> enterprise =
            enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
    //获取所有申报状态
    List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
    if (ObjectUtils.isEmpty(enterprise)){
      throw new EpsException("请完善企业信息后创建企业隐患排查制度维护！");
    }
    if (!collect.contains(EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
      throw new EpsException("企业信息暂未审核通过，请审核通过后创建企业隐患排查制度维护！");
    }
    // 保存消息
    HiddenDangerList hiddenDanger = new HiddenDangerList();
    hiddenDanger.setHiddenDangerCycle(hiddenDangerList.getHiddenDangerCycle());
    hiddenDanger.setHiddenDangerContent(hiddenDangerList.getHiddenDangerContent());
    hiddenDanger.setHiddenDangerInspect(hiddenDangerList.getHiddenDangerInspect());
    hiddenDanger.setHiddenDangerEnd(hiddenDangerList.getHiddenDangerEnd());
    hiddenDanger.setHiddenDangerDept(hiddenDangerList.getHiddenDangerDept());
    hiddenDanger.setHiddenDangerRevise(hiddenDangerList.getHiddenDangerRevise());
    hiddenDanger.setHiddenStatus(hiddenDangerList.getHiddenStatus());
    if (!ObjectUtils.isEmpty(hiddenDangerList.getHiddenStatus())
            && hiddenDangerList.getHiddenStatus().equals(EnterpriseDeclarationConstant.HIDDEN_STATUS_NO)){
      hiddenDanger.setHiddenDangerStatus(EnterpriseDeclarationConstant.INSPECT_STATUS);
    }else {
      //判断隐患是否处理
      HiddenDangerListInspect hiddenDangerListInspect = hiddenDangerListInspectMapper.
              selectOne(new QueryWrapper<HiddenDangerListInspect>()
                      .eq("hidden_danger_id",hiddenDangerList.getId()));
      if (ObjectUtils.isEmpty(hiddenDangerListInspect)) {
        hiddenDanger.setHiddenDangerStatus(EnterpriseDeclarationConstant.INSPECT_STATUS_NO);
      }else {
        hiddenDanger.setHiddenDangerStatus(EnterpriseDeclarationConstant.INSPECT_STATUS_OK);
      }
    }
    hiddenDanger.setHiddenDangerReviseTime(hiddenDangerList.getHiddenDangerReviseTime());
    if (ObjectUtils.isEmpty(hiddenDangerList.getHiddenDangerImage())){
      hiddenDanger.setHiddenDangerImage("[]");
    } else {
      hiddenDanger.setHiddenDangerImage(hiddenDangerList.getHiddenDangerImage());
    }
    if (ObjectUtils.isEmpty(hiddenDangerList.getHiddenDangerFile())){
      hiddenDanger.setHiddenDangerFile("[]");
    } else {
      hiddenDanger.setHiddenDangerFile(hiddenDangerList.getHiddenDangerFile());
    }
    hiddenDanger.setCreateTime(new Date());
    if (ObjectUtils.isEmpty(hiddenDangerList.getId())) {
      CurrentUser currentUser = EpsUtil.getCurrentUser();
      hiddenDanger.setCreatorId(currentUser.getUserId());
      hiddenDanger.setRegionId(enterprise.get(0).getRegionTownId());
      this.save(hiddenDanger);
    } else {
      hiddenDanger.setCreatorId(hiddenDangerList.getCreatorId());
      this.update(hiddenDanger, new QueryWrapper<HiddenDangerList>().eq("id", hiddenDangerList.getId()));
    }
    return "";
  }

  @Override
  public HiddenDangerList findHiddenDangerList(Long id) {
    HiddenDangerList hiddenDangerList = hiddenDangerMapper.selectById(id);
    HiddenDangerListInspect hiddenDangerListInspect = hiddenDangerListInspectMapper.
            selectOne(new QueryWrapper<HiddenDangerListInspect>()
                    .eq("hidden_danger_id",hiddenDangerList.getId()));
    if (ObjectUtils.isEmpty(hiddenDangerListInspect)){
      hiddenDangerList.setUpdateStatus(EnterpriseDeclarationConstant.UPDATE_STATUS_OK);
    }else {
      hiddenDangerList.setUpdateStatus(EnterpriseDeclarationConstant.UPDATE_STATUS_NO);
    }
    return hiddenDangerList;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteHiddenDangerList(Long id) {
    LambdaQueryWrapper<HiddenDangerList> userWarpper = new LambdaQueryWrapper<>();
    userWarpper.eq(HiddenDangerList::getId, id);
    HiddenDangerList hiddenDangerList = this.getById(id);
    if (hiddenDangerList == null) {
      return;
    }
    JSONArray jsonArray = JSON.parseArray(hiddenDangerList.getHiddenDangerImage());
    JSONArray fileArray = JSON.parseArray(hiddenDangerList.getHiddenDangerFile());
    hiddenDangerMapper.delete(userWarpper);
    for (Object obj : jsonArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
    for (Object obj : fileArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
    //删除隐患处理
    HiddenDangerListInspect hiddenDangerId = hiddenDangerListInspectMapper.
            selectOne(new QueryWrapper<HiddenDangerListInspect>().eq("hidden_danger_id", id));
    if (!ObjectUtils.isEmpty(hiddenDangerId)){
      //获取隐患整改图片
      JSONArray inspectArray = JSON.parseArray(hiddenDangerId.getInspectImage());
      JSONArray fileDeleArray = JSON.parseArray(hiddenDangerId.getInspectFile());
      //删除隐患排查整改
      hiddenDangerListInspectMapper.deleteById(hiddenDangerId.getId());
      for (Object obj : inspectArray) {
        fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
      }
      for (Object obj : fileDeleArray) {
        fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
      }
    }
  }

  @Override
  public void deleteFile(Long id, String filePath) {
    if (!ObjectUtils.isEmpty(id)) {
      HiddenDangerList hiddenDangerList = hiddenDangerMapper.selectById(id);
      if (ObjectUtils.isEmpty(hiddenDangerList)) {
        return;
      }
      JSONArray imageArray = JSON.parseArray(hiddenDangerList.getHiddenDangerImage());
      JSONArray imageJson = getArray(filePath, imageArray);
      hiddenDangerList.setHiddenDangerImage(imageJson.toString());
      JSONArray fileArray = JSON.parseArray(hiddenDangerList.getHiddenDangerFile());
      JSONArray fileJson = getArray(filePath, fileArray);
      hiddenDangerList.setHiddenDangerImage(fileJson.toString());
      hiddenDangerMapper.updateById(hiddenDangerList);
    }
    fdfsClientService.deleteFile(filePath);
  }

  @Override
  public HiddenDangerListInspect findHiddenDangerListInspect(Long id) {
    HiddenDangerListInspect hiddenDangerId = hiddenDangerListInspectMapper.
            selectOne(new QueryWrapper<HiddenDangerListInspect>()
            .eq("hidden_danger_id", id));
    if(ObjectUtils.isEmpty(hiddenDangerId)){
      hiddenDangerId=new HiddenDangerListInspect();
    }
    HiddenDangerList hiddenDangerList = hiddenDangerMapper.selectById(id);
    hiddenDangerId.setHiddenDangerDept(hiddenDangerList.getHiddenDangerDept());
    hiddenDangerId.setHiddenDangerRevise(hiddenDangerList.getHiddenDangerRevise());
    hiddenDangerId.setHiddenDangerContent(hiddenDangerList.getHiddenDangerContent());
    hiddenDangerId.setHiddenDangerInspect(hiddenDangerList.getHiddenDangerInspect() );
    hiddenDangerId.setHiddenDangerEnd( hiddenDangerList.getHiddenDangerEnd());
    hiddenDangerId.setHiddenDangerCycle(hiddenDangerList.getHiddenDangerCycle());
    hiddenDangerId.setHiddenDangerReviseTime(hiddenDangerList.getHiddenDangerReviseTime());
    hiddenDangerId.setHiddenDangerId(id);
    if (ObjectUtils.isEmpty(hiddenDangerId.getInspectImage())){
      hiddenDangerId.setInspectImage("[]");
    }
    if (ObjectUtils.isEmpty(hiddenDangerId.getInspectFile())){
      hiddenDangerId.setInspectFile("[]");
    }
    return  hiddenDangerId;
  }

  @Override
  public void addInspect(HiddenDangerListInspect dto) {
    dto.setCreateTime(new Date());
    if (ObjectUtils.isEmpty(dto.getId())) {
      CurrentUser currentUser = EpsUtil.getCurrentUser();
      dto.setCreatorId(currentUser.getUserId());
      if (ObjectUtils.isEmpty(dto.getInspectImage())){
        dto.setInspectImage("[]");
      }
      if (ObjectUtils.isEmpty(dto.getInspectFile())){
        dto.setInspectFile("[]");
      }
      dto.setRegionId(currentUser.getRegionId());
      hiddenDangerListInspectMapper.insert(dto);
      //修改隐患处理状态
      HiddenDangerList hiddenDangerList = hiddenDangerMapper.selectById(dto.getHiddenDangerId());
      hiddenDangerList.setHiddenDangerStatus(EnterpriseDeclarationConstant.INSPECT_STATUS_OK);
      hiddenDangerMapper.updateById(hiddenDangerList);
    } else {
      dto.setCreatorId(dto.getCreatorId());
      hiddenDangerListInspectMapper.update(
              dto, new QueryWrapper<HiddenDangerListInspect>().eq("id", dto.getId()));
    }
  }

  @Override
  public void deleteInspectFile(Long id, String filePath) {
    if (!ObjectUtils.isEmpty(id)) {
      HiddenDangerListInspect hiddenDangerList = hiddenDangerListInspectMapper.selectById(id);
      if (ObjectUtils.isEmpty(hiddenDangerList)) {
        return;
      }
      JSONArray imageArray = JSON.parseArray(hiddenDangerList.getInspectImage());
      JSONArray fileArray = JSON.parseArray(hiddenDangerList.getInspectFile());
      JSONArray file = getArray(filePath, fileArray);
      JSONArray image = getArray(filePath, imageArray);
      hiddenDangerList.setInspectImage(image.toString());
      hiddenDangerList.setInspectFile(file.toString());
      hiddenDangerListInspectMapper.updateById(hiddenDangerList);
    }
    fdfsClientService.deleteFile(filePath);
  }
  private JSONArray getArray(String filePath, JSONArray fileArray) {
    JSONArray json = new JSONArray();
    for (Object obj : fileArray) {
      String filePath1 = ((JSONObject) obj).getString("filePath");
      if (filePath1.equals(filePath)){
        continue;
      }
      Map<String, Object> map=new HashMap<>();
      map.put("fileUrl",((JSONObject) obj).getString("fileUrl"));
      map.put("filePath",((JSONObject) obj).getString("filePath"));
      map.put("fileName",((JSONObject) obj).getString("fileName"));
      json.add(EpsUtil.toJsonObj(map));
    }
    return json;
  }
}
