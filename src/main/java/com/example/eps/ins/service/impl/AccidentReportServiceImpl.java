package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.constant.NumberConstant;
import com.example.eps.ins.common.dto.report.req.AreaReq;
import com.example.eps.ins.common.dto.report.req.CompanyView;
import com.example.eps.ins.common.dto.report.resp.AccidentAllResp;
import com.example.eps.ins.common.dto.report.resp.AccidentSortResp;
import com.example.eps.ins.common.dto.report.resp.AreaResp;
import com.example.eps.ins.common.dto.report.resp.IndustryAccidentResp;
import com.example.eps.ins.mapper.AccidentReportMapper;
import com.example.eps.ins.mapper.RegionMapper;
import com.example.eps.ins.service.AccidentReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class AccidentReportServiceImpl implements AccidentReportService {
  private final  AccidentReportMapper accidentReportMapper;
  private final  RegionMapper regionMapper;

  @Override
  public AccidentAllResp getAll(Long nameId, Integer year) {
    Long regionId = getRegionId();
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_THREE)){
      nameId=region.getParentId();
    }
      return accidentReportMapper.getOneCityAll(year, nameId);
  }

  @Override
  public List<IndustryAccidentResp> getIndustry(CompanyView companyView) {
    Long regionId1 = companyView.getRegionId();
    //当前登录人地区id
    Long regionId = getRegionId();
    // 获取当前登录人该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_THREE)){
      regionId1=region.getParentId();
    }
      List<IndustryAccidentResp> accidentResps = accidentReportMapper.getOneIndustryAccident(
      companyView.getYear(), regionId1);
      // 删除集合中的空元素
      accidentResps.removeAll(Collections.singleton(null));
      accidentResps.sort((o1, o2) -> o2.getNumber().compareTo(o1.getNumber()));
      return accidentResps;
  }

  @Override
  public List<AccidentSortResp> getSort(CompanyView companyView) {
    Long regionId1 = companyView.getRegionId();
    //当前登录人地区id
    Long regionId = getRegionId();
    // 获取当前登录人该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(NumberConstant.LEVE_THREE)){
      regionId1=region.getParentId();
    }
    // 获取返回结果数据
    List<AccidentSortResp> accidentResps =
        accidentReportMapper.getAccidentSort(companyView.getYear(),regionId1);
    // 删除集合中的空元素
    accidentResps.removeAll(Collections.singleton(null));
    // 排序返回
    accidentResps.sort((o1, o2) -> o2.getAccidentNum().compareTo(o1.getAccidentNum()));
    return accidentResps;
  }

  @Override
  public List<AreaResp> getArea(AreaReq companyView) {
    return accidentReportMapper.getAreaNum(companyView.getYear());
  }

  private Long getRegionId() {
    return EpsUtil.getCurrentUser().getRegionId();
  }
}
