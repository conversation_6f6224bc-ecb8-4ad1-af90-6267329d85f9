package com.example.eps.ins.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

public interface FdfsClientService {
    /**
     * 文件上传校验
     *
     * @param file
     * @return
     */
    void uploadCheck(MultipartFile file);

    /**
     * @param file
     * @return 返回文件路径以及文件访问url
     * @throws IOException
     */
    Map<String, Object> uploadFile(MultipartFile file) throws IOException;

    /**
     * 文件上传
     *
     * @param bytes     文件字节
     * @param fileSize  文件大小
     * @param fileName  文件名称
     * @param extension 文件扩展名
     * @return 返回文件路径以及文件访问url
     */
    Map<String, Object> uploadFile(byte[] bytes, long fileSize, String fileName, String extension);

    /**
     * 下载文件
     *
     * @param filePath 文件路径
     * @return 文件字节
     */
    byte[] downloadFile(String filePath);

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
    void deleteFile(String filePath);


    Map<String, Object> ossUploadFile(MultipartFile file);
}
