package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.FiniteSpaceWork;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.FiniteSpaceWorkMapper;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.FiniteSpaceWorkService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/9 10:42
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class FiniteSpaceWorkServiceImpl  extends ServiceImpl<FiniteSpaceWorkMapper, FiniteSpaceWork>
        implements FiniteSpaceWorkService {

    private final FdfsClientService fdfsClientService;
    private final FiniteSpaceWorkMapper finiteSpaceWorkMapper;
    private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

    @Override
    public IPage<FiniteSpaceWork> findFiniteSpaceWorks(QueryRequest request, FiniteSpaceWork finiteSpaceWork) {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        if (!ObjectUtils.isEmpty(finiteSpaceWork.getCreatorId())){
            userId=finiteSpaceWork.getCreatorId();
        }
        LambdaQueryWrapper<FiniteSpaceWork> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FiniteSpaceWork::getCreatorId, userId);
//        if (!ObjectUtils.isEmpty(finiteSpaceWork.getSpaceAddress())) {
//            queryWrapper.like(FiniteSpaceWork::getSpaceAddress, finiteSpaceWork.getSpaceAddress());
//        }
        if (!ObjectUtils.isEmpty(finiteSpaceWork.getSpaceName())) {
            queryWrapper.like(FiniteSpaceWork::getSpaceName, finiteSpaceWork.getSpaceName());
        }
        queryWrapper.orderByDesc(FiniteSpaceWork::getCreateTime);
        Page<FiniteSpaceWork> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createFiniteSpaceWork(FiniteSpaceWork finiteSpaceWork) {
        //获取所有的企业申报信息
        List<EnterpriseDeclarationResponse> enterprise =
                enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
        //获取所有申报状态
        List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(enterprise)){
            throw new EpsException("请完善企业信息后创建企业应急预案信息！");
        }
        if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
            throw new EpsException("企业信息暂未审核通过，请审核通过后创建有限空间作业信息！");
        }
        if (ObjectUtils.isEmpty(finiteSpaceWork.getSpaceFile())){
            finiteSpaceWork.setSpaceFile("[]");
        }
        finiteSpaceWork.setCreateTime(new Date());
        if (ObjectUtils.isEmpty(finiteSpaceWork.getId())) {
            CurrentUser currentUser = EpsUtil.getCurrentUser();
            finiteSpaceWork.setCreatorId(currentUser.getUserId());
            finiteSpaceWork.setRegionId(enterprise.get(0).getRegionTownId());
            this.save(finiteSpaceWork);
        } else {
            finiteSpaceWork.setCreatorId(finiteSpaceWork.getCreatorId());
            this.update(finiteSpaceWork, new QueryWrapper<FiniteSpaceWork>()
                    .eq("id", finiteSpaceWork.getId()));
        }
        return "";
    }

    @Override
    public FiniteSpaceWork findFiniteSpaceWork(Long id) {
        return finiteSpaceWorkMapper.selectById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteFiniteSpaceWork(Long id) {
        LambdaQueryWrapper<FiniteSpaceWork> userWarpper = new LambdaQueryWrapper<>();
        userWarpper.eq(FiniteSpaceWork::getId, id);
        FiniteSpaceWork finiteSpaceWork = this.getById(id);
        if (finiteSpaceWork == null) {
            return;
        }
        JSONArray jsonArray = JSON.parseArray(finiteSpaceWork.getSpaceFile());
        finiteSpaceWorkMapper.delete(userWarpper);
        for (Object obj : jsonArray) {
            fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(Long id, String filePath) {
        if (!ObjectUtils.isEmpty(id)) {
            FiniteSpaceWork finiteSpaceWork = finiteSpaceWorkMapper.selectById(id);
            if (ObjectUtils.isEmpty(finiteSpaceWork)) {
                return;
            }
            JSONArray jsonArray = JSON.parseArray(finiteSpaceWork.getSpaceFile());
            JSONArray json = new JSONArray();
            for (Object obj : jsonArray) {
                String filePath1 = ((JSONObject) obj).getString("filePath");
                if (filePath1.equals(filePath)) {
                    continue;
                }
                Map<String, Object> map = new HashMap<>();
                map.put("fileUrl", ((JSONObject) obj).getString("fileUrl"));
                map.put("filePath", ((JSONObject) obj).getString("filePath"));
                map.put("fileName", ((JSONObject) obj).getString("fileName"));
                json.add(EpsUtil.toJsonObj(map));
            }
            finiteSpaceWork.setSpaceFile(json.toString());
            finiteSpaceWorkMapper.updateById(finiteSpaceWork);
        }
        fdfsClientService.deleteFile(filePath);
    }
}
