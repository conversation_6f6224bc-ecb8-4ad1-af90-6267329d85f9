package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.OutsourceWork;

import java.io.IOException;

/**
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date: Created in 2023/1/9 13:38
 * @Version: 1.0
 */
public interface OutsourceWorkService extends IService<OutsourceWork> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param outsourceWork outsourceWork
     * @return IPage<specialCertificate>
     */
    IPage<OutsourceWork> findOutsourceWork(QueryRequest request, OutsourceWork outsourceWork);

    /**
     * 新增
     * @param outsourceWork outsourceWork
     * @throws IOException IOException
     */
    String createOutsourceWork(OutsourceWork outsourceWork);

    /**
     * 详情
     * @param id  id
     * @return
     */
    OutsourceWork findOutsourceWork(Long id);


    /**
     * 删除
     *
     */
    void deleteOutsourceWork(Long id);

    /**
     * 删除文件
     * @param id
     * @param filePath
     */
    void deleteFile(Long id, String filePath);
}
