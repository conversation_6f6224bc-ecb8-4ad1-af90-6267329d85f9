package com.example.eps.ins.service.impl;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.PutObjectRequest;
import com.github.tobato.fastdfs.domain.conn.FdfsWebServer;
import com.github.tobato.fastdfs.domain.fdfs.MetaData;
import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.domain.proto.storage.DownloadByteArray;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.service.FastDfsRetryService;
import com.example.eps.ins.service.FdfsClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class FdfsClientServiceImpl implements FdfsClientService {

    @Value("${fdfs.maxFileSize}")
    private long maxFileSize;

    @Value("${fdfs.maxFileSizeUnit}")
    private String maxFileSizeUnit;

    @Value("${oss.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.accessKeySecret}")
    private String accessKeySecret;
    @Value("${oss.bucket}")
    private String bucket;
    @Value("${oss.endpoint}")
    private String endpoint;

    @Autowired
    private FastFileStorageClient fastFileStorageClient;
    @Autowired
    private FdfsWebServer fdfsWebServer;
    @Autowired
    private FastDfsRetryService fastDfsRetryService;

    @Override
    public void uploadCheck(MultipartFile file) {
        double fileSize = (double) file.getSize();
        if ("K".equalsIgnoreCase(maxFileSizeUnit)) {
            fileSize = fileSize / 1024;
        } else if ("M".equalsIgnoreCase(maxFileSizeUnit)) {
            fileSize = fileSize / 1048576;
        } else if ("G".equalsIgnoreCase(maxFileSizeUnit)) {
            fileSize = fileSize / 1073741824;
        }

        if (fileSize > maxFileSize) {
            throw new EpsException(String.format("文件上传大小限制为%s%s以内", maxFileSize, maxFileSizeUnit));
        }
    }

    @Override
    public Map<String, Object> uploadFile(MultipartFile file) throws IOException {
        if (file != null) {
            byte[] bytes = file.getBytes();
            long fileSize = file.getSize();
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            return this.uploadFile(bytes, fileSize, originalFilename, extension);
        }
        return null;
    }

    @Override
    public Map<String, Object> uploadFile(byte[] bytes, long fileSize, String fileName, String extension) {
        try {
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
            Set<MetaData> metaDataSet = new HashSet<>();
            metaDataSet.add(new MetaData("dateTime", LocalDateTime.now().toString()));

            // 使用重试服务上传文件
            StorePath storePath = fastDfsRetryService.uploadFile(byteArrayInputStream, fileSize, extension, metaDataSet);

            Map<String, Object> fileMap = new HashMap<String, Object>() {{
                put("fileName", fileName);
                put("filePath", storePath.getFullPath());
                put("fileUrl", fdfsWebServer.getWebServerUrl() + storePath.getFullPath());
            }};

            log.info("文件上传成功: fileName={}, filePath={}", fileName, storePath.getFullPath());
            return fileMap;

        } catch (Exception e) {
            log.error("文件上传失败: fileName={}, error={}", fileName, e.getMessage(), e);
            throw new EpsException("文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] downloadFile(String filePath) {
        try {
            byte[] bytes = null;
            if (StringUtils.isNotBlank(filePath)) {
                String group = filePath.substring(0, filePath.indexOf("/"));
                String path = filePath.substring(filePath.indexOf("/") + 1);

                // 使用重试服务下载文件
                bytes = fastDfsRetryService.downloadFile(group, path);

                log.debug("文件下载成功: filePath={}, size={}字节", filePath, bytes != null ? bytes.length : 0);
            }
            return bytes;
        } catch (Exception e) {
            log.error("文件下载失败: filePath={}, error={}", filePath, e.getMessage(), e);
            throw new EpsException("文件下载失败: " + e.getMessage());
        }
    }

  @Override
  public void deleteFile(String filePath) {
      // 创建OSSClient实例。
      OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
      if (StringUtils.isNotBlank(filePath)) {
          try {
              //fastFileStorageClient.deleteFile(filePath);
             // 删除文件或目录。如果要删除目录，目录必须为空。
              ossClient.deleteObject(bucket, filePath);
          } finally {
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            }
        }
    }
    @Override
    public Map<String, Object> ossUploadFile(MultipartFile file) {
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            // 填写Byte数组。
            byte[] content = file.getBytes();
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, extension,
                    new ByteArrayInputStream(content));
            // 设置该属性可以返回response。如果不设置，则返回的response为空。
            putObjectRequest.setProcess("true");

            // 创建PutObject请求。
//            PutObjectResult result = ossClient.putObject(putObjectRequest);
            // 如果上传成功，则返回200。
//            System.out.println(result.getResponse().getStatusCode());
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        }catch (IOException e){
            e.printStackTrace();
        }finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return null;
    }
}
