package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.constant.CheckListConstant;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.Message;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.entity.system.MessageUserSee;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.*;
import com.example.eps.ins.service.MessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;
import static com.example.eps.ins.common.constant.RegionConstant.REGION_LEVEL_TOWN_UP;
import static com.example.eps.ins.common.entity.system.Message.MessageType.WARNING;
import static com.example.eps.ins.common.entity.system.Message.StatusType.EARLY_WARNING;
import static com.example.eps.ins.common.entity.system.Message.StatusType.MESSAGE_STATUS;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/31 16:59
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
@Slf4j
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message>
        implements MessageService {

    private final MessageMapper messageMapper;
    private final RegionMapper regionMapper;
    private final EnterpriseMapper enterpriseMapper;
    private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;
    private final WarningForCardMapper warningForCardMapper;
    private final MessageUserMapper messageUserMapper;
    private final MessageUserSeeMapper messageUserSeeMapper;

    @Override
    public IPage<Message> findMessages(QueryRequest request, Message dto) {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        //获取所有的企业申报信息
        List<EnterpriseDeclarationResponse> enterpriseDeclarationList =
                enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
        //获取所有申报状态
        List<Integer> collect = enterpriseDeclarationList.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
        //审核状态无历史通过记录
        if (ObjectUtils.isEmpty(enterpriseDeclarationList)){
            Page<Message> page = new Page<>(request.getPageNum(), request.getPageSize());
            page.setRecords(null);
            page.setTotal(0);
            return page;
        }
        if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
            Page<Message> page = new Page<>(request.getPageNum(), request.getPageSize());
            page.setRecords(null);
            page.setTotal(0);
            return page;
        }

        Enterprise enterprise = enterpriseMapper.selectOne(new QueryWrapper<Enterprise>()
                .eq("creator_id", userId));
        //企业所涉风险
        StringBuilder sql= new StringBuilder();
        List<Integer> numToString = getNumToString(enterprise.getRiskType());
        if (numToString.size()>0){
        for (Integer integer : numToString) {
                sql.append("or message_risk like concat('%',").append(integer).append(",'%')");
             }
        }
        //企业所处行业
        Long industryId = enterprise.getIndustryId();
        if (!ObjectUtils.isEmpty(industryId)){
            sql.append("or message_industry like concat('%',").append(industryId).append(",'%')");
        }
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(dto.getPublishTimeSlot()) && !dto.getPublishTimeSlot().isEmpty()) {
            if (StringUtils.isNotEmpty(dto.getPublishTimeSlot().get(0))
                    && StringUtils.isNotEmpty(dto.getPublishTimeSlot().get(1))) {
                queryWrapper.between(
                        Message::getPublishTime,
                        dto.getPublishTimeSlot().get(0) + CheckListConstant.START_TIME,
                        dto.getPublishTimeSlot().get(1) + CheckListConstant.END_TIME);
            }
        }
        if(!ObjectUtils.isEmpty(dto.getTitle())){
            queryWrapper.like(Message::getTitle,dto.getTitle());
        }
        if (!ObjectUtils.isEmpty(dto.getType())){
            queryWrapper.eq(Message::getType,dto.getType());
        }
        //所有企业可以查看的消息
//        queryWrapper.eq(Message::getMessageStatus,MESSAGE_STATUS);
        EnterpriseDeclarationResponse enterpriseDeclarationResponse =
                enterpriseDeclarationService.currentUserEnterpriseDeclarationSecond();
        Long parentId = regionMapper.selectById(enterpriseDeclarationResponse.getRegionTownId()).getParentId();
        queryWrapper.apply("("+parentId+"=create_region_id or "+enterpriseDeclarationResponse.getRegionTownId()
                +"=create_region_id or create_region_id = 1 )"+" and ( message_status="+MESSAGE_STATUS+" "+sql+")");
        queryWrapper.ne(Message::getMessageStatus,0);
        queryWrapper.orderByDesc(Message::getPublishTime);
        Page<Message> page = new Page<>(request.getPageNum(), request.getPageSize());
        Page<Message> page1 = this.page(page, queryWrapper);
        page1.getRecords().forEach(a->{
            MessageUser messageUser = messageUserMapper.selectOne(new QueryWrapper<MessageUser>().eq("message_id", a.getId())
                    .eq("user_id", userId).eq("type", CheckListConstant.MESSAGE_TYPE_ENTER));
            if (!ObjectUtils.isEmpty(messageUser)){
                a.setSeeStatus(messageUser.getStatus());
            }else {
                a.setSeeStatus(CheckListConstant.MESSAGE_OK);
            }
        });
        return page1;
    }

    @Override
    public Message findMessage(long id) {
        Message message = messageMapper.findMessage(id);
        List<Region> regions = messageMapper.findRegions(id);
        List<Long> regionIds = new ArrayList<>();
        regions =
                regions.stream()
                        .filter(region -> region.getLevel() < REGION_LEVEL_TOWN_UP)
                        .collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(regions)) {
            regionIds = regions.stream().map(Region::getId).collect(Collectors.toList());
        }
        message.setRegionIds(regionIds);
        message.setRegions(regions);
        //修改已读未读状态为已读
        Long userId = EpsUtil.getCurrentUser().getUserId();
        MessageUser messageUser = messageUserMapper.selectOne(new QueryWrapper<MessageUser>()
                .eq("message_id", id).eq("user_id", userId).eq("type", CheckListConstant.MESSAGE_TYPE_ENTER));
        if (!ObjectUtils.isEmpty(messageUser)){
            MessageUserSee messageUserSee=new MessageUserSee();
            messageUserSee.setId(messageUser.getId());
            messageUserSee.setMessageId(messageUser.getMessageId());
            messageUserSee.setUserId(messageUser.getUserId());
            messageUserSee.setStatus(CheckListConstant.MESSAGE_OK);
            messageUserSee.setType(messageUser.getType());
            messageUserSee.setRegionId(messageUser.getRegionId());
            messageUserSee.setCreateTime(messageUser.getCreateTime());
            messageUserSee.setEnterprisePhone(messageUser.getEnterprisePhone());
            messageUserSee.setEnterpriseName(messageUser.getEnterpriseName());
            messageUserSee.setRiskStatus(messageUser.getRiskStatus());
            messageUserSeeMapper.insert(messageUserSee);
            messageUserMapper.deleteById(messageUser);
        }
        return message;
    }

    @Override
    public List<WarningForCard> earlyWarningByUser() {
        Long regionId = EpsUtil.getCurrentUser().getUserId();
         return warningForCardMapper.selectList(new QueryWrapper<WarningForCard>()
                    .eq("remark_status", WARNING)
                 .eq("status",0)
                 .eq("create_id",regionId));
    }

    @Override
    public List<WarningForCard> warningByUser() {
        Long regionId = EpsUtil.getCurrentUser().getUserId();
        return warningForCardMapper.selectList(new QueryWrapper<WarningForCard>()
                    .in("remark_status", EARLY_WARNING)
                .eq("status",0)
                    .eq("create_id",regionId));
    }

    @Override
    public Integer findNoSee() {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        //获取所有的企业申报信息
        List<EnterpriseDeclarationResponse> enterpriseDeclarationList =
                enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
        //获取所有申报状态
        List<Integer> collect = enterpriseDeclarationList.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
        //审核状态无历史通过记录
        //审核状态无历史通过记录
        if (ObjectUtils.isEmpty(enterpriseDeclarationList)){
            return 0;
        }
        QueryWrapper<MessageUser> queryWrapper= new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        queryWrapper.eq("type", CheckListConstant.MESSAGE_TYPE_ENTER);
        List<MessageUser> messageUser = messageUserMapper.selectList(queryWrapper);
        return messageUser.size();
    }

    /**
     * 获取字符串中的数字并返回 String类型的list集合
     * 		例如：String a = "aher23nsdft234afn2345jasdfsdf";
     * 		返回：[23, 234, 2345] 注：类型为String
     *
     * @param a
     * @return List<String>
     */
    public List<Integer> getNumToString(String a) {

        List<Integer> ss = new ArrayList<Integer>();
        if (ObjectUtils.isEmpty(a)|| "".equals(a)){
            return ss;
        }
        for (String sss : a.replaceAll("[^0-9]", ",").split(",")) {
            if (sss.length() > 0) {
                ss.add(Integer.parseInt(sss));
            }
        }
        return ss;
    }
}
