package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.dto.device.DeviceBindingRequest;
import com.example.eps.ins.common.dto.device.DeviceBindingResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.device.EnterpriseDeviceBinding;

import java.util.List;

/**
 * 企业设备绑定关系 Service
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface IEnterpriseDeviceBindingService extends IService<EnterpriseDeviceBinding> {

    /**
     * 绑定设备到企业
     *
     * @param request 绑定请求
     * @param creatorId 创建人ID
     * @return 绑定结果
     */
    EnterpriseDeviceBinding bindDevice(DeviceBindingRequest request, Long creatorId);

    /**
     * 解绑设备
     *
     * @param bindingId 绑定关系ID
     * @return 解绑结果
     */
    boolean unbindDevice(Long bindingId);

    /**
     * 根据企业ID和外部设备ID查询绑定关系
     *
     * @param enterpriseId 企业ID
     * @param externalDeviceId 外部设备ID
     * @return 绑定关系
     */
    EnterpriseDeviceBinding findByEnterpriseIdAndExternalDeviceId(Long enterpriseId, Long externalDeviceId);

    /**
     * 根据企业ID查询绑定的设备列表
     *
     * @param enterpriseId 企业ID
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> findByEnterpriseId(Long enterpriseId);

    /**
     * 根据外部设备ID查询绑定关系列表
     *
     * @param externalDeviceId 外部设备ID
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> findByExternalDeviceId(Long externalDeviceId);

    /**
     * 根据设备ID查询绑定关系列表
     *
     * @param deviceId 设备ID
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> findByDeviceId(Long deviceId);

    /**
     * 分页查询绑定关系列表（带详细信息）
     *
     * @param request 分页请求
     * @param binding 查询条件
     * @return 绑定关系分页列表
     */
    IPage<DeviceBindingResponse> findBindingsWithDetails(QueryRequest request, EnterpriseDeviceBinding binding);

    /**
     * 根据企业ID和绑定状态查询绑定关系列表
     *
     * @param enterpriseId 企业ID
     * @param bindingStatus 绑定状态
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> findByEnterpriseIdAndStatus(Long enterpriseId, Integer bindingStatus);

    /**
     * 根据绑定状态查询绑定关系列表
     *
     * @param bindingStatus 绑定状态
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> findByBindingStatus(Integer bindingStatus);

    /**
     * 批量保存绑定关系
     *
     * @param bindingList 绑定关系列表
     * @return 保存结果
     */
    boolean batchSaveBindings(List<EnterpriseDeviceBinding> bindingList);

    /**
     * 更新绑定状态
     *
     * @param bindingId 绑定关系ID
     * @param bindingStatus 绑定状态
     * @return 更新结果
     */
    boolean updateBindingStatus(Long bindingId, Integer bindingStatus);

    /**
     * 根据企业ID统计绑定设备数量
     *
     * @param enterpriseId 企业ID
     * @return 绑定设备数量
     */
    int countByEnterpriseId(Long enterpriseId);

    /**
     * 根据企业ID和绑定状态统计设备数量
     *
     * @param enterpriseId 企业ID
     * @param bindingStatus 绑定状态
     * @return 设备数量
     */
    int countByEnterpriseIdAndStatus(Long enterpriseId, Integer bindingStatus);

    /**
     * 检查设备是否已被其他企业绑定
     *
     * @param externalDeviceId 外部设备ID
     * @param excludeEnterpriseId 排除的企业ID（可选）
     * @return 是否已被绑定
     */
    boolean isDeviceAlreadyBound(Long externalDeviceId, Long excludeEnterpriseId);

    /**
     * 验证企业是否存在
     *
     * @param enterpriseId 企业ID
     * @return 是否存在
     */
    boolean validateEnterpriseExists(Long enterpriseId);

    /**
     * 验证外部设备是否存在
     *
     * @param externalDeviceId 外部设备ID
     * @return 是否存在
     */
    boolean validateExternalDeviceExists(Long externalDeviceId);
}
