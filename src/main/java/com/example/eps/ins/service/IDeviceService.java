package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.dto.device.DeviceInfo;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.device.Device;

import java.util.List;

/**
 * 设备信息 Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IDeviceService extends IService<Device> {

    /**
     * 分页查询设备列表
     *
     * @param request 分页请求
     * @param device 查询条件
     * @return 设备分页列表
     */
    IPage<Device> findDevices(QueryRequest request, Device device);

    /**
     * 根据外部设备ID查询设备
     *
     * @param externalDeviceId 外部设备ID
     * @return 设备信息
     */
    Device findByExternalDeviceId(Long externalDeviceId);

    /**
     * 根据序列号查询设备
     *
     * @param serial 设备序列号
     * @return 设备信息
     */
    Device findBySerial(String serial);

    /**
     * 同步设备信息到数据库（包含子设备）
     *
     * @param deviceInfoList 设备信息列表
     * @return 同步结果
     */
    boolean syncDevices(List<DeviceInfo> deviceInfoList);

    /**
     * 同步设备信息到数据库（包含子设备）
     *
     * @param deviceInfoList 设备信息列表
     * @param syncSubDevices 是否同步子设备
     * @return 同步结果
     */
    boolean syncDevices(List<DeviceInfo> deviceInfoList, boolean syncSubDevices);

    /**
     * 保存或更新单个设备信息
     *
     * @param deviceInfo 设备信息
     * @return 保存的设备实体
     */
    Device saveOrUpdateDevice(DeviceInfo deviceInfo);

    /**
     * 批量保存设备信息
     *
     * @param deviceList 设备列表
     * @return 保存结果
     */
    boolean batchSaveDevices(List<Device> deviceList);

    /**
     * 根据同步状态查询设备列表
     *
     * @param syncStatus 同步状态
     * @return 设备列表
     */
    List<Device> findBySyncStatus(Integer syncStatus);

    /**
     * 更新设备同步状态
     *
     * @param deviceId 设备ID
     * @param syncStatus 同步状态
     * @return 更新结果
     */
    boolean updateSyncStatus(Long deviceId, Integer syncStatus);
}
