package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.TrainEducation;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.TrainEducationMapper;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.TrainEducationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/** @Author: Zhanghongyin @Date: Created in 2022/10/11 9:52 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class TrainEducationServiceImpl extends ServiceImpl<TrainEducationMapper, TrainEducation>
    implements TrainEducationService {

  private final FdfsClientService fdfsClientService;
  private final TrainEducationMapper trainEducationMapper;
  private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

  @Override
  public IPage<TrainEducation> findTrainEducations(
      QueryRequest request, TrainEducation trainEducation) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    if (!ObjectUtils.isEmpty(trainEducation.getCreatorId())){
      userId=trainEducation.getCreatorId();
    }
    LambdaQueryWrapper<TrainEducation> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(TrainEducation::getCreatorId, userId);
    if (!ObjectUtils.isEmpty(trainEducation.getTrainTheme())) {
      queryWrapper.like(
              TrainEducation::getTrainTheme, trainEducation.getTrainTheme());
    }
    queryWrapper.orderByDesc(TrainEducation::getCreateTime);
    Page<TrainEducation> page = new Page<>(request.getPageNum(), request.getPageSize());
    return this.page(page, queryWrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String createTrainEducation(TrainEducation trainEducation){
    //获取所有的企业申报信息
    List<EnterpriseDeclarationResponse> enterprise =
            enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
    //获取所有申报状态
    List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
    if (ObjectUtils.isEmpty(enterprise)){
      throw new EpsException("请完善企业信息后创建企业安全培训教育信息！");
    }
    if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
      throw new EpsException("企业信息暂未审核通过，请审核通过后创建企业安全培训教育信息！");
    }
    // 保存消息
    TrainEducation education = new TrainEducation();
    education.setTrainTheme(trainEducation.getTrainTheme());
    education.setTrainContent(trainEducation.getTrainContent());
    if (ObjectUtils.isEmpty(trainEducation.getTrainImage())){
      education.setTrainImage("[]");
    } else {
      education.setTrainImage(trainEducation.getTrainImage());
    }
    if (ObjectUtils.isEmpty(trainEducation.getTrainFile())){
      education.setTrainFile("[]");
    } else {
      education.setTrainFile(trainEducation.getTrainFile());
    }
    education.setTrainDate(trainEducation.getTrainDate());
    education.setCreateTime(new Date());
    if (ObjectUtils.isEmpty(trainEducation.getId())) {
      CurrentUser currentUser = EpsUtil.getCurrentUser();
      education.setRegionId(enterprise.get(0).getRegionTownId());
      education.setCreatorId(currentUser.getUserId());
      this.save(education);
    } else {
      education.setCreatorId(trainEducation.getCreatorId());
      this.update(
              education, new QueryWrapper<TrainEducation>().eq("id", trainEducation.getId()));
    }
    return "";
  }

  @Override
  public TrainEducation findTrainEducation(Long id) {
    return trainEducationMapper.selectById(id);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteTrainEducation(Long id) {
    LambdaQueryWrapper<TrainEducation> userWarpper = new LambdaQueryWrapper<>();
    userWarpper.eq(TrainEducation::getId, id);
    TrainEducation education = this.getById(id);
    if (education == null) {
      return;
    }
    JSONArray jsonFileArray = JSON.parseArray(education.getTrainFile());
    JSONArray jsonImageArray = JSON.parseArray(education.getTrainImage());
    trainEducationMapper.delete(userWarpper);
    for (Object obj : jsonFileArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
    for (Object obj : jsonImageArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
  }

  @Override
  public void deleteFile(Long id, String filePath) {
    if (!ObjectUtils.isEmpty(id)) {
      LambdaQueryWrapper<TrainEducation> userWarpper = new LambdaQueryWrapper<>();
      userWarpper.eq(TrainEducation::getId, id);
      TrainEducation education = trainEducationMapper.selectById(id);
      if (ObjectUtils.isEmpty(education)) {
        return;
      }
      JSONArray fileArray = JSON.parseArray(education.getTrainFile());
      JSONArray imageArray = JSON.parseArray(education.getTrainImage());
      JSONArray fileJson = getArray(filePath, fileArray);
      JSONArray imageJson = getArray(filePath, imageArray);
      education.setTrainFile(fileJson.toString());
      education.setTrainImage(imageJson.toString());
      trainEducationMapper.updateById(education);
    }
    fdfsClientService.deleteFile(filePath);
  }

  private JSONArray getArray(String filePath, JSONArray fileArray) {
    JSONArray json = new JSONArray();
    for (Object obj : fileArray) {
      String filePath1 = ((JSONObject) obj).getString("filePath");
      if (filePath1.equals(filePath)){
        continue;
      }
      Map<String, Object> map=new HashMap<>();
      map.put("fileUrl",((JSONObject) obj).getString("fileUrl"));
      map.put("filePath",((JSONObject) obj).getString("filePath"));
      map.put("fileName",((JSONObject) obj).getString("fileName"));
      json.add(EpsUtil.toJsonObj(map));
    }
    return json;
  }
}
