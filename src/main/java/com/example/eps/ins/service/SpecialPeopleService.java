package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.SpecialPeople;

import java.io.IOException;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2022/10/11 9:51
 * @Version: 1.0
 */

public interface SpecialPeopleService extends IService<SpecialPeople> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param emergencyPlan enterpriseRisk
     * @return IPage<EmergencyPlan>
     */
    IPage<SpecialPeople> findSpecialPeoples(QueryRequest request, SpecialPeople emergencyPlan);

    /**
     * 新增
     * @param emergencyPlan emergencyPlan
     * @throws IOException IOException
     */
    String createSpecialPeople(SpecialPeople emergencyPlan);

    /**
     * 详情
     * @param id  id
     * @return
     */
    SpecialPeople findSpecialPeople(Long id);


    /**
     * 删除
     *
     */
    void deleteSpecialPeople(Long id);

//    /**
//     * 删除文件
//     * @param id
//     * @param filePath
//     */
//    void deleteFile(Long id, String filePath);
}
