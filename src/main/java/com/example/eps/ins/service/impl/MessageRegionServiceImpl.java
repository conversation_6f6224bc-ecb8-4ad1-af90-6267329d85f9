package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.MessageRegion;
import com.example.eps.ins.mapper.MessageRegionMapper;
import com.example.eps.ins.service.IMessageRegionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Description: <消息地区表服务实现类>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class MessageRegionServiceImpl extends ServiceImpl<MessageRegionMapper, MessageRegion> implements IMessageRegionService {

    private final MessageRegionMapper iMessageRegionMapper;

    @Override
    public IPage<MessageRegion> findMessageRegions(QueryRequest request, MessageRegion dto) {
        LambdaQueryWrapper<MessageRegion> queryWrapper = new LambdaQueryWrapper<>();
        // TODO 设置查询条件
        Page<MessageRegion> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createMessageRegion(MessageRegion dto) {
        this.save(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMessageRegion(MessageRegion dto) {
        this.saveOrUpdate(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMessageRegion(MessageRegion dto) {
        LambdaQueryWrapper<MessageRegion> wapper = new LambdaQueryWrapper<>();
        // TODO 设置删除条件
        this.remove(wapper);
    }
}