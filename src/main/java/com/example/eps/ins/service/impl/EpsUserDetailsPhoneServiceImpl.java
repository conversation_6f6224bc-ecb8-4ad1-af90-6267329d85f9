package com.example.eps.ins.service.impl;

import com.example.eps.ins.auth.component.UserDetailTypeEnum;
import com.example.eps.ins.common.entity.EnterpriseAuthUser;
import com.example.eps.ins.common.entity.enterprise.EnterpriseUser;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.service.RedisService;
import com.example.eps.ins.service.EpsUserDetailsCanProcessor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * 企业端用户获取逻辑.
 */
@Service("phoneUserDetailsService")
@RequiredArgsConstructor
public class EpsUserDetailsPhoneServiceImpl implements UserDetailsService, EpsUserDetailsCanProcessor {
    private final EnterpriseUserServiceImpl enterpriseUserService;
    private final RedisService redisService;
    private final ValidateCodeServiceImpl smsValidateCodeService;
    protected final Log logger = LogFactory.getLog(getClass());

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        EnterpriseUser enterpriseUser = new EnterpriseUser();
        enterpriseUser.setMobile(username);
        List<EnterpriseUser> list = enterpriseUserService.findEnterpriseUsers(enterpriseUser);
//        if (list == null || list.size() == 0) {
//            enterpriseUserService.createEnterpriseUser(enterpriseUser);
//            list = enterpriseUserService.findEnterpriseUsers(enterpriseUser);
//        }
//        return User.builder().username(list.get(0).getCreditCode())
//                .password(list.get(0).getPassword()).authorities(AuthorityUtils.NO_AUTHORITIES).build();
        if (ObjectUtils.isEmpty(list)){
            throw new EpsException("用户未注册!");
        }
        EnterpriseUser eu = list.get(0);
        if (ObjectUtils.isEmpty(eu.getEnterpriseName())){
            eu.setEnterpriseName(eu.getMobile());
        }
        if(ObjectUtils.isEmpty(eu.getPassword())){
            throw new EpsException("登录失败,请点击忘记密码按钮重置密码!");
        }
        EnterpriseAuthUser authUser = new EnterpriseAuthUser(eu.getEnterpriseName(), eu.getPassword(),
                AuthorityUtils.NO_AUTHORITIES);
        BeanUtils.copyProperties(eu, authUser);
        return authUser;
    }

    @Override
    public boolean canProcessor(UserDetailTypeEnum userDetailTypeEnum) {
        return userDetailTypeEnum == UserDetailTypeEnum.ENTERPRISE_PHONE;
    }
}
