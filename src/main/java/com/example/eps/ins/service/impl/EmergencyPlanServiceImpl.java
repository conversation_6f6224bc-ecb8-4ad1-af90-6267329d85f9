package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EmergencyPlan;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.EmergencyPlanMapper;
import com.example.eps.ins.mapper.EnterpriseMapper;
import com.example.eps.ins.common.dto.enterprise.EmergencyPlanVo;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.IEmergencyPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/** @Author: Zhanghongyin @Date: Created in 2022/10/11 9:52 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class EmergencyPlanServiceImpl extends ServiceImpl<EmergencyPlanMapper, EmergencyPlan>
    implements IEmergencyPlanService {
  private static final int PLAN_OK = 1;// 已建立应急预案
  private static final int PLAN_NO = 0;// 未建立应急预案
  private final FdfsClientService fdfsClientService;
  private final EmergencyPlanMapper emergencyPlanMapper;
  private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;
  private final EnterpriseMapper enterpriseMapper;

  @Override
  public IPage<EmergencyPlan> findEmergencyPlans(
      QueryRequest request, EmergencyPlan emergencyPlan) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    if (!ObjectUtils.isEmpty(emergencyPlan.getCreatorId())){
      userId=emergencyPlan.getCreatorId();
    }
    LambdaQueryWrapper<EmergencyPlan> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(EmergencyPlan::getCreatorId, userId);
    if (!ObjectUtils.isEmpty(emergencyPlan.getEmergencyPlanName())) {
      queryWrapper.like(EmergencyPlan::getEmergencyPlanName, emergencyPlan.getEmergencyPlanName());
    }
    queryWrapper.orderByDesc(EmergencyPlan::getCreateTime);
    Page<EmergencyPlan> page = new Page<>(request.getPageNum(), request.getPageSize());
    return this.page(page, queryWrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String createEmergencyPlan(EmergencyPlanVo emergencyPlanVo) {
    //获取所有的企业申报信息
    List<EnterpriseDeclarationResponse> enterprise =
            enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
    //获取所有申报状态
    List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
    if (ObjectUtils.isEmpty(enterprise)){
      throw new EpsException("请完善企业信息后创建企业应急预案信息！");
    }
    if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
      throw new EpsException("企业信息暂未审核通过，请审核通过后创建企业应急预案信息！");
    }
    // 保存消息
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    EmergencyPlan emergencyPlan = new EmergencyPlan();
    emergencyPlan.setEmergencyPlanName(emergencyPlanVo.getEmergencyPlanName());
    emergencyPlan.setPlanType(emergencyPlanVo.getPlanType());
    if (ObjectUtils.isEmpty(emergencyPlanVo.getEmergencyPlanFile())){
      emergencyPlan.setEmergencyPlanFile("[]");
    } else {
      emergencyPlan.setEmergencyPlanFile(emergencyPlanVo.getEmergencyPlanFile());
    }
    emergencyPlan.setDigest(emergencyPlanVo.getDigest());
    emergencyPlan.setCreateTime(new Date());
    if (ObjectUtils.isEmpty(emergencyPlanVo.getId())) {
      emergencyPlan.setCreatorId(currentUser.getUserId());
      emergencyPlan.setRegionId(enterprise.get(0).getRegionTownId());
      this.save(emergencyPlan);
    } else {
      emergencyPlan.setCreatorId(emergencyPlanVo.getCreatorId());
      this.update(emergencyPlan, new QueryWrapper<EmergencyPlan>()
              .eq("id", emergencyPlanVo.getId()));
    }
    //修改单位应急预案和隐患信息
    enterpriseMapper.updateHiddenStatus(emergencyPlanVo.getCreatorId());
    return "";
  }

  @Override
  public EmergencyPlan findEmergencyPlan(Long id) {
    return emergencyPlanMapper.selectById(id);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteEmergencyPlan(Long id) {
    LambdaQueryWrapper<EmergencyPlan> userWarpper = new LambdaQueryWrapper<>();
    userWarpper.eq(EmergencyPlan::getId, id);
    EmergencyPlan emergencyPlan = this.getById(id);
    if (emergencyPlan == null) {
      return;
    }
    JSONArray jsonArray = JSON.parseArray(emergencyPlan.getEmergencyPlanFile());
    emergencyPlanMapper.delete(userWarpper);
    for (Object obj : jsonArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    List<EmergencyPlan> creatorId = emergencyPlanMapper.selectList(new QueryWrapper<EmergencyPlan>()
            .eq("creator_id", currentUser.getUserId()));
    // 修改单位应急预案和隐患信息
    if (ObjectUtils.isEmpty(creatorId)) {
      Enterprise updateEnter =
          enterpriseMapper.selectOne(
              new QueryWrapper<Enterprise>().eq("creator_id", currentUser.getUserId()));
      if (!ObjectUtils.isEmpty(updateEnter)) {
        updateEnter.setPlanStatus(PLAN_NO);
        enterpriseMapper.updateById(updateEnter);
      }
    }
  }

  @Override
  public void deleteFile(Long id, String filePath) {
    if (!ObjectUtils.isEmpty(id)) {
      EmergencyPlan emergencyPlan = emergencyPlanMapper.selectById(id);
      if (ObjectUtils.isEmpty(emergencyPlan)) {
        return;
      }
      JSONArray jsonArray = JSON.parseArray(emergencyPlan.getEmergencyPlanFile());
      JSONArray json = new JSONArray();
      for (Object obj : jsonArray) {
        String filePath1 = ((JSONObject) obj).getString("filePath");
        if (filePath1.equals(filePath)) {
          continue;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("fileUrl", ((JSONObject) obj).getString("fileUrl"));
        map.put("filePath", ((JSONObject) obj).getString("filePath"));
        map.put("fileName", ((JSONObject) obj).getString("fileName"));
        json.add(EpsUtil.toJsonObj(map));
      }
      emergencyPlan.setEmergencyPlanFile(json.toString());
      emergencyPlanMapper.updateById(emergencyPlan);
    }
    fdfsClientService.deleteFile(filePath);
  }
}
