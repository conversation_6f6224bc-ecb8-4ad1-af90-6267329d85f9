package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.FiniteSpaceWork;

import java.io.IOException;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2023/1/9 10:41
 * @Version: 1.0
 */
public interface FiniteSpaceWorkService extends IService<FiniteSpaceWork> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param finiteSpaceWork finiteSpaceWork
     * @return IPage<finiteSpaceWork>
     */
    IPage<FiniteSpaceWork> findFiniteSpaceWorks(QueryRequest request, FiniteSpaceWork finiteSpaceWork);

    /**
     * 新增
     * @param finiteSpaceWork finiteSpaceWork
     * @throws IOException IOException
     */
    String createFiniteSpaceWork(FiniteSpaceWork finiteSpaceWork);

    /**
     * 详情
     * @param id  id
     * @return
     */
    FiniteSpaceWork findFiniteSpaceWork(Long id);


    /**
     * 删除
     *
     */
    void deleteFiniteSpaceWork(Long id);

    /**
     * 删除文件
     * @param id
     * @param filePath
     */
    void deleteFile(Long id, String filePath);
}
