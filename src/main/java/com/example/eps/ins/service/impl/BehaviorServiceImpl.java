package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.constant.NumberConstant;
import com.example.eps.ins.common.dto.report.req.BehaviorReq;
import com.example.eps.ins.common.dto.report.req.EnforcementReq;
import com.example.eps.ins.common.dto.report.resp.BehaviorResp;
import com.example.eps.ins.common.dto.report.resp.EnforcementResp;
import com.example.eps.ins.common.dto.report.resp.YearCheckListResp;
import com.example.eps.ins.mapper.BehaviorMapper;
import com.example.eps.ins.mapper.RegionMapper;
import com.example.eps.ins.service.BehaviorService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.List;

/**
 * @Author: zhanghongyin @Date: Created in 2021/12/1
 * 17:05 @Description:BehaviorServiceImpl @Version: 1.0
 */
@Service
@RequiredArgsConstructor
public class BehaviorServiceImpl implements BehaviorService {
  private final BehaviorMapper behaviorMapper;
  private final RegionMapper regionMapper;

  @Override
  public List<BehaviorResp> getSort(BehaviorReq behaviorReq) {
    Long userRegionId = EpsUtil.getCurrentUser().getRegionId();
    Long regionId = behaviorReq.getRegionId();
    // 获取当前登录人该地址所属地区
    Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
    // 获取参数地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    //如果是市级
    if (region.getLevel().equals(NumberConstant.LEVE_ONE)) {
      NumberConstant.regionCompareUserRegion(userRegion,region);
      return behaviorMapper.selectByCheckId( null,behaviorReq.getYear(), behaviorReq.getSort());
    }else if (region.getLevel().equals(NumberConstant.LEVE_TWO)){
      NumberConstant.regionCompareUserRegion(userRegion,region);
      //获取辖区内镇街
      List<Long> regions = regionMapper.selectSonId(region.getId());
      regions.add(region.getId());
      return behaviorMapper.selectLiveTwo(regions,behaviorReq.getYear(),behaviorReq.getSort());
    } else {
      // 获取所有符合条件的数据
      List<BehaviorResp> behaviorReps =
          behaviorMapper.selectByCheckId(regionId, behaviorReq.getYear(), behaviorReq.getSort());
      // 删除集合中的空元素
      behaviorReps.removeAll(Collections.singleton(null));
      return behaviorReps;
    }
  }

  @Override
  public EnforcementResp getAll(EnforcementReq behaviorReq) {
    Long regionId = behaviorReq.getRegionId();
    Long userRegionId = EpsUtil.getCurrentUser().getRegionId();
    // 获取当前登录人该地址所属地区
    Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
    // 获取参数地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));

    EnforcementResp rectifyAll;
    EnforcementResp enforcementAll;
    //如果是市级
    if (region.getLevel().equals(NumberConstant.LEVE_ONE)) {
      NumberConstant.regionCompareUserRegion(userRegion,region);
      enforcementAll =
              behaviorMapper.getDataViewAll(behaviorReq.getYear(), null);
      rectifyAll = behaviorMapper.getRectifyAll(behaviorReq.getYear(), null);
    }else if (region.getLevel().equals(NumberConstant.LEVE_TWO)){
      NumberConstant.regionCompareUserRegion(userRegion,region);
      //获取辖区内镇街
      List<Long> regions = regionMapper.selectSonId(region.getId());
      regions.add(region.getId());
      enforcementAll =
              behaviorMapper.getDataViewOrTwoLive(behaviorReq.getYear(), regions);
      rectifyAll = behaviorMapper.getRectifyOrTwoLive(behaviorReq.getYear(), regions);
    } else {
      // 区县执法数据
      enforcementAll =
          behaviorMapper.getDataViewAll(behaviorReq.getYear(), regionId);
      rectifyAll = behaviorMapper.getRectifyAll(behaviorReq.getYear(), regionId);
    }
    if (ObjectUtils.isEmpty(rectifyAll)){
      enforcementAll.setNoRectify(NumberConstant.ZERO);
      enforcementAll.setYesRectify(NumberConstant.ZERO);
      enforcementAll.setStartRectify(NumberConstant.ZERO);
      enforcementAll.setRectifyNumber(NumberConstant.ZERO);
      return enforcementAll;
    }
    enforcementAll.setNoRectify(rectifyAll.getNoRectify());
    enforcementAll.setYesRectify(rectifyAll.getYesRectify());
    enforcementAll.setStartRectify(rectifyAll.getStartRectify());
    enforcementAll.setRectifyNumber(rectifyAll.getRectifyNumber());
    return enforcementAll;
  }

  @Override
  public YearCheckListResp getEnforcementList(EnforcementReq behaviorReq) {
    Long regionId = behaviorReq.getRegionId();
    Long userRegionId = EpsUtil.getCurrentUser().getRegionId();
    // 获取当前登录人该地址所属地区
    Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
    // 获取参数地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    //如果是市级
    if (region.getLevel().equals(NumberConstant.LEVE_ONE)) {
      NumberConstant.regionCompareUserRegion(userRegion,region);
      return behaviorMapper.getCityAll(behaviorReq.getYear(), null);
    }else if (region.getLevel().equals(NumberConstant.LEVE_TWO)){
      NumberConstant.regionCompareUserRegion(userRegion,region);
      //获取辖区内镇街
      List<Long> regions = regionMapper.selectSonId(region.getId());
      regions.add(region.getId());
      return behaviorMapper.getCityAllOrTwoLive(behaviorReq.getYear(), regions);
    } else {
      // 获取所有符合条件的数据
      return behaviorMapper.getCityAll(behaviorReq.getYear(), regionId);
    }
  }
}
