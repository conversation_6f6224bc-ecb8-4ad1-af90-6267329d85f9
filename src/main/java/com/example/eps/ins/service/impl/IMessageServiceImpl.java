package com.example.eps.ins.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.MessageRequest;
import com.example.eps.ins.common.constant.SystemConstant;
import com.example.eps.ins.common.dto.report.model.MessageVo;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.Message;
import com.example.eps.ins.common.entity.system.MessageRegion;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.entity.system.MessageUserSee;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.*;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.IMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.CheckListConstant.*;
import static com.example.eps.ins.common.constant.RegionConstant.REGION_LEVEL_TOWN;
import static com.example.eps.ins.common.constant.RegionConstant.REGION_LEVEL_TOWN_UP;
import static com.example.eps.ins.common.entity.system.Message.StatusType.MASSAGE_RELEASE;

/** @Description: <消息通知表服务实现类> @Author: utopia @CreateDate: 2021-11-09 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
@Slf4j
public class IMessageServiceImpl extends ServiceImpl<MessageMapper, Message>
    implements IMessageService {

  private static final int TIME_SIZE = 2;
  //企业
  private static final Integer MESSAGE_TYPE_ENTERPRISE = 2;
  private final MessageRegionServiceImpl messageRegionService;
  private final FdfsClientService fdfsClientService;
  private final MessageMapper messageMapper;
  private final MessageRegionMapper messageRegionMapper;
  private final MessageUserMapper messageUserMapper;
  private final MessageUserSeeMapper messageUserSeeMapper;
  private final RegionMapper regionMapper;
  private final WarningForCardMapper warningForCardMapper;
  private final EnterpriseDeclarationMapper enterpriseDeclarationMapper;
  private final UserMapper userMapper;
  private final EnterpriseMapper enterpriseMapper;
  private final EnterpriseUserMapper enterpriseUserMapper;

  @Override
  public IPage<Message> findMessagesByCurrentUser(MessageRequest request) {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    Region region = regionMapper.selectById(currentUser.getRegionId());
    Long regionId = region.getId();
    if (region.getLevel() == REGION_LEVEL_TOWN) {
      regionId = region.getParentId();
    }
    request.setRegionId(regionId);
    request.setPageNum((request.getPageNum() - 1) * request.getPageSize());
    if (Objects.nonNull(request.getPublishTimeSlot())
        && request.getPublishTimeSlot().size() == TIME_SIZE) {
      String startTime = request.getPublishTimeSlot().get(0);
      String endTime = request.getPublishTimeSlot().get(1);
      if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
        request.getPublishTimeSlot().set(0, startTime + START_TIME);
        request.getPublishTimeSlot().set(1, endTime + END_TIME);
      }
    }
    List<Message> messages = messageMapper.findByPage(request);
    messages.forEach(a->{
      MessageUser messageUser = messageUserMapper.selectOne(new QueryWrapper<MessageUser>()
              .eq("message_id", a.getId())
              .eq("user_id", currentUser.getUserId()).eq("type", MESSAGE_TYPE));
      if (!ObjectUtils.isEmpty(messageUser)){
        a.setSeeStatus(messageUser.getStatus());
      }else {
        a.setSeeStatus(MESSAGE_OK);
      }
    });
    long total = messageMapper.count(request);
    IPage<Message> page = new Page();
    page.setTotal(total);
    page.setRecords(messages);
    return page;
  }

  @Override
  public IPage<Message> findMessages(QueryRequest request, Message dto) {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
    if (!ObjectUtils.isEmpty(dto.getType())){
      queryWrapper.eq(Message::getType,dto.getType());
    }
    if (Objects.nonNull(dto.getPublishTimeSlot()) && !dto.getPublishTimeSlot().isEmpty()) {
      if (StringUtils.isNotEmpty(dto.getPublishTimeSlot().get(0))
          && StringUtils.isNotEmpty(dto.getPublishTimeSlot().get(1))) {
        queryWrapper.between(
            Message::getPublishTime,
            dto.getPublishTimeSlot().get(0) + START_TIME,
            dto.getPublishTimeSlot().get(1) + END_TIME);
      }
    }
    if (dto.getPageType().equals(MASSAGE_RELEASE)) {
      //消息发布页面
      queryWrapper.eq(Message::getCreateRegionId, currentUser.getRegionId());
      if(!ObjectUtils.isEmpty(dto.getTitle())){
        queryWrapper.like(Message::getTitle,dto.getTitle());
      }
      queryWrapper.orderByDesc(Message::getPublishTime);
      Page<Message> page = new Page<>(request.getPageNum(), request.getPageSize());
      Page<Message> page1 = this.page(page, queryWrapper);
      page1.getRecords().forEach(a->{
        List<MessageUser> messageUser = messageUserMapper.selectList(new QueryWrapper<MessageUser>()
                .eq("message_id", a.getId()));
        a.setNoSeeNum(messageUser.size());
      });
      return page1;
    } else {
      //消息查看页面
      List<MessageRegion> messageRegions =
          messageRegionMapper.selectList(
              new LambdaQueryWrapper<MessageRegion>()
                  .eq(MessageRegion::getRegionId, currentUser.getRegionId()));
      List<Long> collect =
          messageRegions.stream().map(MessageRegion::getMessageId).collect(Collectors.toList());
      queryWrapper.in(Message::getId, collect);
      if (ObjectUtils.isEmpty(collect)){
        Page<Message> page = new Page<>(request.getPageNum(), request.getPageSize());
        page.setRecords(null);
        page.setTotal(0);
        return page;
      }
      if(!ObjectUtils.isEmpty(dto.getTitle())){
        queryWrapper.like(Message::getTitle,dto.getTitle());
      }
      queryWrapper.orderByDesc(Message::getPublishTime);
      Page<Message> page = new Page<>(request.getPageNum(), request.getPageSize());
      Page<Message> page1 = this.page(page, queryWrapper);
      page1.getRecords().forEach(a->{
        MessageUser messageUser = messageUserMapper.selectOne(new QueryWrapper<MessageUser>().eq("message_id", a.getId())
                .eq("user_id", currentUser.getUserId()).eq("type", MESSAGE_TYPE));
        if (!ObjectUtils.isEmpty(messageUser)){
          a.setSeeStatus(messageUser.getStatus());
        }else {
          a.setSeeStatus(MESSAGE_OK);
        }
      });
      return page1;
    }
  }

  @Override
  public Message findMessage(long id) {
    Message message = messageMapper.findMessage(id);
    List<Region> regions = messageMapper.findRegions(id);
    List<Long> regionIds = new ArrayList<>();
    regions =
        regions.stream()
            .filter(region -> region.getLevel() < REGION_LEVEL_TOWN_UP)
            .collect(Collectors.toList());
    if (!ObjectUtils.isEmpty(regions)) {
      regionIds = regions.stream().map(Region::getId).collect(Collectors.toList());
    }
    message.setRegionIds(regionIds);
    message.setRegions(regions);
    //修改已读未读状态为已读
    Long userId = EpsUtil.getCurrentUser().getUserId();
    MessageUser messageUser = messageUserMapper.selectOne(new QueryWrapper<MessageUser>()
            .eq("message_id", id)
            .eq("user_id", userId)
            .eq("type", MESSAGE_TYPE));
    if (!ObjectUtils.isEmpty(messageUser)){
      MessageUserSee messageUserSee=new MessageUserSee();
      messageUserSee.setId(messageUser.getId());
      messageUserSee.setMessageId(messageUser.getMessageId());
      messageUserSee.setUserId(messageUser.getUserId());
      messageUserSee.setStatus(MESSAGE_OK);
      messageUserSee.setType(messageUser.getType());
      messageUserSee.setRegionId(messageUser.getRegionId());
      messageUserSee.setCreateTime(messageUser.getCreateTime());
      messageUserSee.setEnterprisePhone(messageUser.getEnterprisePhone());
      messageUserSee.setEnterpriseName(messageUser.getEnterpriseName());
      messageUserSee.setRiskStatus(messageUser.getRiskStatus());
      messageUserSeeMapper.insert(messageUserSee);
      messageUserMapper.deleteById(messageUser);
    }
    return message;
  }

  @Override
  public Map<String, List<Message>> findMessageByCurrentUser() {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    Region region = regionMapper.selectById(currentUser.getRegionId());
    Long regionId = region.getId();
    if (region.getLevel() == REGION_LEVEL_TOWN) {
      regionId = region.getParentId();
    }
    Map<String, List<Message>> messages = new HashMap<>();
    messages.put(
        Message.MessageType.ADVICE,
        messageMapper.findMessages(regionId, Message.MessageType.ADVICE));
    messages.put(
        Message.MessageType.NOTICE,
        messageMapper.findMessages(regionId, Message.MessageType.NOTICE));
    return messages;
  }

  @Override
  public IPage<MessageUser> findNoSee(QueryRequest request, MessageUser messageUser) {
    Page<MessageUser> page = new Page<>(request.getPageNum(), request.getPageSize());
    QueryWrapper<MessageUser> queryWrapper= new QueryWrapper<>();
    queryWrapper.eq("message_id", messageUser.getMessageId());
    if (!ObjectUtils.isEmpty(messageUser.getType()) &&
            !messageUser.getType().equals(MESSAGE_TYPE_ENTERPRISE)){
      messageUser.setRiskStatus(null);
    }
    if (!ObjectUtils.isEmpty(messageUser.getType())){
    queryWrapper.eq("type",messageUser.getType());
    }
    if (!ObjectUtils.isEmpty(messageUser.getRiskStatus())){
      queryWrapper.eq("risk_status",messageUser.getRiskStatus());
    }
    if (!ObjectUtils.isEmpty(messageUser.getEnterpriseName())) {
      queryWrapper.like("enterprise_name", messageUser.getEnterpriseName());
    }
    return messageUserMapper.selectPage(page, queryWrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String createMessage(MessageVo dto) {
    if (ObjectUtils.isEmpty(dto.getRegionIds())
        && ObjectUtils.isEmpty(
            dto.getMessageStatus().equals(Message.StatusType.NOT_MESSAGE_STATUS))) {
      throw new EpsException("请至少选择一种查看权限！");
    }
    //        //保存附件并提取保存路径
    //        List<MultipartFile> attachments = dto.getAttachment();
    //        JSONArray json = new JSONArray();
    //        if (Objects.nonNull(attachments)) {
    //            for (MultipartFile multipartFile : attachments) {
    //                fdfsClientService.uploadCheck(multipartFile);
    //                json.add(EpsUtil.toJsonObj(fdfsClientService.uploadFile(multipartFile)));
    //            }
    //        }
    // 保存消息
    Message message = new Message();
    BeanUtils.copyProperties(dto, message);
    message.setAttachmentPath(dto.getAttachment());
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    message.setUserId(currentUser.getUserId());
    message.setCreateRegionId(currentUser.getRegionId());
    message.setPublishNickName(currentUser.getNickName());
    message.setPublishTime(new Date());
    //存储可查看风险企业
    List<Long> riskIds = dto.getRiskIds();
    StringBuilder risk = getStringBuilder(riskIds);
    message.setMessageRisk(risk.toString());
    //存储可查看行业企业
    List<Long> industryIds = dto.getIndustryIds();
    StringBuilder industry = getStringBuilder(industryIds);
    message.setMessageIndustry(industry.toString());
    message.setType(dto.getType());
    message.setStatus(Message.StatusType.PUBLISH);
    if (ObjectUtils.isEmpty(dto.getId())) {
      this.save(message);
      // 保存消息和地区关联
      Long messageId = message.getId();
      List<MessageRegion> messageRegions = new ArrayList<>();
      for (Long s : dto.getRegionIds()) {
        MessageRegion messageRegion = new MessageRegion();
        messageRegion.setMessageId(messageId);
        messageRegion.setRegionId(s);
        messageRegions.add(messageRegion);
      }
      messageRegionService.saveBatch(messageRegions);
    } else {
      // 保存消息和地区关联
      Long messageId = message.getId();
      List<MessageRegion> messageRegions = new ArrayList<>();
      for (Long s : dto.getRegionIds()) {
        MessageRegion messageRegion = new MessageRegion();
        messageRegion.setMessageId(messageId);
        messageRegion.setRegionId(s);
        messageRegions.add(messageRegion);
      }
      message.setId(dto.getId());
      messageMapper.updateById(message);
      messageRegionMapper.delete(new QueryWrapper<MessageRegion>().eq("message_id", dto.getId()));
      messageRegionService.saveBatch(messageRegions);
    }
    //存储管理局未读数据
    List<Long> regionIds = dto.getRegionIds();
    List<MessageUser> messageUsers = userMapper.selectMessage(currentUser.getRegionId(),regionIds,message.getId());
    // 存储企业未读数据
    if (!ObjectUtils.isEmpty(dto.getMessageStatus())
            && !dto.getMessageStatus().equals(0)) {
      Region region = regionMapper.selectById(currentUser.getRegionId());
      List<MessageUser> messageEnterprise =
          enterpriseDeclarationMapper.selectAll(
              industryIds, riskIds, currentUser.getRegionId(), message.getId(), region.getLevel(),dto.getRegionIds());
      messageUsers.addAll(messageEnterprise);
    }
    messageUserMapper.delete(new QueryWrapper<MessageUser>().eq("message_id", message.getId()));
    messageUserMapper.insertAll(messageUsers);
    return "";
  }

  private StringBuilder getStringBuilder(List<Long> longList) {
    StringBuilder risk= new StringBuilder();
    risk.append("[");
    if (!ObjectUtils.isEmpty(risk)) {
      for (Long riskId : longList) {
        if (longList.get(longList.size()-1).equals(riskId)) {
          risk.append(riskId);
        }else {
          risk.append(riskId).append(",");
        }
      }
    }
    risk.append("]");
    return risk;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateMessage(Message dto) {
    this.saveOrUpdate(dto);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteMessage(Long id) {
    LambdaQueryWrapper<MessageUser> userWrapper = new LambdaQueryWrapper<>();
    userWrapper.eq(MessageUser::getMessageId, id);
    Message message = this.getById(id);
    if (message == null) {
      return;
    }
    JSONArray jsonArray = JSON.parseArray(message.getAttachmentPath());

    messageUserMapper.delete(userWrapper);
    LambdaQueryWrapper<MessageRegion> regionWrapper = new LambdaQueryWrapper<>();
    regionWrapper.eq(MessageRegion::getMessageId, id);
    messageRegionMapper.delete(regionWrapper);
    this.removeById(id);

    for (Object obj : jsonArray) {
      JSONObject jsonObject = (JSONObject) obj;
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
  }

  @Override
  public void deleteFile(Long id, String filePath) {
    if (!ObjectUtils.isEmpty(id)) {
      Message message = messageMapper.selectById(id);
      if (ObjectUtils.isEmpty(message)) {
        return;
      }
      JSONArray jsonArray = JSON.parseArray(message.getAttachmentPath());
      JSONArray json = new JSONArray();
      for (Object obj : jsonArray) {
        String filePath1 = ((JSONObject) obj).getString("filePath");
        if (filePath1.equals(filePath)) {
          continue;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("fileUrl", ((JSONObject) obj).getString("fileUrl"));
        map.put("filePath", ((JSONObject) obj).getString("filePath"));
        map.put("fileName", ((JSONObject) obj).getString("fileName"));
        json.add(EpsUtil.toJsonObj(map));
      }
      message.setAttachmentPath(json.toString());
      messageMapper.updateById(message);
    }
    fdfsClientService.deleteFile(filePath);
  }

  //预警
  @Override
  public List<WarningForCard> earlyWarningByUser() {
    Long regionId = EpsUtil.getCurrentUser().getRegionId();
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(SystemConstant.LEVE_TWO)) {
      return warningForCardMapper.selectList(new QueryWrapper<WarningForCard>()
              .eq("remark_status", SystemConstant.WARNING).eq("region_county_id",regionId)
              .eq("status",0));
    } else if (region.getLevel().equals(SystemConstant.LEVE_THREE)) {
      return warningForCardMapper.selectList(new QueryWrapper<WarningForCard>()
              .eq("remark_status", SystemConstant.WARNING).eq("region_town_id",regionId)
              .eq("status",0));
    } else {
      return warningForCardMapper.selectList(new QueryWrapper<WarningForCard>()
              .eq("remark_status", SystemConstant.WARNING)
              .eq("status",0));
    }
  }

  //警告
  @Override
  public List<WarningForCard> warningByUser() {
    Long regionId = EpsUtil.getCurrentUser().getRegionId();
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    if (region.getLevel().equals(SystemConstant.LEVE_TWO)) {
      return warningForCardMapper.selectList(new QueryWrapper<WarningForCard>()
              .in("remark_status", SystemConstant.EARLY_WARNING)
              .eq("region_county_id",regionId)
              .eq("status",0));
    } else if (region.getLevel().equals(SystemConstant.LEVE_THREE)) {
      return warningForCardMapper.selectList(new QueryWrapper<WarningForCard>()
              .in("remark_status", SystemConstant.EARLY_WARNING)
              .eq("region_town_id",regionId)
              .eq("status",0));
    } else {
      return warningForCardMapper.selectList(new QueryWrapper<WarningForCard>()
              .in("remark_status", SystemConstant.EARLY_WARNING)
              .eq("status",0));
    }
  }
}
