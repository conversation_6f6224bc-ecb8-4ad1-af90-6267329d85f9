package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.dto.riskPush.AlarmQueryRequest;
import com.example.eps.ins.common.dto.riskPush.AlarmQueryResponse;
import com.example.eps.ins.common.dto.riskPush.CityEventObject;
import com.example.eps.ins.common.entity.safety.SafetyMonitorEvent;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 安全监控事件服务接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface SafetyMonitorEventService extends IService<SafetyMonitorEvent> {

    /**
     * 保存安全监控事件
     * 
     * @param eventObject 城市事件对象
     * @param deviceInfo 设备信息
     * @param rawJsonData 原始JSON数据
     * @return 保存的事件对象
     */
    SafetyMonitorEvent saveSafetyEvent(CityEventObject eventObject, Map<String, Object> deviceInfo, String rawJsonData);

    /**
     * 根据外部事件ID查询事件
     */
    SafetyMonitorEvent getByExternalEventId(String externalEventId);

    /**
     * 查询指定时间范围内的事件
     */
    List<SafetyMonitorEvent> getEventsByTimeRange(Date startTime, Date endTime);

    /**
     * 根据设备ID查询事件
     */
    List<SafetyMonitorEvent> getEventsByDeviceId(Long deviceId);

    /**
     * 根据事件类型查询事件
     */
    List<SafetyMonitorEvent> getEventsByType(Integer eventType);

    /**
     * 查询告警事件
     */
    List<SafetyMonitorEvent> getAlarmEvents();

    /**
     * 统计事件数量按事件类型
     */
    List<Map<String, Object>> countEventsByType(Date startTime, Date endTime);

    /**
     * 统计告警事件数量按设备
     */
    List<Map<String, Object>> countAlarmEventsByDevice(Date startTime, Date endTime);

    /**
     * 处理事件
     * 
     * @param eventId 事件ID
     * @param processResult 处理结果
     * @return 是否处理成功
     */
    boolean processEvent(Long eventId, String processResult);

    /**
     * 批量处理事件
     */
    boolean batchProcessEvents(List<Long> eventIds, String processResult);

    /**
     * 查询未处理的事件
     */
    List<SafetyMonitorEvent> getUnprocessedEvents();

    /**
     * 查询最近的告警事件
     */
    List<SafetyMonitorEvent> getRecentAlarmEvents(Integer limit);

    /**
     * 根据设备序列号和时间范围查询事件
     */
    List<SafetyMonitorEvent> getEventsByDeviceSerialAndTimeRange(String deviceSerial, Date startTime, Date endTime);

    /**
     * 发送告警通知
     * 
     * @param event 安全监控事件
     * @return 是否发送成功
     */
    boolean sendAlarmNotification(SafetyMonitorEvent event);

    /**
     * 更新告警状态
     */
    boolean updateAlarmStatus(Long eventId, Integer alarmStatus);

    /**
     * 分页查询预警列表（带企业信息）
     *
     * @param request 查询请求参数
     * @return 预警列表分页数据
     */
    IPage<AlarmQueryResponse> getAlarmPageWithEnterpriseInfo(AlarmQueryRequest request);
}
