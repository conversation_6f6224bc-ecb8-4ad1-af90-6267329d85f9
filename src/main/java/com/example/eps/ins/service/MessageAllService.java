package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.eps.ins.common.dto.report.model.MessageAllVo;
import com.example.eps.ins.common.entity.system.MessageAll;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023/6/6 11:10
 * @Version: 1.0
 */
public interface MessageAllService {
    IPage<MessageAll> findMessages(MessageAllVo dto);

    MessageAll detail(Long id);

    void lookMessage(Long id);

    void createMessage(MessageAll dto);

    void deleteMessage(MessageAllVo dto);


}
