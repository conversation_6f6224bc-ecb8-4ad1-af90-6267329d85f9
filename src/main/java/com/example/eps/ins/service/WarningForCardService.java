package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.TDictionaries;

import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/11/10 13:56
 * @Version: 1.0
 */
public interface WarningForCardService extends IService<WarningForCard> {
    IPage<WarningForCard> findSpecialPeoples(QueryRequest request, WarningForCard warningForCard);

    List<TDictionaries> getCode();

    Boolean updateWar(WarningForCard warningForCard);

    void updateWaringForCard(Long id);

    IPage<WarningForCard> findSpecialPeoplesSystem(QueryRequest request, WarningForCard warningForCard);
}
