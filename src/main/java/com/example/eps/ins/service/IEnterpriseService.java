package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.eps.ins.common.model.EnterpriseVo;
import com.example.eps.ins.common.model.ReportVo;
import com.github.dreamyoung.mprelation.IService;
import com.example.eps.ins.common.bean.EnterpriseDeclarationRequest;
import com.example.eps.ins.common.bean.EnterpriseResponse;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 企业信息表 Service接口
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:42
 */
public interface IEnterpriseService extends IService<Enterprise> {
    /**
     * 查询（分页）
     *
     * @param enterprise enterprise
     * @return IPage<Enterprise>
     */
    IPage<Enterprise> findEnterprises(EnterpriseVo enterprise);
    /**
     * 查询（不分页）
     *
     * @param enterprise enterprise
     * @return IPage<Enterprise>
     */
    List<Enterprise>  listEnterprises(EnterpriseVo enterprise);
    /**
     * 查询
     *
     * Enterprise
     * @param id
     */
    EnterpriseResponse findEnterprise(Long id);

    /**
     * 查询（所有）
     *
     * @param enterprise enterprise
     * @return List<Enterprise>
     */
    List<Enterprise> findEnterprises(Enterprise enterprise);

    /**
     * 新增
     *
     * @param enterprise enterprise
     */
    Long createEnterprise(EnterpriseDeclarationRequest enterprise);

    /**
     * 修改
     *
     * @param enterprise enterprise
     */
    void updateEnterprise(Long id,EnterpriseDeclarationRequest enterprise);

    /**
     * 修改
     *
     * @param enterprise enterprise
     */
    void updateEnterpriseAddress(Long id, Enterprise enterprise);

    /**
     * 删除
     *
     * @param enterprise enterprise
     */
    void deleteEnterprise(Enterprise enterprise);

    int importEnterprises(MultipartFile file);

    List<String[]> conver(List<Enterprise> records);

    IPage<Enterprise> hiddenList(EnterpriseVo enterprise);

    /**
     * 企业信息维护获取下拉菜单
     * @return
     */
    List<ReportVo> getReport();
}
