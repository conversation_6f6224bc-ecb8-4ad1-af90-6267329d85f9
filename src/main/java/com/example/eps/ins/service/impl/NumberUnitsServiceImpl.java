package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.constant.NumberConstant;
import com.example.eps.ins.common.dto.report.req.CompanyView;
import com.example.eps.ins.common.dto.report.req.RiskListReq;
import com.example.eps.ins.common.dto.report.resp.*;
import com.example.eps.ins.mapper.*;
import com.example.eps.ins.mapper.TRptRiskCensusMapper;
import com.example.eps.ins.service.NumberUnitsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;

@Service
@RequiredArgsConstructor
public class NumberUnitsServiceImpl implements NumberUnitsService {
    private final TRptRiskCensusMapper tRptRiskCensusMapper;
    private final RegionMapper regionMapper;
    private final RiskMapper riskMapper;
    private final TRptStressThreeMapper tRptStressThreeMapper;
    private final TRptCompanyViewMapper tRptCompanyViewMapper;
    private final TRptIndustryScatterMapper tRptIndustryScatterMapper;
    private final EnterPriseReportMapper enterPriseReportMapper;

    @Override
    public TRptCompanyViewResp companyView(CompanyView companyView) {
        Long regionId = companyView.getRegionId();
        Integer year = companyView.getYear();
//    if (year == Integer.parseInt(DateTimeUtils.getYear())) {
//      year = NumberConstant.ONE;
//    }
        // 当前登录人地区id
        Long userRegionId = getRegionId();
        // 获取参数地址所属地区
        Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
        // 获取当前登录人该地址所属地区
        Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
        NumberConstant.regionCompareUserRegion(userRegion, region);
        if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
            //获取辖区内镇街
            return enterPriseReportMapper.selectUnitReport(regionId, null);
        } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
            return enterPriseReportMapper.selectUnitReport(null, regionId);
        } else {
            return enterPriseReportMapper.selectUnitReport(null, null);
        }
    }

//  @Override
//  public List<TRptIndustryScatterResp> getIndustry(CompanyView companyView) {
//    Integer year = companyView.getYear();
//    Long regionId = companyView.getRegionId();
//    // 当前登录人地区id
//    Long userRegionId = getRegionId();
//    // 获取当前登录人该地址所属地区
//    Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
//    // 获取参数地址所属地区
//    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));

    /// /    if (year == Integer.parseInt(DateTimeUtils.getYear())) {
    /// /      year = NumberConstant.ONE;
    /// /    }
//    if (region.getLevel().equals(NumberConstant.LEVE_TWO)){
//      NumberConstant.regionCompareUserRegion(userRegion,region);
//      //获取辖区内镇街
//      List<Long> regions = regionMapper.selectSonId(region.getId());
//      regions.add(region.getId());
//      return tRptIndustryScatterMapper.selectLiveTwo(regions,year);
//    } else {
//      NumberConstant.regionCompareUserRegion(userRegion,region);
//      return tRptIndustryScatterMapper.getCity(year, regionId);
//    }
//  }
    @Override
    public List<TRptIndustryScatterResp> getIndustry(CompanyView companyView) {
        Long regionId = companyView.getRegionId();
        // 当前登录人地区id
        Long userRegionId = getRegionId();
        // 获取当前登录人该地址所属地区
        Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
        // 获取参数地址所属地区
        Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
        NumberConstant.regionCompareUserRegion(userRegion, region);
        if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
            //获取辖区内镇街
            return enterPriseReportMapper.selectIndustryUnitNum(regionId, null);
        } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
            return enterPriseReportMapper.selectIndustryUnitNum(null, regionId);
        } else {
            return enterPriseReportMapper.selectIndustryUnitNum(null, null);
        }
    }

    @Override
    public TRptStressThreeResp getThreeStress(CompanyView companyView) {
        Long regionId = companyView.getRegionId();
        // 当前登录人地区id
        Long userRegionId = getRegionId();
//    if (year == Integer.parseInt(DateTimeUtils.getYear())) {
//      year = NumberConstant.ONE;
//    }
        // 获取当前登录人该地址所属地区
        Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
        // 获取参数地址所属地区
        Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
        NumberConstant.regionCompareUserRegion(userRegion, region);
        if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
            //获取辖区内镇街
            return enterPriseReportMapper.selectThreeStress(regionId, null);
        } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
            return enterPriseReportMapper.selectThreeStress(null, regionId);
        } else {
            return enterPriseReportMapper.selectThreeStress(null, null);
        }
    }

    @Override
    public List<RiskListResp> riskList(RiskListReq riskListReq) {
        Long regionId = null;
        Long userRegionId = null;
        if (!ObjectUtils.isEmpty(riskListReq)) {
            regionId = riskListReq.getRegionId();
            userRegionId = getRegionId();
        } else {
            regionId = getRegionId();
        }
        // 获取参数地址所属地区
        Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
        if (!ObjectUtils.isEmpty(riskListReq)) {
            // 获取当前登录人该地址所属地区
            Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
            NumberConstant.regionCompareUserRegion(userRegion, region);
        }
        if (region.getLevel().equals(NumberConstant.LEVE_TWO)) {
            //获取辖区内镇街
            return enterPriseReportMapper.selectRiskList(regionId, null);
        } else if (region.getLevel().equals(NumberConstant.LEVE_THREE)) {
            return enterPriseReportMapper.selectRiskList(null, regionId);
        } else {
            return enterPriseReportMapper.selectRiskList(null, null);
        }
    }
//  @Override
//  public List<RiskListResp> riskList(RiskListReq riskListReq) {
//    Integer year = riskListReq.getYear();
////    if (year == Integer.parseInt(DateTimeUtils.getYear())) {
////      year = NumberConstant.ONE;
////    }
//    Long regionId = riskListReq.getRegionId();
//    // 当前登录人地区id
//    Long userRegionId = getRegionId();
//    // 获取当前登录人该地址所属地区
//    Region userRegion = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", userRegionId));
//    // 获取参数地址所属地区
//    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
//    // 所有的风险
//    List<Long> integers = riskMapper.selectByParent(NumberConstant.RISK_PARENT_ID);
//    List<RiskListResp> listReps = new ArrayList<>();
//    NumberConstant.regionCompareUserRegion(userRegion,region);
//    if (region.getLevel().equals(NumberConstant.LEVE_TWO)){
//      //获取辖区内镇街
//      List<Long> regions = regionMapper.selectSonId(region.getId());
//      regions.add(region.getId());
//      // 市区和街道
//      for (Long integer : integers) {
//        // 获取数据
//        RiskListResp riskListResp =
//                tRptRiskCensusMapper.selectLiveTwo(regions, year, integer, riskListReq.getIndustryId());
//        // 判断是否为空
//        if (ObjectUtils.isEmpty(riskListResp) || ObjectUtils.isEmpty(riskListResp.getRisk())) {
//          RiskListResp riskListResp1 = new RiskListResp();
//          String name = riskMapper.getRiskName(integer);
//          riskListResp1.setRisk(name);
//          riskListResp1.setNum(NumberConstant.ZERO);
//          listReps.add(riskListResp1);
//        } else {
//          listReps.add(riskListResp);
//        }
//      }
//    } else {
//      // 市区和街道
//      for (Long integer : integers) {
//        // 获取数据
//        RiskListResp riskListResp =
//            tRptRiskCensusMapper.riskAll(regionId, year, integer, riskListReq.getIndustryId());
//        // 判断是否为空
//        if (ObjectUtils.isEmpty(riskListResp) || ObjectUtils.isEmpty(riskListResp.getRisk())) {
//          RiskListResp riskListResp1 = new RiskListResp();
//          String name = riskMapper.getRiskName(integer);
//          riskListResp1.setRisk(name);
//          riskListResp1.setNum(NumberConstant.ZERO);
//          listReps.add(riskListResp1);
//        } else {
//          listReps.add(riskListResp);
//        }
//      }
//    }
//    return listReps;
//  }

    /**
     * 获取当前登录人地区id
     *
     * @return
     */
    private Long getRegionId() {
        return EpsUtil.getCurrentUser().getRegionId();
    }

    @Override
    public List<RegionResp> getRegion() {
        return regionMapper.selectRegion();
    }
}
