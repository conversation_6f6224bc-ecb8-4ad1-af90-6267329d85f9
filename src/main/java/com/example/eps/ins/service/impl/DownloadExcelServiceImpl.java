package com.example.eps.ins.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.eps.ins.common.dto.report.model.RiskContentVo;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.enterprise.*;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.model.EnterpriseVo;
import com.example.eps.ins.common.utils.*;
import com.example.eps.ins.mapper.*;
import com.example.eps.ins.service.DownloadExcelService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.REGION_THREE_LIVE;
import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.REGION_TWO_LIVE;

/** @Author: Zhanghongyin @Date: Created in 2023/1/11 15:18 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class DownloadExcelServiceImpl implements DownloadExcelService {

  // 企业
  private static final Integer MESSAGE_TYPE_ENTERPRISE = 2;
  private final RegionMapper regionMapper;
  private final EnterpriseMapper enterpriseMapper;
  private final MessageUserMapper messageUserMapper;
  private final HiddenDangerListInspectMapper hiddenDangerListInspectMapper;
  private final HiddenDangerListMapper hiddenDangerMapper;
  private final WarningForCardMapper warningForCardMapper;

  @Override
  public void download(HttpServletResponse response, Enterprise enterprise) {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    List<Long> selectSon = new ArrayList<>();
    Region region = regionMapper.selectById(currentUser.getRegionId());
    if (region.getLevel().equals(REGION_TWO_LIVE)) {
      // 获取子id
      selectSon = regionMapper.selectSon(currentUser.getRegionId());
    } else if (region.getLevel().equals(REGION_THREE_LIVE)) {
      selectSon.add(region.getId());
    }
    List<HiddenExcelDate> hiddenList =
        enterpriseMapper.getHiddenExeclList(
            enterprise.getName(),
            enterprise.getRiskTypeSearch(),
            enterprise.getNoCommit(),
            enterprise.getIndustryId(),
            null,
            null,
            selectSon,
            currentUser.getUserId());
    try {
      SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
      String format1 = format.format(new Date());
      String fileName = URLEncoder.encode("隐患排查列表", "UTF-8") + format1;
      response.setContentType("application/vnd.ms-excel");
      response.setCharacterEncoding("utf-8");
      response.setHeader("Content-disposition", "attachment  ;filename=" + fileName + ".xlsx");
      EasyExcel.write(response.getOutputStream(), HiddenExcelDate.class)
          .sheet("企业列表")
          .doWrite(hiddenList);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Override
  public void message(HttpServletResponse response, MessageUser messageUser) {
    if (!ObjectUtils.isEmpty(messageUser.getType())
        && !messageUser.getType().equals(MESSAGE_TYPE_ENTERPRISE)) {
      messageUser.setRiskStatus(null);
    }
    List<MessageExcelDate> messageExcel =
        messageUserMapper.getMessageExcel(
            messageUser.getMessageId(),
            messageUser.getType(),
            messageUser.getRiskStatus(),
            messageUser.getEnterpriseName());
    try {
      SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
      String format1 = format.format(new Date());
      String fileName = URLEncoder.encode("未读列表", "UTF-8") + format1;
      response.setContentType("application/vnd.ms-excel");
      response.setCharacterEncoding("utf-8");
      response.setHeader("Content-disposition", "attachment  ;filename=" + fileName + ".xlsx");
      if (ObjectUtils.isEmpty(messageUser.getType())
          || messageUser.getType().equals(MESSAGE_TYPE_ENTERPRISE)) {
        EasyExcel.write(response.getOutputStream(), MessageExcelDate.class)
            .sheet("未读列表")
            .doWrite(messageExcel);
      } else {
        EasyExcel.write(response.getOutputStream(), MessageExcelAdminDate.class)
            .sheet("未读列表")
            .doWrite(messageExcel);
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Override
  public void hidden(HttpServletResponse response, EnterpriseVo enterprise) {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    List<Long> selectSon = new ArrayList<>();
    Region region = regionMapper.selectById(currentUser.getRegionId());
    if (region.getLevel().equals(REGION_TWO_LIVE)) {
      // 获取子id
      selectSon = regionMapper.selectSon(currentUser.getRegionId());
    } else if (region.getLevel().equals(REGION_THREE_LIVE)) {
      selectSon.add(region.getId());
    }
    List<Enterprise> list =
        enterpriseMapper.getHiddenEnterprise(
            enterprise.getName(),
            enterprise.getRiskTypeSearch(),
            enterprise.getNoCommit(),
            enterprise.getIndustryId(),
            null,
            null,
            selectSon,
            currentUser.getUserId());
    List<HiddenDangerListInspectDate> hiddenList = new ArrayList<>();
    list.forEach(
        a -> {
          get(a, hiddenList);
        });
    try {
      SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
      String format1 = format.format(new Date());
      String fileName = URLEncoder.encode("隐患排查列表", "UTF-8") + format1;
      response.setContentType("application/vnd.ms-excel");
      response.setCharacterEncoding("utf-8");
      response.setHeader("Content-disposition", "attachment  ;filename=" + fileName + ".xlsx");
      EasyExcel.write(response.getOutputStream(), HiddenDangerListInspectDate.class)
          .sheet("企业列表")
          .doWrite(hiddenList);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Override
  public void warningForCard(HttpServletResponse response, WarningForCard warningForCard) {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    Long regionId = currentUser.getRegionId();
    // 获取该地址所属地区
    Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
    List<WarningForCardData> list = warningForCardMapper.selectAllExcel(region.getLevel(),warningForCard.getCode(),
            warningForCard.getName(),currentUser.getUserId(),regionId);
    try {
      SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
      String format1 = format.format(new Date());
      String fileName = URLEncoder.encode("企业预警", "UTF-8") + format1;
      response.setContentType("application/vnd.ms-excel");
      response.setCharacterEncoding("utf-8");
      response.setHeader("Content-disposition", "attachment  ;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), WarningForCardData.class)
                .sheet("企业预警")
                .doWrite(list);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  @Override
  public void riskContent(HttpServletResponse response, RiskContentVo riskContentVo) {
    CurrentUser currentUser = EpsUtil.getCurrentUser();
    List<Long> selectSon = new ArrayList<>();
    Region region = regionMapper.selectById(currentUser.getRegionId());
    if (region.getLevel().equals(REGION_TWO_LIVE)) {
      // 获取子id
      selectSon = regionMapper.selectSon(currentUser.getRegionId());
    } else if (region.getLevel().equals(REGION_THREE_LIVE)) {
      selectSon.add(region.getId());
    }
    List<HiddenExcelDate> hiddenList =
            enterpriseMapper.enterpriseExcelList(
                    riskContentVo.getEnterpriseName(),selectSon);
    try {
      SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
      String format1 = format.format(new Date());
      String fileName = URLEncoder.encode("隐患排查列表", "UTF-8") + format1;
      response.setContentType("application/vnd.ms-excel");
      response.setCharacterEncoding("utf-8");
      response.setHeader("Content-disposition", "attachment  ;filename=" + fileName + ".xlsx");
      EasyExcel.write(response.getOutputStream(), HiddenExcelDate.class)
              .sheet("企业列表")
              .doWrite(hiddenList);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  private HiddenDangerListInspectDate get(
      Enterprise enterprise, List<HiddenDangerListInspectDate> hiddenList) {
    HiddenDangerListInspectDate hiddenDangerListInspectDate = new HiddenDangerListInspectDate();
    List<HiddenDangerListInspect> list =
        hiddenDangerListInspectMapper.selectList(
            new QueryWrapper<HiddenDangerListInspect>()
                .eq("creator_id", enterprise.getCreatorId()));
    list.forEach(
        hiddenDangerId -> {
          HiddenDangerList hiddenDangerList =
              hiddenDangerMapper.selectOne(
                  new QueryWrapper<HiddenDangerList>()
                      .eq("id", hiddenDangerId.getHiddenDangerId())
                      .eq("hidden_status", 1));
          if (!ObjectUtils.isEmpty(hiddenDangerList)) {
            hiddenDangerId.setHiddenDangerDept(hiddenDangerList.getHiddenDangerDept());
            hiddenDangerId.setHiddenDangerRevise(hiddenDangerList.getHiddenDangerRevise());
            hiddenDangerId.setHiddenDangerContent(hiddenDangerList.getHiddenDangerContent());
            hiddenDangerId.setHiddenDangerInspect(hiddenDangerList.getHiddenDangerInspect());
            hiddenDangerId.setHiddenDangerCycle(hiddenDangerList.getHiddenDangerCycle());
            hiddenDangerId.setHiddenDangerReviseTime(hiddenDangerList.getHiddenDangerReviseTime());
            hiddenDangerId.setHiddenDangerId(enterprise.getCreatorId());
            if (ObjectUtils.isEmpty(hiddenDangerId.getInspectImage())) {
              hiddenDangerId.setInspectImage("[]");
            }
            BeanUtils.copyProperties(hiddenDangerId, hiddenDangerListInspectDate);
            if (hiddenDangerId.getHiddenDangerCycle().equals(0)) {
              hiddenDangerListInspectDate.setHiddenDangerCycle("日");
            } else if (hiddenDangerId.getHiddenDangerCycle().equals(1)) {
              hiddenDangerListInspectDate.setHiddenDangerCycle("周");
            } else {
              hiddenDangerListInspectDate.setHiddenDangerCycle("月");
            }
            hiddenDangerListInspectDate.setEnterpriseName(enterprise.getName());
            hiddenList.add(hiddenDangerListInspectDate);
          }
        });
    return hiddenDangerListInspectDate;
  }
}
