package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.eps.ins.common.bean.EnterpriseDeclarationReview;
import com.example.eps.ins.common.constant.EnterpriseDeclarationConstant;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.*;
import com.example.eps.ins.mapper.*;
import com.example.eps.ins.service.IRegionService;
import com.github.dreamyoung.mprelation.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationRequest;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.DataTransfer;
import com.example.eps.ins.common.utils.EnterpriseUtil;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IEnterpriseDeclarationService;
import com.example.eps.ins.service.IEnterpriseRiskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.*;
import static com.example.eps.ins.common.constant.RegionConstant.REGION_LEVEL_CITY;

/**
 * 企业信息申报表 Service实现
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
@Slf4j
public class EnterpriseDeclarationServiceImpl extends ServiceImpl<EnterpriseDeclarationMapper, EnterpriseDeclaration> implements IEnterpriseDeclarationService {

    private final EnterpriseDeclarationMapper enterpriseDeclarationMapper;
    private final IEnterpriseRiskService enterpriseRiskService;
    private final EnterpriseMapper enterpriseMapper;
    private final BasicRiskMapper basicRiskMapper;
    private final IRegionService regionService;
    private final IndustryMapper industryMapper;
    private final RegionMapper regionMapper;
    private static final int TIME_SIZE = 2;
    private static final int ENTERPRISE_DECLARATION_REVIEW_STEP_FIRST = 1;// 初审
    private static final int ENTERPRISE_DECLARATION_REVIEW_STEP_SECOND = 2; //复审
    private static final int PLAN_OK = 1;// 已建立应急预案
    private static final int PLAN_NO = 0;// 未建立应急预案
    private static final int HIDDEN_OK = 1; //已建立隐患制度
    private static final int HIDDEN_NO = 0;// 未建立隐患制度
    private final HiddenDangerMapper hiddenDangerMapper;
    private final EmergencyPlanMapper emergencyPlanMapper;

    @Override
    public EnterpriseDeclarationResponse currentUserEnterpriseDeclaration() throws EpsException {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        LambdaQueryWrapper<EnterpriseDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseDeclaration::getCreatorId, userId);
        queryWrapper.orderByDesc(EnterpriseDeclaration::getCreateTime);
        queryWrapper.last("limit 1");
        EnterpriseDeclaration enterpriseDeclaration = this.getOne(queryWrapper);
        if (null == enterpriseDeclaration) {
            return null;
        }
        EnterpriseDeclarationResponse response = (EnterpriseDeclarationResponse) DataTransfer.transfer(enterpriseDeclaration, EnterpriseDeclarationResponse.class);
        return response;
    }

    @Override
    public List<EnterpriseDeclarationResponse> currentUserEnterpriseDeclarationList() {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        List<EnterpriseDeclarationResponse> enterpriseDeclarationResponses=new ArrayList<>();
        LambdaQueryWrapper<EnterpriseDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseDeclaration::getCreatorId, userId);
        queryWrapper.eq(EnterpriseDeclaration::getStatus, EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT);
        queryWrapper.orderByDesc(EnterpriseDeclaration::getCreateTime);
        List<EnterpriseDeclaration> enterpriseDeclaration = enterpriseDeclarationMapper.selectList(queryWrapper);
        if (null == enterpriseDeclaration) {
            return null;
        }
        enterpriseDeclaration.forEach(a->{
            EnterpriseDeclarationResponse response = (EnterpriseDeclarationResponse) DataTransfer.transfer(a,
                    EnterpriseDeclarationResponse.class);
            enterpriseDeclarationResponses.add(response);
        });
        return enterpriseDeclarationResponses;
    }

    @Override
    public EnterpriseDeclarationResponse currentUserEnterpriseDeclarationSecond() {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        LambdaQueryWrapper<EnterpriseDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseDeclaration::getCreatorId, userId);
        queryWrapper.eq(EnterpriseDeclaration::getStatus, EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT);
        queryWrapper.orderByDesc(EnterpriseDeclaration::getCreateTime);
        queryWrapper.last("limit 1");
        EnterpriseDeclaration enterpriseDeclaration = this.getOne(queryWrapper);
        if (null == enterpriseDeclaration) {
            return null;
        }
        EnterpriseDeclarationResponse response = (EnterpriseDeclarationResponse) DataTransfer.transfer(enterpriseDeclaration, EnterpriseDeclarationResponse.class);
        return response;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveEnterpriseDeclaration(EnterpriseDeclarationRequest enterpriseDeclarationRequest) {
        Long id = enterpriseDeclarationRequest.getId();
        //获取最近一次申报
        EnterpriseDeclarationResponse lastDeclaration = currentUserEnterpriseDeclaration();
        if (null != lastDeclaration) {
            if (Objects.isNull(id)) {
                throw new EpsException("当前已有申报审核中，请勿暂存申报信息");
            } else {
                if (!id.equals(lastDeclaration.getId())) {//传入的ID不是最近一次申报的ID
                    throw new EpsException("当前已有申报审核中，请勿暂存申报信息");
                }else if ( !lastDeclaration.getStatus().equals(EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_WAIT_REVIEW) ||
                        !lastDeclaration.getStatus().equals(EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_FIRST_REVIEW_ADOPT)) {
                    EnterpriseDeclaration enterpriseDeclaration = (EnterpriseDeclaration) DataTransfer.transfer(enterpriseDeclarationRequest, EnterpriseDeclaration.class);
                    enterpriseDeclaration.setModifyTime(new Date());
                    enterpriseDeclaration.setRisks(enterpriseDeclarationRequest.getRisks().toJSONString());
                    this.updateById(enterpriseDeclaration);
                    //删除原有风险，保存新设置风险
                    EnterpriseRisk enterpriseRiskWrapper = new EnterpriseRisk();
                    enterpriseRiskWrapper.setEnterpriseId(id);
                    enterpriseRiskService.remove(new LambdaQueryWrapper<>(enterpriseRiskWrapper));
                    JSONObject jsonObject = enterpriseDeclarationRequest.getRisks();
                    List<EnterpriseRisk> enterpriseRisks = EnterpriseUtil.generateEnterpriseRisk(enterpriseDeclaration.getId(), jsonObject);
                    if (!enterpriseRisks.isEmpty()) {
                        enterpriseRiskService.saveBatch(enterpriseRisks);
                    }
                } else {//判断当前状态是否审核中
                    throw new EpsException("当前已提交申报，请勿暂存申报信息");
                }
            }
        }else{
            if (Objects.isNull(id)) {
                id = createNewEnterpriseDeclaration(enterpriseDeclarationRequest);
            }else {
                throw new EpsException("申报信息有误，请重试");
            }
        }
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createEnterpriseDeclaration(EnterpriseDeclarationRequest enterpriseDeclarationRequest) {
        //判断重复申报 判断条件为社信代码
        String socialCode = enterpriseDeclarationRequest.getSocialCode();
        Enterprise enterprise = new Enterprise();
        enterprise.setSocialCode(socialCode);
        Enterprise existEnterprise = enterpriseMapper.selectOne(new QueryWrapper<>(enterprise));
        if (null != existEnterprise &&
                !existEnterprise.getCreatorId().equals(EpsUtil.getCurrentUser().getUserId())) {
            throw new EpsException("该企业（社会信用统一代码）已被其他用户申报");
        }
        LambdaQueryWrapper<EnterpriseDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseDeclaration::getSocialCode, socialCode);
        queryWrapper.ne(EnterpriseDeclaration::getCreatorId, EpsUtil.getCurrentUser().getUserId());
        queryWrapper.and(wrapper -> wrapper.eq(EnterpriseDeclaration::getStatus, EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_WAIT_REVIEW).or().eq(EnterpriseDeclaration::getStatus, EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_FIRST_REVIEW_ADOPT));
        List<EnterpriseDeclaration> existEnterpriseDeclarations = this.enterpriseDeclarationMapper.selectList(queryWrapper);
        if (null != existEnterpriseDeclarations && !existEnterpriseDeclarations.isEmpty()) {
            throw new EpsException("该企业（社会信用统一代码）已被其他用户申报");
        }
        Long id = enterpriseDeclarationRequest.getId();
        //获取最近一次申报
        EnterpriseDeclarationResponse lastDeclaration = currentUserEnterpriseDeclaration();
        if (null != lastDeclaration) {
            if (null == id) {
                throw new EpsException("当前已有申报审核中，请勿重复申报");
            } else {
                if (!id.equals(lastDeclaration.getId())) {//传入的ID不是最近一次申报的ID
                    throw new EpsException("当前已有申报审核中，请勿重复申报");
                } else if (lastDeclaration.getStatus().equals(EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_WAIT_REVIEW)) {//判断当前状态是否可修改
                    EnterpriseDeclaration enterpriseDeclaration = (EnterpriseDeclaration) DataTransfer.transfer(enterpriseDeclarationRequest, EnterpriseDeclaration.class);
                    enterpriseDeclaration.setModifyTime(new Date());
                    enterpriseDeclaration.setRisks(enterpriseDeclarationRequest.getRisks().toJSONString());
                    this.updateById(enterpriseDeclaration);
                    //删除原有风险，保存新设置风险
                    EnterpriseRisk enterpriseRiskWrapper = new EnterpriseRisk();
                    enterpriseRiskWrapper.setEnterpriseId(id);
                    enterpriseRiskService.remove(new LambdaQueryWrapper<>(enterpriseRiskWrapper));
                    JSONObject jsonObject = enterpriseDeclarationRequest.getRisks();
                    List<EnterpriseRisk> enterpriseRisks = EnterpriseUtil.generateEnterpriseRisk(enterpriseDeclaration.getId(), jsonObject);
                    if (!enterpriseRisks.isEmpty()) {
                        enterpriseRiskService.saveBatch(enterpriseRisks);
                    }
                } else if (lastDeclaration.getStatus().equals(EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_FIRST_REVIEW_ADOPT)) {//判断当前状态是否审核中
                    throw new EpsException("当前申报审核中，请勿重复申报");
                } else {//创建新申报信息
                    id = createNewEnterpriseDeclaration(enterpriseDeclarationRequest);
                }
            }
        } else {//第一次申报
            if (null == id) {//创建新申报信息
                id = createNewEnterpriseDeclaration(enterpriseDeclarationRequest);
            } else {
                throw new EpsException("申报信息有误，请重试");
            }
        }
        return id;
    }

    /*
     * 查询条件含地区时，不传地区参数，则查看当前用户所属组织及下级组织；
     * 查询条件含地区时，传地区参数，则校验地区参数是否归属当前用户所属组织及下级组织；
     * 查询条件含地区时，传地区参数，且地区参数归属当前用户所属组织及下级组织时，需比对地区参数为区或者镇；
     */
    @Override
    public IPage<EnterpriseDeclaration> findEnterpriseDeclarations(QueryRequest request, EnterpriseDeclaration enterpriseDeclaration) {
        Integer status = enterpriseDeclaration.getStatus();
        LambdaQueryWrapper<EnterpriseDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        Long regionCountyId = enterpriseDeclaration.getRegionCountyId();
        Long regionId = EpsUtil.getCurrentUser().getRegionId();

        if (Objects.isNull(regionCountyId)) {
            if (regionId != REGION_LEVEL_CITY) {
                queryWrapper.and((wrapper) -> {
                    wrapper.eq(EnterpriseDeclaration::getRegionCountyId, regionId).or().eq(EnterpriseDeclaration::getRegionTownId, regionId);
                });
            }
        } else {
            if (!currentUserRegionIds.contains(enterpriseDeclaration.getRegionCountyId())) {
                throw new EpsException("当前用户所在组织无权查看该地区企业申报信息");
            }
            queryWrapper.and(wrapper -> {
                wrapper.eq(EnterpriseDeclaration::getRegionTownId, regionCountyId).or().eq(EnterpriseDeclaration::getRegionCountyId, regionCountyId);
            });
        }
        if (!ObjectUtils.isEmpty(enterpriseDeclaration.getName())){
            queryWrapper.like(EnterpriseDeclaration::getName,enterpriseDeclaration.getName());
        }
        if (!ObjectUtils.isEmpty(enterpriseDeclaration.getPublishTimeSlot())
                && enterpriseDeclaration.getPublishTimeSlot().size() ==TIME_SIZE) {
            String startTime = enterpriseDeclaration.getPublishTimeSlot().get(0);
            String endTime = enterpriseDeclaration.getPublishTimeSlot().get(1);
            if (StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)) {
                queryWrapper.between(EnterpriseDeclaration::getCreateTime,startTime,endTime);
            }
        }
        queryWrapper.eq(Objects.nonNull(enterpriseDeclaration.getIndustryId()),
                EnterpriseDeclaration::getIndustryId, enterpriseDeclaration.getIndustryId());
        if (Objects.isNull(status)) {
            queryWrapper.gt(EnterpriseDeclaration::getStatus,
                    EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SAVE);
        } else {
            queryWrapper.eq(EnterpriseDeclaration::getStatus, status);
        }
        queryWrapper.orderByDesc(EnterpriseDeclaration::getCreateTime);
        Page<EnterpriseDeclaration> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    public List<EnterpriseDeclarationResponse> currentUserEnterpriseDeclarationList(Long userId) {
        List<EnterpriseDeclarationResponse> enterpriseDeclarationResponses=new ArrayList<>();
        LambdaQueryWrapper<EnterpriseDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseDeclaration::getCreatorId, userId);
        queryWrapper.orderByDesc(EnterpriseDeclaration::getCreateTime);
        List<EnterpriseDeclaration> enterpriseDeclaration = enterpriseDeclarationMapper.selectList(queryWrapper);
        if (null == enterpriseDeclaration) {
            return null;
        }
        enterpriseDeclaration.forEach(a->{
            EnterpriseDeclarationResponse response = (EnterpriseDeclarationResponse) DataTransfer.transfer(a,
                    EnterpriseDeclarationResponse.class);
            enterpriseDeclarationResponses.add(response);
        });
        return enterpriseDeclarationResponses;
    }

    @Override
    public EnterpriseDeclarationResponse queryEnterpriseDeclaration(Long id) {
        EnterpriseDeclaration enterpriseDeclaration = this.getById(id);
        if (null == enterpriseDeclaration) {
            throw new EpsException("企业申报信息不存在：" + id);
        }
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        Long regionId = enterpriseDeclaration.getRegionTownId();
        if (null == currentUserRegionIds || !currentUserRegionIds.contains(regionId)) {
            throw new EpsException("当前用户所在组织无权查看该企业申报详情");
        }
        queryDetail(enterpriseDeclaration);
        EnterpriseDeclarationResponse response = (EnterpriseDeclarationResponse) DataTransfer.transfer(enterpriseDeclaration, EnterpriseDeclarationResponse.class);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void firstReviewEnterpriseDeclaration(EnterpriseDeclarationReview review) {
        LambdaQueryWrapper<EnterpriseDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseDeclaration::getId, review.getId());
        EnterpriseDeclaration enterpriseDeclaration = this.baseMapper.selectOne(queryWrapper);
        validationReviewStatus(enterpriseDeclaration, ENTERPRISE_DECLARATION_REVIEW_STEP_FIRST);
        Long userId = EpsUtil.getCurrentUser().getUserId();
        String remark = review.getRemark();
        int status = review.getStatus();
        enterpriseDeclaration.setFirstReviewerId(userId);
        enterpriseDeclaration.setFirstReviewTime(new Date());
        enterpriseDeclaration.setFirstReviewRemark(remark);
        enterpriseDeclaration.setFirstReviewStatus(status);
        int declarationStatus = ENTERPRISE_DECLARATION_STATUS_FIRST_REVIEW_REFUSE;
        if (status == ENTERPRISE_DECLARATION_REVIEW_STATUS_ADOPT) {
            declarationStatus = ENTERPRISE_DECLARATION_STATUS_FIRST_REVIEW_ADOPT;
        }
        enterpriseDeclaration.setStatus(declarationStatus);
        this.updateById(enterpriseDeclaration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void secondReviewEnterpriseDeclaration(EnterpriseDeclarationReview review) {
        LambdaQueryWrapper<EnterpriseDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseDeclaration::getId, review.getId());
        EnterpriseDeclaration enterpriseDeclaration = this.baseMapper.selectOne(queryWrapper);
        validationReviewStatus(enterpriseDeclaration, ENTERPRISE_DECLARATION_REVIEW_STEP_SECOND);
        Long userId = EpsUtil.getCurrentUser().getUserId();
        String remark = review.getRemark();
        int status = review.getStatus();
        enterpriseDeclaration.setSecondReviewerId(userId);
        enterpriseDeclaration.setSecondReviewTime(new Date());
        enterpriseDeclaration.setSecondReviewRemark(remark);
        enterpriseDeclaration.setSecondReviewStatus(status);
        int declarationStatus = ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_REFUSE;
        //复审审核通过
        if (status == ENTERPRISE_DECLARATION_REVIEW_STATUS_ADOPT) {
            declarationStatus = ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;
        }
        enterpriseDeclaration.setStatus(declarationStatus);
        this.updateById(enterpriseDeclaration);
        if (status == ENTERPRISE_DECLARATION_REVIEW_STATUS_ADOPT) {
            createEnterprise(enterpriseDeclaration);
        }
    }

    public void updateBasicRisk(BasicRiskMapper basicRiskMapper,Enterprise newEnterprise, BasicRisk newBasicRisk) {
        //获取历史记录并删除
        BasicRisk oldBasic= basicRiskMapper.selectOne(new LambdaQueryWrapper<BasicRisk>()
                .eq(BasicRisk::getEnterpriseId, newEnterprise.getId()));
        if (!ObjectUtils.isEmpty(oldBasic)){
            basicRiskMapper.deleteById(oldBasic.getId());
        }
        newBasicRisk.setName(newEnterprise.getName());
        newBasicRisk.setSocialCode(newEnterprise.getSocialCode());
        newBasicRisk.setEnterpriseId(newEnterprise.getId());
        if (!ObjectUtils.isEmpty(newEnterprise.getScaleStatus()) &&
                newEnterprise.getScaleStatus().equals(SCALE_STATUS_UP)){
            newBasicRisk.setScaleStatus(ONE);
        }
        if (!ObjectUtils.isEmpty(newEnterprise.getCurrentEvaluationStatus()) &&
                newEnterprise.getCurrentEvaluationStatus().equals(ONE)) {
            newBasicRisk.setCurrentEvaluation(ONE);
        }
        if (!ObjectUtils.isEmpty(newEnterprise.getOrderAndCard()) &&
                newEnterprise.getOrderAndCard().equals(ORDER_AND_CARD)) {
            newBasicRisk.setOrderAndCard(ONE);
        }
        if (!ObjectUtils.isEmpty(newEnterprise.getSafetyBckLevel()) ||
                !ObjectUtils.isEmpty(newEnterprise.getSafetyBckTime()) ||
                !ObjectUtils.isEmpty(newEnterprise.getSafetyMajorTime()) ||
                !ObjectUtils.isEmpty(newEnterprise.getSafetyPostTime())){
            newBasicRisk.setSafetyBck(ONE);
        }
        newBasicRisk.setRegionId(newEnterprise.getRegionTownId());
        newBasicRisk.setRegionParentId(newEnterprise.getRegionCountyId());
        basicRiskMapper.insert(newBasicRisk);
    }
    /**
     * 企业申报审核校验.
     *
     * @param enterpriseDeclaration
     * @param reviewStep
     */
    private void validationReviewStatus(EnterpriseDeclaration enterpriseDeclaration, int reviewStep) {
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        Long regionId = enterpriseDeclaration.getRegionTownId();
        if (null == currentUserRegionIds || !currentUserRegionIds.contains(regionId)) {
            throw new EpsException("当前用户所在组织无权审核该企业申报");
        }
        if (null == enterpriseDeclaration) {
            throw new EpsException("企业申报信息不存在，请联系管理员");
        } else if (reviewStep == ENTERPRISE_DECLARATION_REVIEW_STEP_SECOND
                && enterpriseDeclaration.getStatus() != ENTERPRISE_DECLARATION_STATUS_WAIT_REVIEW) {
            throw new EpsException("企业申报信息状态不正确，不能进行审核!");
        }
    }

    /**
     * Create enterprise.
     * 企业信息表新增复审通过企业
     * @param enterpriseDeclaration The enterprise declaration.
     */
    private void createEnterprise(EnterpriseDeclaration enterpriseDeclaration) {
        LambdaQueryWrapper<Enterprise> enterpriseQueryWrapper = new LambdaQueryWrapper<>();
        enterpriseQueryWrapper.eq(Enterprise::getCreatorId, enterpriseDeclaration.getCreatorId());
        Enterprise enterprise = enterpriseMapper.selectOne(enterpriseQueryWrapper);
        EnterpriseRisk enterpriseRiskWrapper = new EnterpriseRisk();
        enterpriseRiskWrapper.setEnterpriseId(enterpriseDeclaration.getId());
        List<EnterpriseRisk> enterpriseRisks = enterpriseRiskService.findEnterpriseRisks(enterpriseRiskWrapper);
        Enterprise newEnterprise = (Enterprise) DataTransfer.transfer(enterpriseDeclaration, Enterprise.class);
        newEnterprise.setKeyEnterprise(EnterpriseUtil.getEnterpriseKey(newEnterprise, enterpriseRisks));
        if (!ObjectUtils.isEmpty(enterprise)){
            List<HiddenDanger> hiddenDangers = hiddenDangerMapper.selectList(new QueryWrapper<HiddenDanger>()
                    .eq("creator_id", enterprise.getCreatorId()));
            if (ObjectUtils.isEmpty(hiddenDangers)){
                newEnterprise.setHiddenStatus(HIDDEN_NO);
            }else {
                newEnterprise.setHiddenStatus(HIDDEN_OK);
            }
            List<EmergencyPlan> emergencyPlans = emergencyPlanMapper.selectList(new QueryWrapper<EmergencyPlan>()
                    .eq("creator_id", enterprise.getCreatorId()));
            if (ObjectUtils.isEmpty(emergencyPlans)) {
                newEnterprise.setPlanStatus(PLAN_NO);
            }else {
                newEnterprise.setPlanStatus(PLAN_OK);
            }
        }else {
            newEnterprise.setHiddenStatus(HIDDEN_NO);
            newEnterprise.setPlanStatus(PLAN_NO);
        }
        //保存统计数据
        BasicRisk newBasicRisk=new BasicRisk();
        String risk = getRiskType(newEnterprise,newBasicRisk);
        newEnterprise.setRiskType(risk);
        newEnterprise.setCreateTime(new Date());
        if (null == enterprise) {
            newEnterprise.setId(null);
            enterpriseMapper.insert(newEnterprise);
        } else {
            newEnterprise.setId(enterprise.getId());
            newEnterprise.setBusinessAddress(enterprise.getBusinessAddress());
            newEnterprise.setAddress(enterprise.getAddress());
            enterpriseMapper.updateById(newEnterprise);
        }
        for (EnterpriseRisk enterpriseRisk : enterpriseRisks) {
            enterpriseRisk.setEnterpriseId(newEnterprise.getId());
        }
        enterpriseRiskWrapper.setEnterpriseId(newEnterprise.getId());
        enterpriseRiskService.remove(new LambdaQueryWrapper<>(enterpriseRiskWrapper));
        updateBasicRisk(basicRiskMapper,newEnterprise, newBasicRisk);
        enterpriseRiskService.saveBatch(enterpriseRisks);
    }

    /**
     * 创建企业申报信息
     *
     * @param enterpriseDeclarationRequest
     * @return
     */
    private Long createNewEnterpriseDeclaration(EnterpriseDeclarationRequest enterpriseDeclarationRequest) {
        EnterpriseDeclaration enterpriseDeclaration = (EnterpriseDeclaration) DataTransfer.transfer(enterpriseDeclarationRequest, EnterpriseDeclaration.class);
        enterpriseDeclaration.setFirstReviewerId(null);
        enterpriseDeclaration.setFirstReviewRemark(null);
        enterpriseDeclaration.setFirstReviewStatus(null);
        enterpriseDeclaration.setFirstReviewTime(null);
        enterpriseDeclaration.setSecondReviewerId(null);
        enterpriseDeclaration.setSecondReviewRemark(null);
        enterpriseDeclaration.setSecondReviewStatus(null);
        enterpriseDeclaration.setSecondReviewTime(null);
        enterpriseDeclaration.setCreateTime(new Date());
        enterpriseDeclaration.setModifyTime(new Date());
        enterpriseDeclaration.setCreatorId(EpsUtil.getCurrentUser().getUserId());
        enterpriseDeclaration.setRisks(enterpriseDeclarationRequest.getRisks().toJSONString());
        enterpriseDeclaration.setCreatorMobile(EpsUtil.getCurrentUser().getMobile());
        this.save(enterpriseDeclaration);
        JSONObject jsonObject = enterpriseDeclarationRequest.getRisks();
        List<EnterpriseRisk> enterpriseRisks = EnterpriseUtil.generateEnterpriseRisk(enterpriseDeclaration.getId(), jsonObject);
        if (!enterpriseRisks.isEmpty()) {
            enterpriseRiskService.saveBatch(enterpriseRisks);
        }
        return enterpriseDeclaration.getId();
    }

    /**
     * 获取是否四涉一限一使用
     * @param enterpriseDeclaration enterpriseDeclaration
     * @return String
     */
    public String getRiskType(Enterprise enterpriseDeclaration,BasicRisk basicRisk) {
        StringBuilder risk=new StringBuilder();
        //解析风险json
        JSONObject jsonObject = JSON.parseObject(enterpriseDeclaration.getRisks());
        //涉燃爆粉尘
        Boolean  i7 = (Boolean) JSON.parseObject(jsonObject.get("i1").toString()).get("i7");
        //涉高温熔融
        Boolean  i93 = (Boolean) JSON.parseObject(jsonObject.get("i2").toString()).get("i93");
        //涉冶金煤气
        Boolean  i159 = (Boolean) JSON.parseObject(jsonObject.get("i3").toString()).get("i159");
        //涉氨制冷
        Boolean  i178 = (Boolean) JSON.parseObject(jsonObject.get("i4").toString()).get("i178");
        //有限空间
        Boolean  i221 = (Boolean) JSON.parseObject(jsonObject.get("i5").toString()).get("i221");
        //其他燃爆毒危化品使用
        Boolean  i313 = (Boolean) JSON.parseObject(jsonObject.get("i6").toString()).get("i313");
        if(i7){
            risk.append("i1");
            basicRisk.setRiskI1(ONE);
        }
        if(i93){
            risk.append("i2");
            basicRisk.setRiskI2(ONE);
        }
        if(i159){
            risk.append("i3");
            basicRisk.setRiskI3(ONE);
        }
        if(i178){
            risk.append("i4");
            basicRisk.setRiskI4(ONE);
        }
        if(i221){
            risk.append("i5");
            basicRisk.setRiskI5(ONE);
        }
        if(i313){
            risk.append("i6");
            basicRisk.setRiskI6(ONE);
        }
        return risk.toString();
    }
    private void queryDetail(EnterpriseDeclaration enterpriseDeclaration) {
        if (Objects.isNull(enterpriseDeclaration.getIndustry())) {
            enterpriseDeclaration.setIndustry(industryMapper.selectById(enterpriseDeclaration.getIndustryId()));
        }
        if (Objects.isNull(enterpriseDeclaration.getRegionCounty())) {
            enterpriseDeclaration.setRegionCounty(regionMapper.selectById(enterpriseDeclaration.getRegionCountyId()));
        }
        if (Objects.isNull(enterpriseDeclaration.getRegionTown())) {
            enterpriseDeclaration.setRegionTown(regionMapper.selectById(enterpriseDeclaration.getRegionTownId()));
        }
    }
}
