package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.SpecialCertificate;

import java.io.IOException;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2023/1/9 13:38
 * @Version: 1.0
 */
public interface SpecialCertificateService extends IService<SpecialCertificate> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param specialCertificate specialCertificate
     * @return IPage<specialCertificate>
     */
    IPage<SpecialCertificate> findSpecialCertificates(QueryRequest request, SpecialCertificate specialCertificate);

    /**
     * 新增
     * @param specialCertificate specialCertificate
     * @throws IOException IOException
     */
    String createSpecialCertificate(SpecialCertificate specialCertificate);

    /**
     * 详情
     * @param id  id
     * @return
     */
    SpecialCertificate findSpecialCertificate(Long id);


    /**
     * 删除
     *
     */
    void deleteSpecialCertificate(Long id);

    /**
     * 删除文件
     * @param id
     * @param filePath
     */
    void deleteFile(Long id, String filePath);
}
