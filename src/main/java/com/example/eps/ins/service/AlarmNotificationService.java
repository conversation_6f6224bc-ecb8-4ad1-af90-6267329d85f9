package com.example.eps.ins.service;

import com.example.eps.ins.common.entity.safety.SafetyMonitorEvent;

/**
 * 告警通知服务接口
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
public interface AlarmNotificationService {

    /**
     * 发送安全帽违规告警通知
     * 
     * @param event 安全监控事件
     * @return 是否发送成功
     */
    boolean sendSafetyHelmetAlarm(SafetyMonitorEvent event);

    /**
     * 发送反光衣违规告警通知
     * 
     * @param event 安全监控事件
     * @return 是否发送成功
     */
    boolean sendReflectiveVestAlarm(SafetyMonitorEvent event);

    /**
     * 发送人员入侵告警通知
     * 
     * @param event 安全监控事件
     * @return 是否发送成功
     */
    boolean sendPersonIntrusionAlarm(SafetyMonitorEvent event);

    /**
     * 发送通用安全告警通知
     * 
     * @param event 安全监控事件
     * @return 是否发送成功
     */
    boolean sendGenericSafetyAlarm(SafetyMonitorEvent event);

    /**
     * 发送邮件通知
     * 
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @return 是否发送成功
     */
    boolean sendEmailNotification(String to, String subject, String content);

    /**
     * 发送短信通知
     * 
     * @param phone 手机号
     * @param message 短信内容
     * @return 是否发送成功
     */
    boolean sendSmsNotification(String phone, String message);

    /**
     * 发送系统内部消息通知
     * 
     * @param userId 用户ID
     * @param title 标题
     * @param content 内容
     * @return 是否发送成功
     */
    boolean sendSystemMessage(Long userId, String title, String content);
}
