package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EquipmentMaintain;

import java.io.IOException;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/10/11 9:51
 * @Version: 1.0
 */

public interface EquipmentMaintainService extends IService<EquipmentMaintain> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param trainEducation trainEducation
     * @return IPage<EmergencyPlan>
     */
    IPage<EquipmentMaintain> findEquipmentMaintains(QueryRequest request, EquipmentMaintain trainEducation);

    /**
     * 新增
     * @param trainEducation trainEducation
     * @throws IOException IOException
     */
    String createEquipmentMaintain(EquipmentMaintain trainEducation);


    /**
     * 详情
     * @param id  id
     */
    EquipmentMaintain findEquipmentMaintain(Long id);


    /**
     * 删除
     *
     */
    void deleteEquipmentMaintain(Long id);

    void deleteFile(Long id, String filePath);
}
