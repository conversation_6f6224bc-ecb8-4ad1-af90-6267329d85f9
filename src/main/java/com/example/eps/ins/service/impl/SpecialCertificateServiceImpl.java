package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.SpecialCertificate;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.SpecialCertificateMapper;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.SpecialCertificateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/9 13:38
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class SpecialCertificateServiceImpl extends ServiceImpl<SpecialCertificateMapper,SpecialCertificate>
        implements SpecialCertificateService {

    private final SpecialCertificateMapper specialCertificateMapper;
    private final FdfsClientService fdfsClientService;
    private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

    @Override
    public IPage<SpecialCertificate> findSpecialCertificates(QueryRequest request, SpecialCertificate specialCertificate) {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        if (!ObjectUtils.isEmpty(specialCertificate.getCreatorId())){
            userId=specialCertificate.getCreatorId();
        }
        LambdaQueryWrapper<SpecialCertificate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpecialCertificate::getCreatorId, userId);
        if (!ObjectUtils.isEmpty(specialCertificate.getSpecialType())) {
            queryWrapper.eq(SpecialCertificate::getSpecialType, specialCertificate.getSpecialType());
        }
//        if (!ObjectUtils.isEmpty(finiteSpaceWork.getSpaceName())) {
//            queryWrapper.like(FiniteSpaceWork::getSpaceName, finiteSpaceWork.getSpaceName());
//        }
        queryWrapper.orderByDesc(SpecialCertificate::getCreateTime);
        Page<SpecialCertificate> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createSpecialCertificate(SpecialCertificate specialCertificate) {
        //获取所有的企业申报信息
        List<EnterpriseDeclarationResponse> enterprise =
                enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
        //获取所有申报状态
        List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(enterprise)){
            throw new EpsException("请完善企业信息后创建企业应急预案信息！");
        }
        if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
            throw new EpsException("企业信息暂未审核通过，请审核通过后创建特种作业许可证信息！");
        }
        if (ObjectUtils.isEmpty(specialCertificate.getSpecialFile())){
            specialCertificate.setSpecialFile("[]");
        }
        specialCertificate.setCreateTime(new Date());
        if (ObjectUtils.isEmpty(specialCertificate.getId())) {
            CurrentUser currentUser = EpsUtil.getCurrentUser();
            specialCertificate.setCreatorId(currentUser.getUserId());
            specialCertificate.setRegionId(enterprise.get(0).getRegionTownId());
            this.save(specialCertificate);
        } else {
            specialCertificate.setCreatorId(specialCertificate.getCreatorId());
            this.update(specialCertificate, new QueryWrapper<SpecialCertificate>()
                    .eq("id", specialCertificate.getId()));
        }
        return "";
    }

    @Override
    public SpecialCertificate findSpecialCertificate(Long id) {
        return specialCertificateMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSpecialCertificate(Long id) {
        LambdaQueryWrapper<SpecialCertificate> userWarpper = new LambdaQueryWrapper<>();
        userWarpper.eq(SpecialCertificate::getId, id);
        SpecialCertificate specialCertificate = this.getById(id);
        if (specialCertificate == null) {
            return;
        }
        JSONArray jsonArray = JSON.parseArray(specialCertificate.getSpecialFile());
        specialCertificateMapper.delete(userWarpper);
        for (Object obj : jsonArray) {
            fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(Long id, String filePath) {
        if (!ObjectUtils.isEmpty(id)) {
            SpecialCertificate specialCertificate = specialCertificateMapper.selectById(id);
            if (ObjectUtils.isEmpty(specialCertificate)) {
                return;
            }
            JSONArray jsonArray = JSON.parseArray(specialCertificate.getSpecialFile());
            JSONArray json = new JSONArray();
            for (Object obj : jsonArray) {
                String filePath1 = ((JSONObject) obj).getString("filePath");
                if (filePath1.equals(filePath)) {
                    continue;
                }
                Map<String, Object> map = new HashMap<>();
                map.put("fileUrl", ((JSONObject) obj).getString("fileUrl"));
                map.put("filePath", ((JSONObject) obj).getString("filePath"));
                map.put("fileName", ((JSONObject) obj).getString("fileName"));
                json.add(EpsUtil.toJsonObj(map));
            }
            specialCertificate.setSpecialFile(json.toString());
            specialCertificateMapper.updateById(specialCertificate);
        }
        fdfsClientService.deleteFile(filePath);
    }
}
