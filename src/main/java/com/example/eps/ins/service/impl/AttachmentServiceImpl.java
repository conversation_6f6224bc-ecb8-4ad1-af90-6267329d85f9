package com.example.eps.ins.service.impl;

import com.example.eps.ins.common.exception.ApplicationError;
import com.example.eps.ins.common.exception.ErrorCodes;
import com.example.eps.ins.common.utils.FileUtil;
import com.example.eps.ins.common.handler.FileNotFoundExecption;
import com.example.eps.ins.service.IAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AttachmentServiceImpl implements IAttachmentService {
    @Value("${business.fileRootPath}")
    private String fileRootPath;

    public String getFileRootPath() {
        return fileRootPath;
    }

    @Override
    public List<String> save(List<MultipartFile> multipartFiles) {
        List tempList = multipartFiles.stream().filter(multipartFile -> {
            return StringUtils.isNoneBlank(multipartFile.getOriginalFilename());
        }).collect(Collectors.toList());
        if(tempList.size() == 0){
            return tempList;
        }
        return FileUtil.saveFile(tempList,fileRootPath);
    }

    @Override
    public File download(String filePath) {
        String path = this.getFileRootPath() + "/" + filePath;
        log.debug("download file:"+path);
        File file = new File(path);
        if(!file.exists()){
            ApplicationError error = new ApplicationError(ErrorCodes.FILE_NOT_FOUND);
            log.error("not found this file,file path is "+path+".");
            throw new FileNotFoundExecption("没有该文件",error);
        }
        if(!file.isFile()){
            log.error("file is not file type,file path is "+path+".");
            ApplicationError error = new ApplicationError(ErrorCodes.FILE_NOT_FILE_TYPE);
            throw new FileNotFoundExecption("选择的文件不是文件类型",error);
        }
        return new File(path);
    }
}
