package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.dto.enterprise.EmergencydrillVO;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Emergencydrill;

import java.io.IOException;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2022/10/11 9:51
 * @Version: 1.0
 */

public interface IEmergencyDrillService extends IService<Emergencydrill> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param emergencyPlan enterpriseRisk
     * @return IPage<EmergencyPlan>
     */
    IPage<Emergencydrill> findEmergencyDrills(QueryRequest request, Emergencydrill emergencyPlan);

    /**
     * 新增
     * @param emergencyPlan emergencyPlan
     * @throws IOException IOException
     */
    String createEmergencyDrill(EmergencydrillVO emergencyPlan);


    /**
     * 详情
     * @param id  id
     */
    Emergencydrill findEmergencyDrill(Long id);


    /**
     * 删除
     *
     */
    void deleteEmergencyDrill(Long id);

    void deleteFile(Long id, String filePath);
}
