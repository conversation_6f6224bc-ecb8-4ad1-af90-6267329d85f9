package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.eps.ins.common.dto.report.model.RiskContentVo;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.entity.enterprise.RiskContent;

import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023/6/10 11:24
 * @Version: 1.0
 */
public interface RiskContentService {
    List<RiskContent> getList(RiskContentVo dto);

    IPage<RiskContent> getSystemList(RiskContentVo dto);

    void add(RiskContentVo dto);

    IPage<RiskContent> getAllList(RiskContentVo dto);

    void delete(Long id);

    IPage<Enterprise> enterpriseList(RiskContentVo dto);
}
