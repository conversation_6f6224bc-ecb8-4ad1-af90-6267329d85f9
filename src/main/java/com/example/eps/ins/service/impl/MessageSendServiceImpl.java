package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.entity.enterprise.EnterpriseUser;
import com.example.eps.ins.common.entity.enterprise.MessageSendOne;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.entity.system.AllMessageUser;
import com.example.eps.ins.common.entity.system.MessageAll;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.EnterpriseMapper;
import com.example.eps.ins.mapper.MessageAllMapper;
import com.example.eps.ins.mapper.MessageAllUserMapper;
import com.example.eps.ins.mapper.MessageSendMapper;
import com.example.eps.ins.mapper.*;
import com.example.eps.ins.common.dto.enterprise.NoSeeResp;
import com.example.eps.ins.service.MessageSendService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/10 14:37
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class MessageSendServiceImpl extends ServiceImpl<MessageSendMapper, MessageSendOne>
        implements MessageSendService {

    private final MessageSendMapper messageSendMapper;
    private final MessageAllUserMapper messageAllUserMapper;
    private final MessageAllMapper messageAllMapper;
    private final EnterpriseMapper enterpriseMapper;
    private final EnterpriseUserMapper enterpriseUser;
    private final RegionMapper regionMapper;

    private static final Integer MESSAGE_STATUS_OK=1;
    private static final Integer MESSAGE_STATUS_NO=0;

    @Override
    public String updateStatus(Long id) {
        MessageSendOne message = messageSendMapper.selectById(id);
        message.setStatus(MESSAGE_STATUS_OK);
        messageSendMapper.updateById(message);
        return " ";
    }

    @Override
    public NoSeeResp noSeeNum() {
        NoSeeResp noSeeResp=new NoSeeResp();
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        Enterprise enterprise = enterpriseMapper.selectOne(new QueryWrapper<Enterprise>().
                eq("creator_id", currentUser.getUserId()));
        QueryWrapper<MessageSendOne> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("receive_user",currentUser.getUserId());
        queryWrapper.eq("status",MESSAGE_STATUS_NO);
        List<MessageSendOne> messageSendOnes = messageSendMapper.selectList(queryWrapper);
        noSeeResp.setOneToOne(messageSendOnes.size());
        QueryWrapper<AllMessageUser> messageAllQueryWrapper=new QueryWrapper<>();
        messageAllQueryWrapper.eq("user_id",currentUser.getUserId());
        //获取已读列表
        int allMessageUsers = messageAllUserMapper.selectCount(messageAllQueryWrapper);
        //获取该登录账号所以未读分享消息
        int industryId = messageAllMapper.selectCount(new QueryWrapper<MessageAll>()
                .like("industry_id", enterprise.getIndustryId()));
        noSeeResp.setOneToAll(industryId-allMessageUsers);
        return noSeeResp;
    }

    @Override
    public IPage<MessageSendOne> messageList(QueryRequest request, MessageSendOne messageSendOne) {
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        LambdaQueryWrapper<MessageSendOne> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageSendOne::getReceiveUser, currentUser.getUserId());
        queryWrapper.orderByDesc(MessageSendOne::getCreateTime);
        Page<MessageSendOne> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }
    @Override
    public String sendMessage(MessageSendOne messageSendOne) {
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        EnterpriseUser enterpriseUser = this.enterpriseUser.selectById(messageSendOne.getReceiveUser());
        Enterprise enterprise=enterpriseMapper.selectOne(new QueryWrapper<Enterprise>()
                .eq("creator_id",enterpriseUser.getUserId()));
        messageSendOne.setReceiveName(enterprise.getName());
        messageSendOne.setReceivePhone(enterpriseUser.getMobile());
        messageSendOne.setSendUser(currentUser.getUserId());
        messageSendOne.setStatus(MESSAGE_STATUS_NO);
        Region region = regionMapper.selectById(currentUser.getRegionId());
        messageSendOne.setSendName(region.getName()+"-"+currentUser.getNickName());
        messageSendOne.setCreateTime(new Date());
        messageSendMapper.insert(messageSendOne);
        return " ";
    }

    @Override
    public void deleteMessage(Long id) {
        messageSendMapper.deleteById(id);
    }
}
