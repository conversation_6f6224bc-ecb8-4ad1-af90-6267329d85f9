package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.dreamyoung.mprelation.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.example.eps.ins.common.bean.EnterpriseDeclarationRequest;
import com.example.eps.ins.common.bean.EnterpriseResponse;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.constant.EnterpriseConstant;
import com.example.eps.ins.common.constant.EpsConstant;
import com.example.eps.ins.common.constant.RegionConstant;
import com.example.eps.ins.common.entity.enterprise.*;
import com.example.eps.ins.common.entity.system.EnterpriseImport;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.DataTransfer;
import com.example.eps.ins.common.utils.EnterpriseUtil;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.utils.SortUtil;
import com.example.eps.ins.mapper.*;
import com.example.eps.ins.common.model.EnterpriseData;
import com.example.eps.ins.common.model.EnterpriseVo;
import com.example.eps.ins.common.model.EpsError;
import com.example.eps.ins.common.model.ReportVo;
import com.example.eps.ins.service.IEnterpriseRiskService;
import com.example.eps.ins.service.IEnterpriseService;
import com.example.eps.ins.service.IRegionService;
import com.wuwenze.poi.ExcelKit;
import com.wuwenze.poi.annotation.ExcelField;
import com.wuwenze.poi.handler.ExcelReadHandler;
import com.wuwenze.poi.pojo.ExcelErrorField;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.example.eps.ins.common.constant.EnterpriseConstant.*;
import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.*;

/**
 * 企业信息表 Service实现
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:42
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class EnterpriseServiceImpl extends ServiceImpl<EnterpriseMapper, Enterprise> implements IEnterpriseService {


    private static final String STRING_ZEOR="0";
    private static final long NOT_IN=0L;
    //没有建立填报内容
    private static final Integer REPORT_STATUS=0;
    private final EnterpriseMapper enterpriseMapper;
    private final IEnterpriseRiskService enterpriseRiskServiceImpl;
    private final EnterpriseDeclarationMapper enterpriseDeclarationMapper;
    private final IRegionService regionService;
    private final IndustryMapper industryMapper;
    private final RegionMapper regionMapper;
    private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;
    private final BasicRiskMapper basicRiskMapper;
    private final HiddenDangerMapper hiddenDangerMapper;
    private final EmergencyPlanMapper emergencyPlanMapper;
    private final EnterpriseUserMapper enterpriseUserMapper;
    @Value("${user.password}")
    private String paaword;

    /*
     * 查询条件含地区时，不传地区参数，则查看当前用户所属组织及下级组织；
     * 查询条件含地区时，传地区参数，则校验地区参数是否归属当前用户所属组织及下级组织；
     * 查询条件含地区时，传地区参数，且地区参数归属当前用户所属组织及下级组织时，需比对地区参数为区或者镇；
     */
    @Override
    public IPage<Enterprise> findEnterprises(EnterpriseVo enterprise) {
        List<Long> ids=new ArrayList<>();
        if (!ObjectUtils.isEmpty(enterprise.getReportType())){
            if (ObjectUtils.isEmpty(enterprise.getReportTime())){
                ids=enterpriseMapper.selectReport(enterprise.getReportType(),
                        null,
                        null,
                        enterprise.getColumn());
            }else {
                ids=enterpriseMapper.selectReport(enterprise.getReportType(),
                        enterprise.getReportTime().get(0),
                        enterprise.getReportTime().get(1),
                        enterprise.getColumn());
            }
        }
        //添加一个不存在的id避免in方法发生错误
        ids.add(NOT_IN);
        Enterprise queryEnterprise = new Enterprise();
        queryEnterprise.setIndustryId(enterprise.getIndustryId());
        queryEnterprise.setNature(enterprise.getNature());
        queryEnterprise.setKeyEnterprise(enterprise.getKeyEnterprise());
        queryEnterprise.setPeopleNumber(enterprise.getPeopleNumber());
        LambdaQueryWrapper<Enterprise> queryWrapper = new LambdaQueryWrapper<>(queryEnterprise);
        if (!ObjectUtils.isEmpty(enterprise.getReportStatus())){
            //没有创建
            if (enterprise.getReportStatus().equals(REPORT_STATUS)){
                queryWrapper.notIn(Enterprise::getCreatorId,ids);
            }else {
                queryWrapper.in(Enterprise::getCreatorId,ids);
            }
        }
        List<String> riskTypeSearch = enterprise.getRiskTypeSearch();
        int count = EpsConstant.COUNT_ONE;
//        if (riskTypeSearch != null && riskTypeSearch.size() > EpsConstant.COUNT_ZEOR) {
//            for (String typeSearch : riskTypeSearch) {
//                if (count == EpsConstant.COUNT_ONE) {
//                    if (riskTypeSearch.size() == EpsConstant.COUNT_ONE) {
//                        queryWrapper.like(Enterprise::getRiskType, typeSearch);
//                    } else {
//                        queryWrapper.apply("(risk_type LIKE CONCAT('%','" + typeSearch + "','%')");
//                    }
//                } else if (count == riskTypeSearch.size()) {
//                    queryWrapper.or().apply("risk_type LIKE CONCAT('%','" + typeSearch + "','%'))");
//                } else {
//                    queryWrapper.or().apply("risk_type LIKE CONCAT('%','" + typeSearch + "','%')");
//                }
//                count++;
//            }
//        }
        if (!StringUtils.isEmpty(enterprise.getTradeType())){
            if (EpsConstant.TRADE_TYPE.equals(enterprise.getTradeType())){
                queryWrapper.apply("trade_type = '[]'");
            }else {
                queryWrapper.apply("trade_type <> '[]'");
            }
        }
        if (!StringUtils.isEmpty(enterprise.getName())) {
            queryWrapper.like(Enterprise::getName, enterprise.getName());
        }
        if (!ObjectUtils.isEmpty(enterprise.getSearchTime())
                && enterprise.getSearchTime().size()>0){
            queryWrapper.apply("create_time BETWEEN '"+enterprise.getSearchTime().get(0)+"' and '"+
                    enterprise.getSearchTime().get(1)+"'");
        }
        if (!ObjectUtils.isEmpty(enterprise.getCurrentEvaluationStatus())){
            queryWrapper.eq(Enterprise::getCurrentEvaluationStatus,enterprise.getCurrentEvaluationStatus());
        }
        if (!ObjectUtils.isEmpty(enterprise.getScaleStatus())){
            queryWrapper.eq(Enterprise::getScaleStatus,enterprise.getScaleStatus());
        }
        if (!ObjectUtils.isEmpty(enterprise.getHiddenStatus())){
            queryWrapper.eq(Enterprise::getHiddenStatus,enterprise.getHiddenStatus());
        }
        if (!ObjectUtils.isEmpty(enterprise.getPlanStatus())){
            queryWrapper.eq(Enterprise::getPlanStatus,enterprise.getPlanStatus());
        }
        if (!ObjectUtils.isEmpty(enterprise.getOrderAndCard())){
            queryWrapper.eq(Enterprise::getOrderAndCard,enterprise.getOrderAndCard());
        }
        if (!ObjectUtils.isEmpty(enterprise.getSafetyBck())){
            if (enterprise.getSafetyBck().equals(EpsConstant.SAFETY_BCK_NO)){
                queryWrapper.apply("(safety_bck_level is not null or safety_bck_time is not null or safety_major_time " +
                        "is not null or safety_post_time is not null)");
                if (!ObjectUtils.isEmpty(enterprise.getSafetyBckLevel())){
                    queryWrapper.eq(Enterprise::getSafetyBckLevel,enterprise.getSafetyBckLevel());
                }
                if (!ObjectUtils.isEmpty(enterprise.getSafetyMajorTime())){
                    if(enterprise.getSafetyMajorTime().equals(EpsConstant.SAFETY_MAJOR_POST)){
                        queryWrapper.isNotNull(Enterprise::getSafetyMajorTime);
                    }else {
                        queryWrapper.isNull(Enterprise::getSafetyMajorTime);
                    }
                }
                if (!ObjectUtils.isEmpty(enterprise.getSafetyPostTime())) {
                    if (enterprise.getSafetyPostTime().equals(EpsConstant.SAFETY_MAJOR_POST)) {
                      queryWrapper.isNotNull(Enterprise::getSafetyPostTime);
                    } else {
                      queryWrapper.isNull(Enterprise::getSafetyPostTime);
                    }
                }
            }else {
                queryWrapper.apply("(safety_bck_level is null and safety_bck_time is null and safety_major_time " +
                        "is null and safety_post_time is null)");
            }
        }
        if (!ObjectUtils.isEmpty(enterprise.getRisks())){
            if (enterprise.getRisks().equals(STRING_ZEOR)) {
                queryWrapper.eq(Enterprise::getRiskType,"");
            }else {
              if (riskTypeSearch != null && riskTypeSearch.size() > EpsConstant.COUNT_ZEOR) {
                for (String typeSearch : riskTypeSearch) {
                  if (count == EpsConstant.COUNT_ONE) {
                    if (riskTypeSearch.size() == EpsConstant.COUNT_ONE) {
                      queryWrapper.like(Enterprise::getRiskType, typeSearch);
                    } else {
                      queryWrapper.apply("(risk_type LIKE CONCAT('%','" + typeSearch + "','%')");
                    }
                  } else if (count == riskTypeSearch.size()) {
                    queryWrapper.or().apply("risk_type LIKE CONCAT('%','" + typeSearch + "','%'))");
                  } else {
                    queryWrapper.or().apply("risk_type LIKE CONCAT('%','" + typeSearch + "','%')");
                  }
                  count++;
                }
              }else {
                queryWrapper.ne(Enterprise::getRiskType,"");
              }
            }
        }
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        Long regionCountyId = enterprise.getRegionCountyId();
        if (null == regionCountyId) {
            queryWrapper.in(Enterprise::getRegionTownId, currentUserRegionIds);
        } else if (!currentUserRegionIds.contains(regionCountyId)) {
            throw new EpsException("当前用户所在组织无权查看该地区企业");
        } else {
            queryWrapper.and(wrapper -> {
                wrapper.eq(Enterprise::getRegionTownId, regionCountyId).or().eq(Enterprise::getRegionCountyId, regionCountyId);
            });
        }
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        Page<Enterprise> page = new Page<>(enterprise.getPageNum(), enterprise.getPageSize());
        SortUtil.handlePageSort(enterprise, page, "id", EpsConstant.ORDER_DESC, false);
        IPage<Enterprise> response = this.page(page, queryWrapper);
        response.getRecords().forEach(a -> {
              a.setRegionCounty(regionService.getById(a.getRegionCountyId()));
              a.setRegionTown(regionService.getById(a.getRegionTownId()));
              EnterpriseUser enterpriseUser = enterpriseUserMapper.selectById(a.getCreatorId());
              if (!ObjectUtils.isEmpty(enterpriseUser)) {
                a.setCreatorPhone(enterpriseUser.getMobile());
              }
              a.setNoSee(enterpriseMapper.selectNoSee(a.getCreatorId(),currentUser.getUserId()));
            });
        return response;
    }

    @Override
    public List<Enterprise> listEnterprises(EnterpriseVo enterprise) {
        List<Long> ids=new ArrayList<>();
        if (!ObjectUtils.isEmpty(enterprise.getReportType())){
            if (ObjectUtils.isEmpty(enterprise.getReportTime())){
                ids=enterpriseMapper.selectReport(enterprise.getReportType(),
                        null,
                        null,
                        enterprise.getColumn());
            }else {
                ids=enterpriseMapper.selectReport(enterprise.getReportType(),
                        enterprise.getReportTime().get(0),
                        enterprise.getReportTime().get(1),
                        enterprise.getColumn());
            }
        }
        //添加一个不存在的id避免in方法发生错误
        ids.add(NOT_IN);
        Enterprise queryEnterprise = new Enterprise();
        queryEnterprise.setIndustryId(enterprise.getIndustryId());
        queryEnterprise.setNature(enterprise.getNature());
        queryEnterprise.setKeyEnterprise(enterprise.getKeyEnterprise());
        queryEnterprise.setPeopleNumber(enterprise.getPeopleNumber());
        LambdaQueryWrapper<Enterprise> queryWrapper = new LambdaQueryWrapper<>(queryEnterprise);
        if (!ObjectUtils.isEmpty(enterprise.getReportStatus())){
            //没有创建
            if (enterprise.getReportStatus().equals(REPORT_STATUS)){
                queryWrapper.notIn(Enterprise::getCreatorId,ids);
            }else {
                queryWrapper.in(Enterprise::getCreatorId,ids);
            }
        }
        List<String> riskTypeSearch = enterprise.getRiskTypeSearch();
        int count = EpsConstant.COUNT_ONE;
        if (riskTypeSearch != null && riskTypeSearch.size() > EpsConstant.COUNT_ZEOR) {
            for (String typeSearch : riskTypeSearch) {
                if (count == EpsConstant.COUNT_ONE) {
                    if (riskTypeSearch.size() == EpsConstant.COUNT_ONE) {
                        queryWrapper.like(Enterprise::getRiskType, typeSearch);
                    } else {
                        queryWrapper.apply("(risk_type LIKE CONCAT('%','" + typeSearch + "','%')");
                    }
                } else if (count == riskTypeSearch.size()) {
                    queryWrapper.or().apply("risk_type LIKE CONCAT('%','" + typeSearch + "','%'))");
                } else {
                    queryWrapper.or().apply("risk_type LIKE CONCAT('%','" + typeSearch + "','%')");
                }
                count++;
            }
        }
        if (!StringUtils.isEmpty(enterprise.getTradeType())){
            if (EpsConstant.TRADE_TYPE.equals(enterprise.getTradeType())){
                queryWrapper.apply("trade_type = '[]'");
            }else {
                queryWrapper.apply("trade_type <> '[]'");
            }
        }
        if (!StringUtils.isEmpty(enterprise.getName())) {
            queryWrapper.like(Enterprise::getName, enterprise.getName());
        }
        if (!ObjectUtils.isEmpty(enterprise.getSearchTime())
                && enterprise.getSearchTime().size()>0){
            queryWrapper.apply("create_time BETWEEN '"+enterprise.getSearchTime().get(0)+"' and '"+
                    enterprise.getSearchTime().get(1)+"'");
        }
        if (!ObjectUtils.isEmpty(enterprise.getCurrentEvaluationStatus())){
            queryWrapper.eq(Enterprise::getCurrentEvaluationStatus,enterprise.getCurrentEvaluationStatus());
        }
        if (!ObjectUtils.isEmpty(enterprise.getScaleStatus())){
            queryWrapper.eq(Enterprise::getScaleStatus,enterprise.getScaleStatus());
        }
        if (!ObjectUtils.isEmpty(enterprise.getHiddenStatus())){
            queryWrapper.eq(Enterprise::getHiddenStatus,enterprise.getHiddenStatus());
        }
        if (!ObjectUtils.isEmpty(enterprise.getPlanStatus())){
            queryWrapper.eq(Enterprise::getPlanStatus,enterprise.getPlanStatus());
        }
        if (!ObjectUtils.isEmpty(enterprise.getOrderAndCard())){
            queryWrapper.eq(Enterprise::getOrderAndCard,enterprise.getOrderAndCard());
        }
        if (!ObjectUtils.isEmpty(enterprise.getSafetyBck())){
            if (enterprise.getSafetyBck().equals(EpsConstant.SAFETY_BCK_NO)){
                queryWrapper.apply("(safety_bck_level is not null or safety_bck_time is not null or safety_major_time " +
                        "is not null or safety_post_time is not null)");
                if (!ObjectUtils.isEmpty(enterprise.getSafetyBckLevel())){
                    queryWrapper.eq(Enterprise::getSafetyBckLevel,enterprise.getSafetyBckLevel());
                }
                if (!ObjectUtils.isEmpty(enterprise.getSafetyMajorTime())){
                    if(enterprise.getSafetyMajorTime().equals(EpsConstant.SAFETY_MAJOR_POST)){
                        queryWrapper.isNotNull(Enterprise::getSafetyMajorTime);
                    }else {
                        queryWrapper.isNull(Enterprise::getSafetyMajorTime);
                    }
                }
                if (!ObjectUtils.isEmpty(enterprise.getSafetyPostTime())) {
                    if (enterprise.getSafetyPostTime().equals(EpsConstant.SAFETY_MAJOR_POST)) {
                        queryWrapper.isNotNull(Enterprise::getSafetyPostTime);
                    } else {
                        queryWrapper.isNull(Enterprise::getSafetyPostTime);
                    }
                }
            }else {
                queryWrapper.apply("(safety_bck_level is null and safety_bck_time is null and safety_major_time " +
                        "is null and safety_post_time is null)");
            }
        }
        if (!ObjectUtils.isEmpty(enterprise.getRisks())){
            if (enterprise.getRisks().equals(STRING_ZEOR)) {
                queryWrapper.isNull(Enterprise::getRiskType);
            }else {
                queryWrapper.isNotNull(Enterprise::getRiskType);
            }
        }
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        Long regionCountyId = enterprise.getRegionCountyId();
        if (null == regionCountyId) {
            queryWrapper.in(Enterprise::getRegionTownId, currentUserRegionIds);
        } else if (!currentUserRegionIds.contains(regionCountyId)) {
            throw new EpsException("当前用户所在组织无权查看该地区企业");
        } else {
            queryWrapper.and(wrapper -> {
                wrapper.eq(Enterprise::getRegionTownId, regionCountyId).or().eq(Enterprise::getRegionCountyId, regionCountyId);
            });
        }
        List<Enterprise> list = enterpriseMapper.selectList(queryWrapper);
        list.forEach(a->{
            a.setRegionCounty(regionService.getById(a.getRegionCountyId()));
            a.setRegionTown(regionService.getById(a.getRegionTownId()));
            EnterpriseUser enterpriseUser = enterpriseUserMapper.selectById(a.getCreatorId());
            if (!ObjectUtils.isEmpty(enterpriseUser)){
                a.setCreatorPhone(enterpriseUser.getMobile());
            }
        });
        return list;
    }

    @Override
    public EnterpriseResponse findEnterprise(Long id) {
        Enterprise enterprise = this.getById(id);
        if (null == enterprise) {
            throw new EpsException("企业信息不存在");
        }
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        Long regionId = enterprise.getRegionTownId();
        if (null == currentUserRegionIds || !currentUserRegionIds.contains(regionId)) {
            throw new EpsException("当前用户所在组织无权查看该企业");
        }
        queryDetail(enterprise);
        EnterpriseResponse response = (EnterpriseResponse) DataTransfer.transfer(enterprise, EnterpriseResponse.class);
//        response.setRisks(getEnterpriseRiskInfo(id));
        EnterpriseUser enterpriseUser = enterpriseUserMapper.selectById(response.getCreatorId());
        if (!ObjectUtils.isEmpty(enterpriseUser)){
            response.setCreatorPhone(enterpriseUser.getMobile());
        }
        return response;
    }

    @Override
    public List<Enterprise> findEnterprises(Enterprise enterprise) {
        LambdaQueryWrapper<Enterprise> queryWrapper = new LambdaQueryWrapper<>();
        // TODO 设置查询条件
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createEnterprise(EnterpriseDeclarationRequest request) {
//        Long creatorId = EpsUtil.getCurrentUser().getUserId();
        if (!ObjectUtils.isEmpty(request.getCreatorPhone())){

            EnterpriseUser enterpriseUser1 = enterpriseUserMapper.selectOne(new QueryWrapper<EnterpriseUser>()
                    .eq("mobile", request.getCreatorPhone()));
            if (!ObjectUtils.isEmpty(enterpriseUser1)){
                throw new EpsException("登录手机号重复!");
            }
            EnterpriseUser enterpriseUser=new EnterpriseUser();
            PasswordEncoder encoder = new BCryptPasswordEncoder();
            enterpriseUser.setCreateTime(new Date());
            enterpriseUser.setUpdateTime(new Date());
            enterpriseUser.setMobile(request.getCreatorPhone());
            enterpriseUser.setEnterpriseName(request.getName());
            enterpriseUser.setPassword(encoder.encode(paaword));
            enterpriseUserMapper.insert(enterpriseUser);
            request.setCreatorId(enterpriseUser.getUserId());
        }
        validateSocialCode(request.getSocialCode(), null,null);
        BasicRisk newBasicRisk = new BasicRisk();
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        //校验区县镇街
        validateRegion(currentUserRegionIds, request.getRegionCountyId(), request.getRegionTownId());

        Enterprise enterprise = (Enterprise) DataTransfer.transfer(request, Enterprise.class);
        enterprise.setRisks(request.getRisks().toJSONString());
//        enterprise.setCreatorId(creatorId);
//        enterprise.setModifyUserId(creatorId);
        enterprise.setCreateTime(new Date());
        enterprise.setModifyTime(new Date());
        if (!ObjectUtils.isEmpty(request.getRisks())) {
            String riskType = enterpriseDeclarationService.getRiskType(enterprise, newBasicRisk);
            enterprise.setRiskType(riskType);
        }
        this.save(enterprise);
        //写入申报表数据
        EnterpriseDeclaration enterpriseDeclaration = (EnterpriseDeclaration) DataTransfer.transfer(enterprise, EnterpriseDeclaration.class);
        enterpriseDeclaration.setCreatorMobile(request.getCreatorPhone());
        enterpriseDeclaration.setStatus(3);
        enterpriseDeclarationMapper.insert(enterpriseDeclaration);
        saveNewEnterpriseRisks(enterprise.getRisks(), enterpriseDeclaration.getId());
        List<EnterpriseRisk> enterpriseRisks = saveRisks(enterprise.getRisks(), enterprise.getId());
        enterprise.setKeyEnterprise(EnterpriseUtil.getEnterpriseKey(enterprise, enterpriseRisks));
        this.updateById(enterprise);
        enterpriseDeclarationService.updateBasicRisk(basicRiskMapper, enterprise, newBasicRisk);
        return enterprise.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnterprise(Long id, EnterpriseDeclarationRequest request) {
        Enterprise one = this.getById(id);
        if (null == one) {
            throw new EpsException("企业信息不存在");
        }
        if (!ObjectUtils.isEmpty(request.getCreatorPhone())){
            EnterpriseUser enterpriseUser=enterpriseUserMapper.selectById(request.getCreatorId());
            enterpriseUser.setCreateTime(new Date());
            enterpriseUser.setMobile(request.getCreatorPhone());
            List<EnterpriseUser> mobile = enterpriseUserMapper.selectList(new QueryWrapper<EnterpriseUser>()
                    .eq("mobile", request.getCreatorPhone()));
            if (!ObjectUtils.isEmpty(mobile)) {
                mobile.forEach(
                    a -> {
                    if (a.getMobile().equals(request.getCreatorPhone())
                    && !a.getUserId().equals(request.getCreatorId())) {
                        throw new EpsException("登录手机号重复");
                    }
                });
            }
            enterpriseUserMapper.updateById(enterpriseUser);
        }
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        Enterprise enterprise = (Enterprise) DataTransfer.transfer(request, Enterprise.class);
        BasicRisk newBasicRisk = new BasicRisk();
        enterprise.setId(id);
        enterprise.setRisks(request.getRisks().toJSONString());
        Long userId = EpsUtil.getCurrentUser().getUserId();
        validateSocialCode(enterprise.getSocialCode(), id,one.getCreatorId());
        validateRegion(currentUserRegionIds, request.getRegionCountyId(), request.getRegionTownId());
        String risks = enterprise.getRisks();
        //获取最近一次申报
        EnterpriseDeclaration lastDeclaration = currentUserEnterpriseDeclaration(enterprise.getCreatorId());
        if (null != lastDeclaration) {
            EnterpriseDeclaration enterpriseDeclaration = (EnterpriseDeclaration) DataTransfer.transfer(enterprise, EnterpriseDeclaration.class);
            enterpriseDeclaration.setId(lastDeclaration.getId());
            enterpriseDeclaration.setModifyTime(new Date());
            enterpriseDeclaration.setModifyUserId(userId);
            enterpriseDeclarationMapper.updateById(enterpriseDeclaration);
            saveNewEnterpriseRisks(risks, lastDeclaration.getId());

        }
        List<EnterpriseRisk> enterpriseRisks = saveNewEnterpriseRisks(risks, id);
        enterprise.setKeyEnterprise(EnterpriseUtil.getEnterpriseKey(enterprise, enterpriseRisks));
        enterprise.setModifyTime(new Date());
        enterprise.setModifyUserId(userId);
        List<HiddenDanger> hiddenDangers = hiddenDangerMapper.selectList(new QueryWrapper<HiddenDanger>()
                .eq("creator_id", enterprise.getCreatorId()));
        if (ObjectUtils.isEmpty(hiddenDangers)){
            enterprise.setHiddenStatus(EpsConstant.HIDDEN_NO);
        }else {
            enterprise.setHiddenStatus(EpsConstant.HIDDEN_OK);
        }
        List<EmergencyPlan> emergencyPlans = emergencyPlanMapper.selectList(new QueryWrapper<EmergencyPlan>()
                .eq("creator_id", enterprise.getCreatorId()));
        if (ObjectUtils.isEmpty(emergencyPlans)) {
            enterprise.setPlanStatus(EpsConstant.PLAN_NO);
        }else {
            enterprise.setPlanStatus(EpsConstant.PLAN_OK);
        }
        String riskType = enterpriseDeclarationService.getRiskType(enterprise, newBasicRisk);
        enterpriseDeclarationService.updateBasicRisk(basicRiskMapper, enterprise, newBasicRisk);
        enterprise.setRiskType(riskType);
        this.updateById(enterprise);
    }

    @Override
    public void updateEnterpriseAddress(Long id, Enterprise enterprise) {
        Enterprise one = this.getById(id);
        if (null == one) {
            throw new EpsException("企业申报信息不存在");
        }
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        Long regionId = one.getRegionTownId();
        if (null == currentUserRegionIds || !currentUserRegionIds.contains(regionId)) {
            throw new EpsException("当前用户所在组织无权编辑该企业");
        }
        LambdaUpdateWrapper<Enterprise> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(Enterprise::getId, id);
        if (Objects.nonNull(enterprise.getName())) {
            updateWrapper.set(Enterprise::getName, enterprise.getName());
        }
        updateWrapper.set(Enterprise::getBusinessAddress, enterprise.getBusinessAddress());
        updateWrapper.set(Enterprise::getLng, enterprise.getLng());
        updateWrapper.set(Enterprise::getLat, enterprise.getLat());
        this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEnterprise(Enterprise enterprise) {
        LambdaQueryWrapper<Enterprise> wapper = new LambdaQueryWrapper<>();
        // TODO 设置删除条件
        this.remove(wapper);
    }

    private void queryDetail(Enterprise enterprise) {
        if (Objects.isNull(enterprise.getIndustry())) {
            enterprise.setIndustry(industryMapper.selectById(enterprise.getIndustryId()));
        }
        if (Objects.isNull(enterprise.getRegionCounty())) {
            enterprise.setRegionCounty(regionMapper.selectById(enterprise.getRegionCountyId()));
        }
        if (Objects.isNull(enterprise.getRegionTown())) {
            enterprise.setRegionTown(regionMapper.selectById(enterprise.getRegionTownId()));
        }
    }

    @Override
    public int importEnterprises(MultipartFile file) {
        if (file.isEmpty()) {
            throw new EpsException("导入数据为空");
        }
        String filename = file.getOriginalFilename();
        if (!StringUtils.endsWith(filename, ".xlsx")) {
            throw new EpsException(String.format("只支持%s类型文件导入", ".xlsx"));
        }
        List<Long> currentUserRegionIds = regionService.getCurrentUserRegionIds();
        final List<Enterprise> enterprises = Lists.newArrayList();
        List<StringBuilder> errorMsgs = new ArrayList<>();
        final Map<String, String> socialCodeValidate = new HashMap<>();
//        Long creatorId = EpsUtil.getCurrentUser().getUserId();
        try {
            ExcelKit.$Import(EnterpriseImport.class).readXlsx(file.getInputStream(),
                    new ExcelReadHandler<EnterpriseImport>() {
                        @Override
                        public void onSuccess(int sheet, int row, EnterpriseImport enterpriseImport) {
                            if (sheet == 0) {
                                Enterprise enterprise = (Enterprise) DataTransfer.transfer(
                                        enterpriseImport, Enterprise.class);
                                List<EpsError> errors = new ArrayList<>();
                                try {
                                    BasicRisk newBasicRisk = new BasicRisk();
                                    String socialCode = enterpriseImport.getSocialCode();
                                    //校验社信代码
                                    validateSocialCodeByImport(socialCode, socialCodeValidate, errors);
                                    //校验行业
                                    enterprise.setIndustryId(validateIndustry(enterpriseImport.getIndustry(), errors));
                                    //校验单位性质
                                    enterprise.setNature(validateNature(enterpriseImport.getNature(), errors));
                                    //校验区县镇街
                                    Region regionCounty = validateRegion(enterpriseImport.getRegionCounty(),
                                            RegionConstant.REGION_LEVEL_AREA, getValue("regionCounty"), errors);
                                    Region regionTown = validateRegion(enterpriseImport.getRegionTown(),
                                            RegionConstant.REGION_LEVEL_TOWN, getValue("regionTown"), errors);
                                    validateRegion(currentUserRegionIds, regionCounty, regionTown, errors);
                                    enterprise.setRegionCountyId(regionCounty.getId());
                                    enterprise.setRegionTownId(regionTown.getId());
                                    //校验从业人数
                                    enterprise.setPeopleNumber(validatePeopleNumber(
                                            enterpriseImport.getPeopleNumber(), errors));
                                    //校验联系电话
                                    enterprise.setLegalRepresentativePhone(
                                            validatePhone(enterpriseImport.getLegalRepresentativePhone(),
                                                    getValue("legalRepresentativePhone"), errors));
                                    enterprise.setSafetyDirectorPhone(
                                            validatePhone(enterpriseImport.getSafetyDirectorPhone(),
                                                    getValue("safetyDirectorPhone"), errors));
                                    //校验生产经营状态
                                    enterprise.setProductionStatus(validateProductionStatus(
                                            enterpriseImport.getProductionStatus(), errors));
//                                    //校验安全三同时或现状评价
//                                    enterprise.setCurrentEvaluation(
//                                            validateCurrentEvaluation(enterpriseImport.getIsCurrentEvaluation(),
//                                                    enterpriseImport.getCurrentEvaluation(), errors));
                                    //校验安全生产标准化
                                    String isSafety = enterpriseImport.getIsSafety();
                                    String safetyBckLevel = enterpriseImport.getSafetyBckLevel();
                                    String safetyBckTime = enterpriseImport.getSafetyBckTime();
                                    String safetyMajorTime = enterpriseImport.getSafetyMajorTime();
                                    String safetyPostTime = enterpriseImport.getSafetyPostTime();
                                    validateIsSafety(isSafety, safetyBckLevel, safetyBckTime,
                                            safetyMajorTime, safetyPostTime, errors);
                                    enterprise.setSafetyBckLevel(getData(safetyBckLevel));
                                    enterprise.setSafetyBckTime(getData(safetyBckTime));
                                    if (ObjectUtils.isEmpty(enterpriseImport.getCertificateCode())){
                                        enterprise.setCertificateCode(enterpriseImport.getCertificateCode());
                                    }
                                    enterprise.setSafetyMajorTime(getData(safetyMajorTime));
                                    enterprise.setSafetyPostTime(getData(safetyPostTime));
                                    //校验应急预案
                                    String isEmergency = enterpriseImport.getIsEmergency();
                                    String emergencyPlanReview = enterpriseImport.getEmergencyPlanReview();
                                    String emergencyPlanRecord = enterpriseImport.getEmergencyPlanRecord();
                                    validateIsEmergency(isEmergency, emergencyPlanReview, emergencyPlanRecord, errors);
                                    enterprise.setEmergencyPlanReview(EMERGENCYPLAN_REVIEW.get(emergencyPlanReview));
                                    enterprise.setEmergencyPlanRecord(EMERGENCYPLAN_RECORD.get(emergencyPlanRecord));
                                    String isIndustrial = enterpriseImport.getIsIndustrial();
                                    String isLiability = enterpriseImport.getIsLiability();
                                    validateSafetyInsurance(isIndustrial, isLiability, errors);
                                    enterprise.setSafetyInsurance(getSafetyInsurance(isIndustrial, isLiability));
                                    enterprise.setCreateTime(new Date());
                                    enterprise.setModifyTime(new Date());
                                    enterprise.setTradeType(getTradeTypeImp(enterpriseImport.getTradeType()));
                                    enterprise.setOrderAndCard(getOrderAndCardImp(enterpriseImport.getOrderAndCard(),
                                            errors));
                                    enterprise.setScaleStatus(getScaleStatusImp(enterpriseImport.getScaleStatus(),
                                            errors));
                                    enterprise.setEnterpriseIntroduce(enterpriseImport.getEnterpriseIntroduce());
                                    enterpriseDeclarationService.updateBasicRisk(basicRiskMapper, enterprise,
                                            newBasicRisk);
                                    enterprise.setPlanStatus(EnterpriseConstant.PLAN_STATUS);
                                    enterprise.setHiddenStatus(EnterpriseConstant.HIDDEN_STATUS);
                                    validateLoginPhone(enterpriseImport.getLoginPhone(),
                                            errors);
//                                    enterprises.add(enterprise);
                                } catch (Exception e) {
                                    errors.add(new EpsError("未知列", e.getMessage()));
                                }
                                if (errors.size() > 0) {
                                    StringBuilder errorMsg = new StringBuilder();
                                    errorMsg.append("【第" + row + "行】:");
                                    for (EpsError error : errors) {
                                        errorMsg.append(error.toString());
                                        errorMsg.append(";");
                                    }
                                    errorMsgs.add(errorMsg);
                                }else {
                                    EnterpriseUser enterpriseUser=new EnterpriseUser();
                                    PasswordEncoder encoder = new BCryptPasswordEncoder();
                                    enterpriseUser.setCreateTime(new Date());
                                    enterpriseUser.setUpdateTime(new Date());
                                    enterpriseUser.setMobile(enterpriseImport.getLoginPhone());
                                    enterpriseUser.setEnterpriseName(enterpriseImport.getName());
                                    enterpriseUser.setPassword(encoder.encode(paaword));
                                    enterpriseUserMapper.insert(enterpriseUser);
                                    enterprise.setCreatorId(enterpriseUser.getUserId());
                                    enterprise.setModifyUserId(enterpriseUser.getUserId());
                                    enterprises.add(enterprise);
                                }
                            }
                        }

                        @Override
                        public void onError(int sheet, int row, List<ExcelErrorField> errorFields) {
                            if (sheet == 0) {
                                StringBuilder errorMsg = new StringBuilder();
                                errorMsg.append("【第" + row + "行】:");
                                for (ExcelErrorField error : errorFields) {
                                    errorMsg.append(error.getErrorMessage());
                                    errorMsg.append(";");
                                }
                                errorMsgs.add(errorMsg);
                            }
                        }
                    });
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (errorMsgs.size() > 0) {
            throw new EpsException(Joiner.on("#").join(errorMsgs));
        }

        this.saveBatch(enterprises);
        return enterprises.size();
    }

    private void validateLoginPhone(String loginPhone,List<EpsError> errors) {
        try {
            if (ObjectUtils.isEmpty(loginPhone)){
                throw new EpsException("登录手机号为空！");
            }else {
                validatePhone(loginPhone,"loginPhone",errors);
                EnterpriseUser enterpriseUser1 = enterpriseUserMapper.selectOne(new QueryWrapper<EnterpriseUser>()
                    .eq("mobile", loginPhone));
                if (!ObjectUtils.isEmpty(enterpriseUser1)){
                    throw new EpsException("登录手机号重复!");
                }
            }
        } catch (EpsException e) {
            errors.add(new EpsError("loginPhone", e.getMessage()));
        }

    }

    private String getTradeTypeImp(String tradeType) {
        List<Integer> list=new ArrayList<>();
        // 1-批发  2-仓储   3-零售   4-住宿   5-餐饮
        String[] split = tradeType.split(",");
        for (String s : split) {
            if ("批发".equals(s)){
                list.add(EnterpriseConstant.WHOSLESALE);
            }
            if ("仓储".equals(s)){
                list.add(EnterpriseConstant.KEEP_GRAIN);
            }
            if ("零售".equals(s)){
                list.add(EnterpriseConstant.RETAIL);
            }
            if ("住宿".equals(s)){
                list.add(EnterpriseConstant.STAY);
            }
            if ("餐饮".equals(s)){
                list.add(EnterpriseConstant.RESTAURANT);
            }
        }
        return list.toString();
    }

    private Integer getOrderAndCardImp(String orderAndCard,List<EpsError> errors) {
        if (ObjectUtils.isEmpty(orderAndCard)){
            errors.add(new EpsError(getValue("orderAndCard"), "两单两卡创建情况"));
        }else {
            //两单两卡创建情况：0-未创建；1-创建中；2-已创建
            if ("未创建".equals(orderAndCard)){
                return 0;
            }
            if ("创建中".equals(orderAndCard)){
                return 1;
            }
            if ("已创建".equals(orderAndCard)){
                return 2;
            }
        }
        return null;
    }

    private Integer getScaleStatusImp(String scaleStatus,List<EpsError> errors) {
        //规上规下企业：0-规下；1-规上
        if (ObjectUtils.isEmpty(scaleStatus)){
            errors.add(new EpsError(getValue("scaleStatus"), "规上规下未创建"));
        }else {
            if ("规下".equals(scaleStatus)){
                return 0;
            }
            if ("规上".equals(scaleStatus)){
                return 1;
            }
        }
        return null;
    }

    @Override
    public List<String[]> conver(List<Enterprise> records) {
        List<String[]> data = new ArrayList<>();
//        List<EnterpriseData> data = new ArrayList<>();
        for (Enterprise record : records) {
            EnterpriseData enterpriseData = new EnterpriseData();
            if (!ObjectUtils.isEmpty(record.getName())) {
                enterpriseData.setName(record.getName());
            }else {
                enterpriseData.setName(record.getName());
            }
            if (!ObjectUtils.isEmpty(record.getSocialCode())) {
                enterpriseData.setSocialCode(record.getSocialCode());
            }else {
                enterpriseData.setSocialCode(" ");
            }
            if (!ObjectUtils.isEmpty(record.getIndustryId())) {
                enterpriseData.setIndustry(industryMapper.selectById(record.getIndustryId()).getName());
            }else {
                enterpriseData.setIndustry(" ");
            }
            if (!ObjectUtils.isEmpty(record.getNature())) {
                if (record.getNature().equals(1)) {
                    enterpriseData.setNature("企业");
                } else {
                    enterpriseData.setNature("个体工商户");
                }
            }
            if (!ObjectUtils.isEmpty(record.getRegionCountyId())) {
                enterpriseData.setRegionCounty(regionMapper.selectById(record.getRegionCountyId()).getName());
            }else {
                enterpriseData.setRegionCounty(" ");
            }
            if (!ObjectUtils.isEmpty(record.getRegionTownId())) {
                enterpriseData.setRegionTown(regionMapper.selectById(record.getRegionTownId()).getName());
            }else {
                enterpriseData.setRegionTown(" ");
            }
            if (!ObjectUtils.isEmpty(record.getPeopleNumber())) {
                enterpriseData.setPeopleNumber(record.getPeopleNumber());
            }else {
                enterpriseData.setPeopleNumber(" ");
            }
            if (!ObjectUtils.isEmpty(record.getLegalRepresentativeName())) {
                enterpriseData.setLegalRepresentativeName(record.getLegalRepresentativeName());
            }else {
                enterpriseData.setLegalRepresentativeName(" ");
            }
            if (!ObjectUtils.isEmpty(record.getLegalRepresentativePhone())) {
                enterpriseData.setLegalRepresentativePhone(record.getLegalRepresentativePhone());
            }else {
                enterpriseData.setName(record.getName());
            }
            if (!ObjectUtils.isEmpty(record.getSafetyDirectorName())) {
                enterpriseData.setSafetyDirectorName(record.getSafetyDirectorName());
            }else {
                enterpriseData.setSafetyDirectorName(" ");
            }
            if (!ObjectUtils.isEmpty(record.getSafetyDirectorPhone())) {
                enterpriseData.setSafetyDirectorPhone(record.getSafetyDirectorPhone());
            }else {
                enterpriseData.setSafetyDirectorPhone(" ");
            }
            if (!ObjectUtils.isEmpty(record.getSafetyFullNumber())) {
                enterpriseData.setSafetyFullNumber(record.getSafetyFullNumber());
            }else {
                enterpriseData.setSafetyFullNumber(0);
            }
            if (!ObjectUtils.isEmpty(record.getSafetyPartNumber())) {
                enterpriseData.setSafetyPartNumber(record.getSafetyPartNumber());
            }else {
                enterpriseData.setSafetyPartNumber(0);
            }
            if (!ObjectUtils.isEmpty(record.getAddress())) {
                enterpriseData.setAddress(record.getAddress());
            }else {
                enterpriseData.setAddress(" ");
            }
            if (!ObjectUtils.isEmpty(record.getProductionStatus())) {
                if (record.getProductionStatus().equals("1")) {
                    enterpriseData.setProductionStatus("正常营业");
                } else if (record.getProductionStatus().equals("2")) {
                    enterpriseData.setProductionStatus("放假");
                } else if (record.getProductionStatus().equals("3")) {
                    enterpriseData.setProductionStatus("停产整改");
                } else if (record.getProductionStatus().equals("4")) {
                    enterpriseData.setProductionStatus("注销");
                } else {
                    enterpriseData.setProductionStatus("搬迁");
                }

            }
            if (!ObjectUtils.isEmpty(record.getCertificateCode())) {
                enterpriseData.setCertificateCode(record.getCertificateCode());
            }
            if (!ObjectUtils.isEmpty(record.getSafetyBckLevel())) {
                enterpriseData.setSafetyBck("有");
                if (!ObjectUtils.isEmpty(record.getSafetyBckLevel())) {
                    enterpriseData.setSafetyBckLevel(record.getSafetyBckLevel());
                }else {
                    enterpriseData.setSafetyBckLevel(" ");
                }
                if (!ObjectUtils.isEmpty(record.getSafetyBckTime())) {
                    enterpriseData.setSafetyBckTime(record.getSafetyBckTime());
                }else {
                    enterpriseData.setSafetyBckTime(" ");
                }
                if (!ObjectUtils.isEmpty(record.getSafetyMajorTime())) {
                    enterpriseData.setSafetyMajorTime(record.getSafetyMajorTime());
                }else {
                    enterpriseData.setSafetyMajorTime(" ");
                }
                if (!ObjectUtils.isEmpty(record.getSafetyPostTime())) {
                    enterpriseData.setSafetyPostTime(record.getSafetyPostTime());
                }else {
                    enterpriseData.setSafetyPostTime(" ");
                }
            } else {
                enterpriseData.setSafetyBck("无");
            }
            if (!ObjectUtils.isEmpty(record.getEmergencyPlanRecord())) {
                enterpriseData.setEmergencyPlan("有");
                if (!ObjectUtils.isEmpty(record.getEmergencyPlanReview())) {
                    if ("1".equals(record.getEmergencyPlanReview())) {
                        enterpriseData.setEmergencyPlanReview("已评审");
                    }
                } else {
                    enterpriseData.setEmergencyPlanReview("未评审");
                }
                if (!ObjectUtils.isEmpty(record.getEmergencyPlanRecord())) {
                    if("1".equals(record.getEmergencyPlanRecord())){
                        enterpriseData.setEmergencyPlanRecord("已备案");
                    }else {
                        enterpriseData.setEmergencyPlanRecord("未备案");
                    }
                }
            } else {
                enterpriseData.setEmergencyPlan("无");
            }
            if (!ObjectUtils.isEmpty(record.getScaleStatus())) {
                if (record.getScaleStatus().equals(0)) {
                    enterpriseData.setScaleStatus("规下");
                } else {
                    enterpriseData.setScaleStatus("规上");
                }
            }
            if (!ObjectUtils.isEmpty(record.getSafetyInsurance())) {
                if (record.getSafetyInsurance().equals("[1]")) {
                    enterpriseData.setSafetyInsurance("工商保险");
                } else if ((record.getSafetyInsurance().equals("[1,2]"))) {
                    enterpriseData.setSafetyInsurance("工商保险及安全责任保险");
                } else if ((record.getSafetyInsurance().equals("[2]"))) {
                    enterpriseData.setSafetyInsurance("安全责任保险");
                }else {
                    enterpriseData.setSafetyInsurance(" ");
                }
            }
            String[] objects={enterpriseData.getName(),
                    enterpriseData.getSocialCode(),
                    enterpriseData.getIndustry(),
                    enterpriseData.getNature(),
                    enterpriseData.getRegionCounty(),
                    enterpriseData.getRegionTown(),
                    enterpriseData.getPeopleNumber(),
                    enterpriseData.getLegalRepresentativeName(),
                    enterpriseData.getLegalRepresentativePhone(),
                    enterpriseData.getSafetyDirectorName(),
                    enterpriseData.getSafetyDirectorPhone(),
                    enterpriseData.getSafetyFullNumber().toString(),
                    enterpriseData.getSafetyPartNumber().toString(),
                    enterpriseData.getAddress(),
                    enterpriseData.getProductionStatus(),
                    enterpriseData.getSafetyBck(),
                    enterpriseData.getSafetyBckLevel(),
                    enterpriseData.getSafetyBckTime(),
                    enterpriseData.getCertificateCode(),
                    enterpriseData.getSafetyMajorTime(),
                    enterpriseData.getSafetyPostTime(),
                    enterpriseData.getEmergencyPlan(),
                    enterpriseData.getEmergencyPlanReview(),
                    enterpriseData.getEmergencyPlanRecord(),
                    enterpriseData.getScaleStatus(),
                    enterpriseData.getSafetyInsurance()};
            data.add(objects);
        }
        return data;
    }

    @Override
    public IPage<Enterprise> hiddenList(EnterpriseVo enterprise) {
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        List<Long> selectSon=new ArrayList<>();
        Region region = regionMapper.selectById(currentUser.getRegionId());
        if (region.getLevel().equals(REGION_TWO_LIVE)) {
        // 获取子id
            selectSon = regionMapper.selectSon(currentUser.getRegionId());
        }else if (region.getLevel().equals(REGION_THREE_LIVE)){
            selectSon.add(region.getId());
        }
        Page<Enterprise> page = new Page<>(enterprise.getPageNum(), enterprise.getPageSize());
        List<Enterprise> list=enterpriseMapper.getHiddenList(enterprise.getName(),
                enterprise.getRiskTypeSearch(),
                enterprise.getNoCommit(),
                enterprise.getIndustryId(),
                enterprise.getPageSize(),
                (enterprise.getPageNum()-1)*enterprise.getPageSize(),
                selectSon,currentUser.getUserId());
        page.setRecords(list);
        page.setTotal(enterpriseMapper.getHiddenList(enterprise.getName(),
                enterprise.getRiskTypeSearch(),
                enterprise.getNoCommit(),
                enterprise.getIndustryId(),
                null,
                null,
                selectSon,currentUser.getUserId()).size());
        return page;
    }

    @Override
    public List<ReportVo> getReport() {
        return enterpriseMapper.getReport();
    }

    /**
     * 删除原有风险，保存新设置风险
     */
    private List<EnterpriseRisk> saveNewEnterpriseRisks(String risks, Long enterpriseId) {
        deleteRisks(enterpriseId);
        List<EnterpriseRisk> enterpriseRisks = saveRisks(risks, enterpriseId);
        return enterpriseRisks;
    }

    private List<EnterpriseRisk> saveRisks(String risks, Long enterpriseId) {
        JSONObject jsonObject = JSONObject.parseObject(risks);
        List<EnterpriseRisk> enterpriseRisks = EnterpriseUtil.generateEnterpriseRisk(enterpriseId, jsonObject);
        if (!enterpriseRisks.isEmpty()) {
            enterpriseRiskServiceImpl.saveBatch(enterpriseRisks);
        }
        return enterpriseRisks;
    }

    private void deleteRisks(Long enterpriseId) {
        EnterpriseRisk enterpriseRiskWrapper = new EnterpriseRisk();
        enterpriseRiskWrapper.setEnterpriseId(enterpriseId);
        enterpriseRiskServiceImpl.remove(new LambdaQueryWrapper<>(enterpriseRiskWrapper));
    }

    /**
     * 判断社信代码重复
     *
     * @param socialCode
     */
    private void validateSocialCodeByDeclaration(String socialCode,Long creatId) {
        LambdaQueryWrapper<EnterpriseDeclaration> declarationQueryWrapper = new LambdaQueryWrapper<>();
        declarationQueryWrapper.eq(EnterpriseDeclaration::getSocialCode, socialCode);
        if (!ObjectUtils.isEmpty(creatId)) {
            declarationQueryWrapper.ne(EnterpriseDeclaration::getCreatorId, creatId);
        }
        declarationQueryWrapper.and(wrapper -> wrapper.eq(EnterpriseDeclaration::getStatus, ENTERPRISE_DECLARATION_STATUS_WAIT_REVIEW).or().eq(EnterpriseDeclaration::getStatus, ENTERPRISE_DECLARATION_STATUS_FIRST_REVIEW_ADOPT));
        List<EnterpriseDeclaration> existEnterpriseDeclarations = enterpriseDeclarationMapper.selectList(declarationQueryWrapper);
        if (null != existEnterpriseDeclarations && !existEnterpriseDeclarations.isEmpty()) {
            throw new EpsException("该企业社信代码已被其它用户申报");
        }
    }

    private EnterpriseDeclaration currentUserEnterpriseDeclaration(Long userId) throws EpsException {
        LambdaQueryWrapper<EnterpriseDeclaration> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterpriseDeclaration::getCreatorId, userId);
        queryWrapper.orderByDesc(EnterpriseDeclaration::getCreateTime);
        queryWrapper.last("limit 1");
        EnterpriseDeclaration enterpriseDeclaration = enterpriseDeclarationMapper.selectOne(queryWrapper);
        return enterpriseDeclaration;
    }

    private String getSafetyInsurance(String isIndustrial, String isLiability) {
        List<Integer> insurances = new ArrayList<>();
        if (EpsConstant.EXCEL_VALUE_HAVE.equals(isIndustrial)) {
            insurances.add(SAFETY_INSURANCE);
        }
        if (EpsConstant.EXCEL_VALUE_HAVE.equals(isLiability)) {
            insurances.add(SAFETY_INSURANCE_LIABILITY);
        }
        return JSONArray.toJSONString(insurances);
    }

    private void validateSafetyInsurance(String isIndustrial, String isLiability, List<EpsError> errors) {
        if (!isIndustrial.equals(EpsConstant.EXCEL_VALUE_HAVE) && !isLiability.equals(EpsConstant.EXCEL_VALUE_HAVE)) {
            errors.add(new EpsError(getValue("isIndustrial"), "保险不能全为空"));
        }
    }

    private void validateIsEmergency(String isEmergency, String emergencyPlanReview, String emergencyPlanRecord,
                                     List<EpsError> errors) {
        if (isEmergency.equals(EpsConstant.EXCEL_VALUE_YES)) {
            if (isNull(emergencyPlanReview) && isNull(emergencyPlanRecord)) {
                errors.add(new EpsError(getValue("isEmergency"), "应急预案不能全为空"));
            }
            if (nonNull(emergencyPlanReview) && !EMERGENCYPLAN_REVIEW.containsKey(emergencyPlanReview)) {
                errors.add(new EpsError(getValue("emergencyPlanReview"), "是否评审有误有误"));
            } else if (nonNull(emergencyPlanRecord) &&
                    !EMERGENCYPLAN_RECORD.containsKey(emergencyPlanRecord)) {
                errors.add(new EpsError(getValue("emergencyPlanRecord"), "是否备案有误"));
            }
        }
    }

    private void validateIsSafety(String isSafety, String safetyBckLevel, String safetyBckTime,
                                  String safetyMajorTime, String safetyPostTime, List<EpsError> errors) {
        if (isSafety.equals(EpsConstant.EXCEL_VALUE_YES)) {
            if (isNull(safetyBckLevel) && isNull(safetyBckTime)
                    && isNull(safetyMajorTime) && isNull(safetyPostTime)) {
                errors.add(new EpsError(getValue("isSafety"), "安全生产标准化不能全为空"));
            }
            if (nonNull(safetyBckTime) && !isLegalDate(safetyBckTime)) {
                errors.add(new EpsError(getValue("safetyBckTime"), "时间有误"));
            } else if (nonNull(safetyMajorTime) && !isLegalDate(safetyBckTime)) {
                errors.add(new EpsError(getValue("safetyMajorTime"), "时间有误"));
            } else if (nonNull(safetyPostTime) && !isLegalDate(safetyPostTime)) {
                errors.add(new EpsError(getValue("safetyPostTime"), "时间有误"));
            }
        }
    }

    private String validateCurrentEvaluation(String isCurrentEvaluation, String currentEvaluation,
                                             List<EpsError> errors) {
        if (isCurrentEvaluation.equals(EpsConstant.EXCEL_VALUE_HAVE)) {
            if (!isLegalDate(currentEvaluation)) {
                errors.add(new EpsError(getValue("isCurrentEvaluation"), "时间有误"));
            }
            return currentEvaluation;
        }
        return "";
    }

    private boolean isLegalDate(String sDate) {
        if (nonNull(sDate)) {
            DateFormat format = new SimpleDateFormat(EpsConstant.DATE_PATTERN);
            try {
                Date date = format.parse(sDate);
                return sDate.equals(format.format(date));
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    private String validateProductionStatus(String productionStatus, List<EpsError> errors) {
        String production = "";
        if (PRODUCTION_STATUS.containsKey(productionStatus)) {
            production = PRODUCTION_STATUS.get(productionStatus);
        } else {
            errors.add(new EpsError(getValue("productionStatus"), "生产经营状态有误"));
        }
        return production;
    }

    private String validatePhone(String phoneNum, String column, List<EpsError> errors) {
        String regex = "^(1[3-9]\\d{9}$)";
        if (phoneNum.length() == 11) {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(phoneNum);
            if (m.matches()) {
                return phoneNum;
            }
        }
        errors.add(new EpsError(column, "电话格式不正确"));
        return "";
    }

    public static void main(String[] args) {
        try {
            String s = EnterpriseImport.class.getDeclaredField("legalRepresentativePhone").getAnnotation(ExcelField.class).value();
            System.out.print(s);
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
    }

    private String validatePeopleNumber(String peopleNumber, List<EpsError> errors) {
        String number = "";
        if (PEOPLE_NUMBERS.containsKey(peopleNumber)) {
            number = PEOPLE_NUMBERS.get(peopleNumber);
        } else {
            errors.add(new EpsError(getValue("peopleNumber"), "从业人数有误"));
        }
        return number;
    }

    private void validateRegion(List<Long> currentUserRegionIds, Long regionCountyId, Long regionTownId) {
        if (Objects.isNull(regionCountyId)) {
            throw new EpsException("该企业所属区县不能为空");
        }
//        if (!currentUserRegionIds.contains(regionCountyId)
//                || (Objects.nonNull(regionTownId) && !currentUserRegionIds.contains(regionTownId))) {
//            throw new EpsException("用户没有权限操作导入其它地区企业");
//        }
    }

    private void validateRegion(List<Long> currentUserRegionIds, Region regionCounty, Region regionTown,
                                List<EpsError> errors) {
        String column = getValue("regionCounty");
        if (Objects.isNull(regionCounty)) {
            errors.add(new EpsError(column, "区县不能为空"));
        }

        if (Objects.nonNull(regionTown) && regionTown.getParentId() != regionCounty.getId()) {
            errors.add(new EpsError(getValue("regionTown"), "镇街不属于所属区县"));
        }
//        if (!currentUserRegionIds.contains(regionCounty.getId())
//                || (Objects.nonNull(regionTown) && !currentUserRegionIds.contains(regionTown.getId()))) {
//            errors.add(new EpsError(column, "用户没有权限操作导入其它地区企业"));
//        }
    }

    private Region validateRegion(String regionName, int regionLevel, String column, List<EpsError> errors) {
        Region region = null;
        if (nonNull(regionName)) {
            LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Region::getName, regionName);
            queryWrapper.last("limit 1");
            region = regionMapper.selectOne(queryWrapper);
            if (Objects.isNull(region)) {
                errors.add(new EpsError(column, "地区不存在"));
            } else if (region.getLevel() != regionLevel) {
                errors.add(new EpsError(column, "地区错误"));
            }
        }
        return region;
    }

    private String validateNature(String nature, List<EpsError> errors) {
        String natureNum = "";
        if (NATURES.containsKey(nature)) {
            natureNum = NATURES.get(nature);
        } else {
            errors.add(new EpsError(getValue("nature"), "单位性质有误"));
        }
        return natureNum;
    }

    private Long validateIndustry(String industryName, List<EpsError> errors) {
        LambdaQueryWrapper<Industry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Industry::getName, industryName);
        queryWrapper.last("limit 1");
        Industry industry = industryMapper.selectOne(queryWrapper);
        if (Objects.isNull(industry)) {
            errors.add(new EpsError(getValue("industry"), "行业不存在"));
        }
        return industry.getId();
    }

    private void validateSocialCodeByImport(String socialCode, Map<String, String> socialCodeValidate,
                                            List<EpsError> errors) {
        String errorValue = getValue("socialCode");
        if (socialCodeValidate.containsKey(socialCode)) {
            errors.add(new EpsError(errorValue, "excel中数据重复"));
        }
        socialCodeValidate.put(socialCode, socialCode);
        try {
            validateSocialCodeByDeclaration(socialCode,null);
        } catch (EpsException e) {
            errors.add(new EpsError(errorValue, e.getMessage()));
        }
        try {
            validateSocialCodeByEnterprise(socialCode, null);
        } catch (EpsException e) {
            errors.add(new EpsError(errorValue, e.getMessage()));
        }
    }

    private void validateSocialCode(String socialCode, Long id, Long creatId) {
        validateSocialCodeByDeclaration(socialCode,creatId);
        validateSocialCodeByEnterprise(socialCode, id);
    }

    private void validateSocialCodeByEnterprise(String socialCode, Long id) {
        LambdaQueryWrapper<Enterprise> enterpriseQueryWrapper = new LambdaQueryWrapper<>();
        enterpriseQueryWrapper.eq(Enterprise::getSocialCode, socialCode);
        if (Objects.nonNull(id)) {
            enterpriseQueryWrapper.ne(Enterprise::getId, id);
        }
        enterpriseQueryWrapper.last("limit 1");
        Enterprise enterprise = enterpriseMapper.selectOne(enterpriseQueryWrapper);
        if (Objects.nonNull(enterprise)) {
            throw new EpsException("该企业社信代码已被占用");
        }
    }

    private boolean nonNull(String str) {
        return Objects.nonNull(str) && !str.equals(EpsConstant.EXCEL_EMPTY);
    }

    private boolean isNull(String str) {
        return Objects.isNull(str) || str.equals(EpsConstant.EXCEL_EMPTY);
    }

    private String getData(String str) {
        if (isNull(str)) {
            return "";
        }
        return str;
    }

    private String getValue(String name) {
        try {
            return EnterpriseImport.class.getDeclaredField(name).getAnnotation(ExcelField.class).value();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
        return "";
    }

    private String getErrorMsg(String name) {
        try {
            return EnterpriseImport.class.getDeclaredField(name).getAnnotation(ExcelField.class).value();
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
        return "";
    }
}
