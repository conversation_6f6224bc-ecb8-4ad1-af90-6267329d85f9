package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Industry;
import com.example.eps.ins.mapper.IndustryMapper;
import com.example.eps.ins.service.IIndustryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 行业表 Service实现
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:40
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class IndustryServiceImpl extends ServiceImpl<IndustryMapper, Industry> implements IIndustryService {

    private final IndustryMapper industryMapper;

    @Override
    public IPage<Industry> findIndustrys(QueryRequest request, Industry industry) {
        LambdaQueryWrapper<Industry> queryWrapper = new LambdaQueryWrapper<>();
        // TODO 设置查询条件
        Page<Industry> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    public List<Industry> findIndustries() {
        LambdaQueryWrapper<Industry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Industry::getId,Industry::getName);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createIndustry(Industry industry) {
        this.save(industry);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIndustry(Industry industry) {
        this.saveOrUpdate(industry);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteIndustry(Industry industry) {
        LambdaQueryWrapper<Industry> wapper = new LambdaQueryWrapper<>();
        // TODO 设置删除条件
        this.remove(wapper);
    }
}
