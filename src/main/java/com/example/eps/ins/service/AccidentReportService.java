package com.example.eps.ins.service;

import com.example.eps.ins.common.dto.report.req.AreaReq;
import com.example.eps.ins.common.dto.report.req.CompanyView;
import com.example.eps.ins.common.dto.report.resp.AccidentAllResp;
import com.example.eps.ins.common.dto.report.resp.AccidentSortResp;
import com.example.eps.ins.common.dto.report.resp.AreaResp;
import com.example.eps.ins.common.dto.report.resp.IndustryAccidentResp;

import java.util.List;

public interface AccidentReportService {
    AccidentAllResp getAll(Long nameId, Integer year);

    List<IndustryAccidentResp> getIndustry(CompanyView companyView);

    List<AccidentSortResp> getSort(CompanyView companyView);

    List<AreaResp> getArea(AreaReq companyView);
}
