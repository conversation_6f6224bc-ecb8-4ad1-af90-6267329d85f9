package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.dto.device.DeviceInfo;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.device.Device;
import com.example.eps.ins.mapper.DeviceMapper;
import com.example.eps.ins.service.IDeviceService;
import com.example.eps.ins.service.ISubDeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 设备信息 Service实现
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements IDeviceService {

    private final DeviceMapper deviceMapper;
    private final ISubDeviceService subDeviceService;

    @Override
    public IPage<Device> findDevices(QueryRequest request, Device device) {
        Page<Device> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.deviceMapper.selectDevicePage(page, device);
    }

    @Override
    public Device findByExternalDeviceId(Long externalDeviceId) {
        return this.deviceMapper.selectByExternalDeviceId(externalDeviceId);
    }

    @Override
    public Device findBySerial(String serial) {
        return this.deviceMapper.selectBySerial(serial);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncDevices(List<DeviceInfo> deviceInfoList) {
        return syncDevices(deviceInfoList, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncDevices(List<DeviceInfo> deviceInfoList, boolean syncSubDevices) {
        if (deviceInfoList == null || deviceInfoList.isEmpty()) {
            log.warn("设备信息列表为空，无需同步");
            return true;
        }

        try {
            List<Device> devicesToSave = new ArrayList<>();
            Date now = new Date();

            for (DeviceInfo deviceInfo : deviceInfoList) {
                Device existingDevice = findByExternalDeviceId(deviceInfo.getId());
                Device savedDevice = null;

                if (existingDevice != null) {
                    // 更新现有设备
                    updateDeviceFromInfo(existingDevice, deviceInfo);
                    existingDevice.setLastSyncTime(now);
                    existingDevice.setSyncStatus(1);
                    this.updateById(existingDevice);
                    savedDevice = existingDevice;
                    log.info("更新设备信息: {}", deviceInfo.getDeviceName());
                } else {
                    // 创建新设备
                    Device newDevice = createDeviceFromInfo(deviceInfo);
                    newDevice.setLastSyncTime(now);
                    newDevice.setSyncStatus(1);
                    devicesToSave.add(newDevice);
                    log.info("新增设备信息: {}", deviceInfo.getDeviceName());
                }

                // 同步子设备信息
                if (syncSubDevices && deviceInfo.getSubDevices() != null && !deviceInfo.getSubDevices().isEmpty()) {
                    Long deviceId = savedDevice != null ? savedDevice.getId() : null;

                    // 如果是新设备，需要先保存获取ID
                    if (deviceId == null && !devicesToSave.isEmpty()) {
                        // 先保存当前新设备以获取ID
                        Device lastNewDevice = devicesToSave.get(devicesToSave.size() - 1);
                        this.save(lastNewDevice);
                        deviceId = lastNewDevice.getId();
                        devicesToSave.remove(devicesToSave.size() - 1); // 从待保存列表中移除
                    }

                    if (deviceId != null) {
                        try {
                            subDeviceService.syncSubDevices(deviceId, deviceInfo.getSubDevices());
                            log.info("同步设备 {} 的子设备成功，子设备数量: {}",
                                deviceInfo.getDeviceName(), deviceInfo.getSubDevices().size());
                        } catch (Exception subDeviceException) {
                            log.error("同步设备 {} 的子设备失败", deviceInfo.getDeviceName(), subDeviceException);
                            // 子设备同步失败不影响主设备同步
                        }
                    }
                }
            }

            // 批量保存剩余的新设备
            if (!devicesToSave.isEmpty()) {
                return this.saveBatch(devicesToSave);
            }

            return true;
        } catch (Exception e) {
            log.error("同步设备信息失败", e);
            throw new RuntimeException("同步设备信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Device saveOrUpdateDevice(DeviceInfo deviceInfo) {
        Device existingDevice = findByExternalDeviceId(deviceInfo.getId());
        Date now = new Date();

        if (existingDevice != null) {
            // 更新现有设备
            updateDeviceFromInfo(existingDevice, deviceInfo);
            existingDevice.setLastSyncTime(now);
            existingDevice.setSyncStatus(1);
            this.updateById(existingDevice);
            return existingDevice;
        } else {
            // 创建新设备
            Device newDevice = createDeviceFromInfo(deviceInfo);
            newDevice.setLastSyncTime(now);
            newDevice.setSyncStatus(1);
            this.save(newDevice);
            return newDevice;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveDevices(List<Device> deviceList) {
        if (deviceList == null || deviceList.isEmpty()) {
            return true;
        }
        return this.saveBatch(deviceList);
    }

    @Override
    public List<Device> findBySyncStatus(Integer syncStatus) {
        return this.deviceMapper.selectBySyncStatus(syncStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSyncStatus(Long deviceId, Integer syncStatus) {
        Device device = this.getById(deviceId);
        if (device != null) {
            device.setSyncStatus(syncStatus);
            device.setLastSyncTime(new Date());
            return this.updateById(device);
        }
        return false;
    }

    /**
     * 从DeviceInfo创建Device实体
     */
    private Device createDeviceFromInfo(DeviceInfo deviceInfo) {
        Device device = new Device();
        device.setExternalDeviceId(deviceInfo.getId());
        device.setDeviceName(deviceInfo.getDeviceName());
        device.setSerial(deviceInfo.getSerial());
        device.setStatus(deviceInfo.getStatus());
        device.setDeviceType(deviceInfo.getDeviceType());
        device.setIp(deviceInfo.getIp());
        device.setCreateTime(new Date());
        device.setUpdateTime(new Date());
        return device;
    }

    /**
     * 从DeviceInfo更新Device实体
     */
    private void updateDeviceFromInfo(Device device, DeviceInfo deviceInfo) {
        device.setDeviceName(deviceInfo.getDeviceName());
        device.setSerial(deviceInfo.getSerial());
        device.setStatus(deviceInfo.getStatus());
        device.setDeviceType(deviceInfo.getDeviceType());
        device.setIp(deviceInfo.getIp());
        device.setUpdateTime(new Date());
    }
}
