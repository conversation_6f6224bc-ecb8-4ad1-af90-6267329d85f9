package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.SystemUser;
import com.example.eps.ins.common.exception.EpsException;


import java.util.List;

/**
 * <AUTHOR>
 */
public interface IUserService extends IService<SystemUser> {

    /**
     * 通过用户名查找用户
     *
     * @param username 用户名
     * @return 用户
     */
    SystemUser findByName(String username);

    /**
     * 通过用户id查找用户
     *
     * @param userId 用户id
     * @return 用户
     */
    SystemUser findById(Long userId);

    /**
     * 通用查找
     *
     * @param user
     * @return
     */
    List<SystemUser> findList(SystemUser user);

    /**
     * 查找用户详细信息
     *
     * @param request request
     * @param user    用户对象，用于传递查询条件
     * @return IPage
     */
    IPage<SystemUser> findUserDetailList(SystemUser user, QueryRequest request);

    /**
     * 通过用户ID查找用户详细信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    SystemUser findUser(Long id);


    /**
     * 通过用户名查找用户详细信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    SystemUser findUserDetail(String username);

    /**
     * 更新用户登录时间
     *
     * @param username username
     */
    void updateLoginTime(String username);

    /**
     * 新增用户
     *
     * @param user user
     */
    void createUser(SystemUser user);

    /**
     * 修改用户
     *
     * @param user user
     */
    void updateUser(SystemUser user);

    /**
     * 删除用户
     *
     * @param userIds 用户 id数组
     */
    void deleteUsers(String[] userIds);

    /**
     * 更新个人信息
     *
     * @param user 个人信息
     * @throws EpsException 异常
     */
    void updateProfile(SystemUser user) throws EpsException;

    /**
     * 更新用户头像
     *
     * @param avatar 用户头像
     */
    void updateAvatar(String avatar);

    /**
     * 更新用户密码
     *
     * @param password 新密码
     */
    void updatePassword(String password);

    /**
     * 重置密码
     *
     * @param userIDs 用户集合
     */
    void resetPassword(Integer[] userIDs);

    /**
     * 更新用户侧边栏主题
     *
     * @param theme 侧栏主题
     */
    void updateSidebarTheme(String theme);

}