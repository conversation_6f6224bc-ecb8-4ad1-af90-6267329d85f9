package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.dto.enterprise.EmergencydrillVO;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Emergencydrill;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.EmergencyDrillMapper;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.IEmergencyDrillService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/** @Author: Zhanghongyin @Date: Created in 2022/10/11 9:52 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class EmergencyDrillServiceImpl extends ServiceImpl<EmergencyDrillMapper, Emergencydrill>
    implements IEmergencyDrillService {

  private final FdfsClientService fdfsClientService;
  private final EmergencyDrillMapper emergencyDrillMapper;
  private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

  @Override
  public IPage<Emergencydrill> findEmergencyDrills(
      QueryRequest request, Emergencydrill emergencydrill) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    if (!ObjectUtils.isEmpty(emergencydrill.getCreatorId())){
      userId=emergencydrill.getCreatorId();
    }
    LambdaQueryWrapper<Emergencydrill> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(Emergencydrill::getCreatorId, userId);
    if (!ObjectUtils.isEmpty(emergencydrill.getEmergencyDrillName())) {
      queryWrapper.like(
          Emergencydrill::getEmergencyDrillName, emergencydrill.getEmergencyDrillName());
    }
    queryWrapper.orderByDesc(Emergencydrill::getCreateTime);
    Page<Emergencydrill> page = new Page<>(request.getPageNum(), request.getPageSize());
    return this.page(page, queryWrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String createEmergencyDrill(EmergencydrillVO emergencydrillVo){
    //获取所有的企业申报信息
    List<EnterpriseDeclarationResponse> enterprise =
            enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
    //获取所有申报状态
    List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
    if (ObjectUtils.isEmpty(enterprise)){
      throw new EpsException("请完善企业信息后创建企业应急演练信息！");
    }
    if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
      throw new EpsException("企业信息暂未审核通过，请审核通过后创建企业应急演练信息！");
    }
    // 保存消息
    Emergencydrill emergencydrill = new Emergencydrill();
    emergencydrill.setEmergencyDrillName(emergencydrillVo.getEmergencyDrillName());
    if (ObjectUtils.isEmpty(emergencydrillVo.getEmergencyDrillFile())){
      emergencydrill.setEmergencyDrillFile("[]");
    } else {
      emergencydrill.setEmergencyDrillFile(emergencydrillVo.getEmergencyDrillFile());
    }
    if (ObjectUtils.isEmpty(emergencydrillVo.getEmergencyDrillImage())){
      emergencydrill.setEmergencyDrillImage("[]");
    } else {
      emergencydrill.setEmergencyDrillImage(emergencydrillVo.getEmergencyDrillImage());
    }
    emergencydrill.setEmergencyDrillDigest(emergencydrillVo.getEmergencyDrillDigest());
    emergencydrill.setEmergencyDrillTime(emergencydrillVo.getEmergencyDrillTime());
    emergencydrill.setCreateTime(new Date());
    if (ObjectUtils.isEmpty(emergencydrillVo.getId())) {
      CurrentUser currentUser = EpsUtil.getCurrentUser();
      emergencydrill.setRegionId(enterprise.get(0).getRegionTownId());
      emergencydrill.setCreatorId(currentUser.getUserId());
      this.save(emergencydrill);
    } else {
      emergencydrill.setCreatorId(emergencydrillVo.getCreatorId());
      this.update(
          emergencydrill, new QueryWrapper<Emergencydrill>().eq("id", emergencydrillVo.getId()));
    }
    return "";
  }

  @Override
  public Emergencydrill findEmergencyDrill(Long id) {
    return emergencyDrillMapper.selectById(id);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteEmergencyDrill(Long id) {
    LambdaQueryWrapper<Emergencydrill> userWarpper = new LambdaQueryWrapper<>();
    userWarpper.eq(Emergencydrill::getId, id);
    Emergencydrill emergencydrill = this.getById(id);
    if (emergencydrill == null) {
      return;
    }
    JSONArray jsonFileArray = JSON.parseArray(emergencydrill.getEmergencyDrillFile());
    JSONArray jsonImageArray = JSON.parseArray(emergencydrill.getEmergencyDrillImage());
    emergencyDrillMapper.delete(userWarpper);
    for (Object obj : jsonFileArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
    for (Object obj : jsonImageArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
  }

  @Override
  public void deleteFile(Long id, String filePath) {
    if (!ObjectUtils.isEmpty(id)) {
      LambdaQueryWrapper<Emergencydrill> userWarpper = new LambdaQueryWrapper<>();
      userWarpper.eq(Emergencydrill::getId, id);
      Emergencydrill emergencyPlan = emergencyDrillMapper.selectById(id);
      if (ObjectUtils.isEmpty(emergencyPlan)) {
        return;
      }
      JSONArray fileArray = JSON.parseArray(emergencyPlan.getEmergencyDrillFile());
      JSONArray imageArray = JSON.parseArray(emergencyPlan.getEmergencyDrillImage());
      JSONArray fileJson = getArray(filePath, fileArray);
      JSONArray imageJson = getArray(filePath, imageArray);
      emergencyPlan.setEmergencyDrillFile(fileJson.toString());
      emergencyPlan.setEmergencyDrillImage(imageJson.toString());
      emergencyDrillMapper.updateById(emergencyPlan);
    }
    fdfsClientService.deleteFile(filePath);
  }

  private JSONArray getArray(String filePath, JSONArray fileArray) {
    JSONArray json = new JSONArray();
    for (Object obj : fileArray) {
      String filePath1 = ((JSONObject) obj).getString("filePath");
      if (filePath1.equals(filePath)){
        continue;
      }
      Map<String, Object> map=new HashMap<>();
      map.put("fileUrl",((JSONObject) obj).getString("fileUrl"));
      map.put("filePath",((JSONObject) obj).getString("filePath"));
      map.put("fileName",((JSONObject) obj).getString("fileName"));
      json.add(EpsUtil.toJsonObj(map));
    }
    return json;
  }
}
