package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.dto.report.model.HiddenDangerVo;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.HiddenDanger;

import java.io.IOException;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 9:51
 * @Version: 1.0
 */

public interface HiddenDangerService extends IService<HiddenDanger> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param emergencyPlan enterpriseRisk
     * @return IPage<EmergencyPlan>
     */
    IPage<HiddenDanger> findHiddenDanger(QueryRequest request, HiddenDanger emergencyPlan);

    /**
     * 新增
     * @param emergencyPlan emergencyPlan
     * @throws IOException IOException
     */
    String createHiddenDanger(HiddenDangerVo emergencyPlan);

    /**
     * 详情
     * @param id  id
     * @return
     */
    HiddenDanger findHiddenDanger(Long id);


    /**
     * 删除
     *
     */
    void deleteHiddenDanger(Long id);

    /**
     * 删除
     *
     */
    void deleteFile(Long id, String filePath);
}
