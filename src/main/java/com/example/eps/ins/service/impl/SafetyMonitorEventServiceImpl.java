package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.dto.riskPush.AlarmQueryRequest;
import com.example.eps.ins.common.dto.riskPush.AlarmQueryResponse;
import com.example.eps.ins.common.dto.riskPush.CityEventObject;
import com.example.eps.ins.common.dto.riskPush.LocationInfo;
import com.example.eps.ins.common.entity.safety.SafetyMonitorEvent;
import com.example.eps.ins.common.utils.Base64ImageUploadUtil;
import com.example.eps.ins.mapper.SafetyMonitorEventMapper;
import com.example.eps.ins.service.SafetyMonitorEventService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 安全监控事件服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SafetyMonitorEventServiceImpl extends ServiceImpl<SafetyMonitorEventMapper, SafetyMonitorEvent>
        implements SafetyMonitorEventService {

    private final SafetyMonitorEventMapper safetyMonitorEventMapper;
    private final Base64ImageUploadUtil base64ImageUploadUtil;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SafetyMonitorEvent saveSafetyEvent(CityEventObject eventObject, Map<String, Object> deviceInfo, String rawJsonData) {
        try {
            // 检查事件是否已存在
            SafetyMonitorEvent existingEvent = safetyMonitorEventMapper.selectByExternalEventId(eventObject.getId());
            if (existingEvent != null) {
                log.info("事件已存在，跳过保存: {}", eventObject.getId());
                return existingEvent;
            }

            SafetyMonitorEvent event = new SafetyMonitorEvent();
            
            // 基础信息
            event.setExternalEventId(eventObject.getId());
            event.setEventType(eventObject.getEventType());
            event.setEventTypeName(getEventTypeName(eventObject.getEventType()));
            event.setEventSource(eventObject.getEventSource());
            event.setConfidence(eventObject.getConfidence());
            event.setHaveAlarm(eventObject.getHaveAlarm());
            
            // 设备信息
            if (deviceInfo != null) {
                event.setDeviceId(getLongValue(deviceInfo.get("deviceId")));
                event.setDeviceSerial((String) deviceInfo.get("serial"));
                event.setDeviceIp((String) deviceInfo.get("ip"));
                event.setDeviceType((String) deviceInfo.get("deviceType"));
                event.setDeviceStatus((String) deviceInfo.get("status"));
            }
            
            // 摄像头信息
            event.setCameraId(eventObject.getCameraId());
            event.setCameraName(eventObject.getCameraName());
            
            // 图片信息
            event.setImgUrl(eventObject.getImgUrl());

            // 处理 BigUrl - 如果是 base64 格式，上传到 FastDFS
            String processedBigUrl = base64ImageUploadUtil.uploadBase64Image(
                    eventObject.getBigUrl(),
                    "safety_event_" + eventObject.getId()
            );
            event.setBigUrl(processedBigUrl);
            
            // 时间信息
            event.setStartTime(parseTimeString(eventObject.getStartTime()));
            event.setEndTime(parseTimeString(eventObject.getEndTime()));
            event.setMarkTime(parseTimeString(eventObject.getMarkTime()));
            
            // 检测结果信息
            if (eventObject.getSubList() != null && eventObject.getSubList().getLocationInfo() != null 
                && !eventObject.getSubList().getLocationInfo().isEmpty()) {
                LocationInfo locationInfo = eventObject.getSubList().getLocationInfo().get(0);
                
                // 构建边界框JSON
                Map<String, Object> boundingBox = new HashMap<>();
                boundingBox.put("leftTopX", locationInfo.getLeftTopX());
                boundingBox.put("leftTopY", locationInfo.getLeftTopY());
                boundingBox.put("rightBtmX", locationInfo.getRightBtmX());
                boundingBox.put("rightBtmY", locationInfo.getRightBtmY());
                event.setBoundingBox(objectMapper.writeValueAsString(boundingBox));
            }
            
            // 图片尺寸信息
            if (eventObject.getSubList() != null && eventObject.getSubList().getImageInfo() != null) {
                event.setImgWidth(eventObject.getSubList().getImageInfo().getWidth());
                event.setImgHeight(eventObject.getSubList().getImageInfo().getHeight());
            }
            
            // 告警信息
            if (eventObject.getExtInfo() != null && eventObject.getExtInfo().getDataInfo() != null 
                && eventObject.getExtInfo().getDataInfo().getOptParam() != null) {
                event.setAlarmTimes(eventObject.getExtInfo().getDataInfo().getOptParam().getAlarmTimes());
                event.setObjectName(eventObject.getExtInfo().getDataInfo().getOptParam().getObjectName());
            }
            
            // 检测分数（从TrainPlatRaw中提取）
            if (eventObject.getSubList() != null && eventObject.getSubList().getTrainPlatRaw() != null 
                && !eventObject.getSubList().getTrainPlatRaw().isEmpty()) {
                try {
                    String val = eventObject.getSubList().getTrainPlatRaw().get(0).getVal();
                    if (val != null) {
                        Map<String, Object> valMap = objectMapper.readValue(val, Map.class);
                        List<Map<String, Object>> annotations = (List<Map<String, Object>>) valMap.get("annotations");
                        if (annotations != null && !annotations.isEmpty()) {
                            Object score = annotations.get(0).get("score");
                            if (score instanceof Number) {
                                event.setDetectionScore(((Number) score).doubleValue());
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析检测分数失败", e);
                }
            }
            
            // 原始数据和状态
            event.setRawJsonData(null);
            event.setProcessStatus(0); // 未处理
            event.setAlarmStatus(0); // 未告警
            
            // 保存到数据库
            save(event);
            log.info("安全监控事件保存成功: {}", event.getExternalEventId());
            
            return event;
            
        } catch (Exception e) {
            log.error("保存安全监控事件失败", e);
            throw new RuntimeException("保存安全监控事件失败", e);
        }
    }

    @Override
    public SafetyMonitorEvent getByExternalEventId(String externalEventId) {
        return safetyMonitorEventMapper.selectByExternalEventId(externalEventId);
    }

    @Override
    public List<SafetyMonitorEvent> getEventsByTimeRange(Date startTime, Date endTime) {
        return safetyMonitorEventMapper.selectByTimeRange(startTime, endTime);
    }

    @Override
    public List<SafetyMonitorEvent> getEventsByDeviceId(Long deviceId) {
        return safetyMonitorEventMapper.selectByDeviceId(deviceId);
    }

    @Override
    public List<SafetyMonitorEvent> getEventsByType(Integer eventType) {
        return safetyMonitorEventMapper.selectByEventType(eventType);
    }

    @Override
    public List<SafetyMonitorEvent> getAlarmEvents() {
        return safetyMonitorEventMapper.selectAlarmEvents(1);
    }

    @Override
    public List<Map<String, Object>> countEventsByType(Date startTime, Date endTime) {
        return safetyMonitorEventMapper.countEventsByType(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> countAlarmEventsByDevice(Date startTime, Date endTime) {
        return safetyMonitorEventMapper.countAlarmEventsByDevice(startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processEvent(Long eventId, String processResult) {
        try {
            SafetyMonitorEvent event = getById(eventId);
            if (event != null) {
                event.setProcessStatus(1); // 已处理
                event.setProcessResult(processResult);
                event.setProcessTime(new Date());
                return updateById(event);
            }
            return false;
        } catch (Exception e) {
            log.error("处理事件失败: {}", eventId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchProcessEvents(List<Long> eventIds, String processResult) {
        try {
            return safetyMonitorEventMapper.updateProcessStatus(eventIds, 1, processResult) > 0;
        } catch (Exception e) {
            log.error("批量处理事件失败", e);
            return false;
        }
    }

    @Override
    public List<SafetyMonitorEvent> getUnprocessedEvents() {
        return safetyMonitorEventMapper.selectUnprocessedEvents();
    }

    @Override
    public List<SafetyMonitorEvent> getRecentAlarmEvents(Integer limit) {
        return safetyMonitorEventMapper.selectRecentAlarmEvents(limit);
    }

    @Override
    public List<SafetyMonitorEvent> getEventsByDeviceSerialAndTimeRange(String deviceSerial, Date startTime, Date endTime) {
        return safetyMonitorEventMapper.selectByDeviceSerialAndTimeRange(deviceSerial, startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendAlarmNotification(SafetyMonitorEvent event) {
        try {
            // TODO: 实现具体的告警通知逻辑
            // 可以发送邮件、短信、推送通知等
            log.info("发送告警通知: 事件ID={}, 事件类型={}, 设备={}", 
                    event.getExternalEventId(), event.getEventTypeName(), event.getDeviceSerial());
            
            // 更新告警状态
            return updateAlarmStatus(event.getId(), 1);
            
        } catch (Exception e) {
            log.error("发送告警通知失败: {}", event.getExternalEventId(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAlarmStatus(Long eventId, Integer alarmStatus) {
        try {
            SafetyMonitorEvent event = getById(eventId);
            if (event != null) {
                event.setAlarmStatus(alarmStatus);
                if (alarmStatus == 1) {
                    event.setAlarmTime(new Date());
                }
                return updateById(event);
            }
            return false;
        } catch (Exception e) {
            log.error("更新告警状态失败: {}", eventId, e);
            return false;
        }
    }

    @Override
    public IPage<AlarmQueryResponse> getAlarmPageWithEnterpriseInfo(AlarmQueryRequest request) {
        try {
            log.info("开始查询预警列表，查询条件: {}", request);

            Page<AlarmQueryResponse> page = new Page<>(request.getPageNum(), request.getPageSize());
            IPage<AlarmQueryResponse> result = safetyMonitorEventMapper.selectAlarmPageWithEnterpriseInfo(page, request);

            log.info("预警列表查询完成，总数: {}, 当前页: {}, 每页大小: {}",
                    result.getTotal(), result.getCurrent(), result.getSize());

            return result;

        } catch (Exception e) {
            log.error("查询预警列表失败", e);
            throw new RuntimeException("查询预警列表失败: " + e.getMessage());
        }
    }

    /**
     * 解析时间字符串
     */
    private Date parseTimeString(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            return sdf.parse(timeStr);
        } catch (Exception e) {
            log.warn("解析时间字符串失败: {}", timeStr);
            return null;
        }
    }

    /**
     * 获取事件类型名称
     */
    private String getEventTypeName(Integer eventType) {
        if (eventType == null) {
            return "未知事件";
        }
        switch (eventType) {
            case 121:
                return "安全帽检测";
            case 122:
                return "反光衣检测";
            case 123:
                return "人员入侵检测";
            default:
                return "其他安全事件";
        }
    }



    /**
     * 安全获取Long值
     */
    private Long getLongValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            log.warn("转换Long值失败: {}", value);
            return null;
        }
    }
}
