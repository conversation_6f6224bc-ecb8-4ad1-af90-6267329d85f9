package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.TDictionaries;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.RegionMapper;
import com.example.eps.ins.mapper.WarningForCardMapper;
import com.example.eps.ins.service.WarningForCardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/11/10 13:56
 * @Version: 1.0
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class WarningForCardServiceImpl extends ServiceImpl<WarningForCardMapper, WarningForCard>
        implements WarningForCardService {
    private final WarningForCardMapper warningForCardMapper;
    private final RegionMapper regionMapper;

    @Override
    public IPage<WarningForCard> findSpecialPeoples(QueryRequest request, WarningForCard warningForCard) {
        Long userId = EpsUtil.getCurrentUser().getUserId();
        request.setPageNum((request.getPageNum() - 1) * request.getPageSize());
        List<WarningForCard> list = warningForCardMapper.selectAll(warningForCard.getCode(),
                userId,request.getPageNum(),request.getPageSize());

        List<WarningForCard> total = warningForCardMapper.selectAll(warningForCard.getCode(),
                userId,null,null);
        IPage<WarningForCard> page = new Page<>();
        page.setRecords(list);
        page.setTotal(total.size());
        return page;
    }

    @Override
    public List<TDictionaries> getCode() {
        return warningForCardMapper.getCode();
    }

    @Override
    public Boolean updateWar(WarningForCard warningForCard) {
        warningForCardMapper.updateById(warningForCard);
        return true;
    }
    @Override
    public void updateWaringForCard(Long id) {
        warningForCardMapper.updateStatus(id);
    }

    @Override
    public IPage<WarningForCard> findSpecialPeoplesSystem(
            QueryRequest request, WarningForCard warningForCard) {
        request.setPageNum((request.getPageNum() - 1) * request.getPageSize());
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        Long regionId = currentUser.getRegionId();
        // 获取该地址所属地区
        Region region = regionMapper.selectOne(new QueryWrapper<Region>().eq("id", regionId));
        String name = warningForCard.getName();
        if (!ObjectUtils.isEmpty(name)){
            name = name.trim();
        }
        List<WarningForCard> list = warningForCardMapper.selectAllSystem(region.getLevel(),warningForCard.getCode(),
                name,currentUser.getUserId(),regionId,request.getPageNum(),request.getPageSize());

        List<WarningForCard> total = warningForCardMapper.selectAllSystem(region.getLevel(),warningForCard.getCode(),
                name,currentUser.getUserId(),regionId,null,null);
        IPage<WarningForCard> page = new Page<>(request.getPageNum(), request.getPageSize());
        page.setRecords(list);
        page.setTotal(total.size());
        return page;
    }
}
