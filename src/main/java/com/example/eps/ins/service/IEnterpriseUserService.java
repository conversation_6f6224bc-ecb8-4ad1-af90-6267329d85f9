package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.bean.ForgetMobile;
import com.example.eps.ins.common.bean.ForgetName;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EnterpriseUser;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.exception.ValidateCodeException;

import java.util.List;

/**
 * Service接口
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:30
 */
public interface IEnterpriseUserService extends IService<EnterpriseUser> {
    /**
     * 查询（分页）
     *
     * @param request        QueryRequest
     * @param enterpriseUser enterpriseUser
     * @return IPage<EnterpriseUser>
     */
    IPage<EnterpriseUser> findEnterpriseUsers(QueryRequest request, EnterpriseUser enterpriseUser);

    /**
     * 删除
     *
     * @param enterpriseUser enterpriseUser
     */
    void deleteEnterpriseUser(EnterpriseUser enterpriseUser);
    /**
     * 查询（不分页）
     *
     * @param enterpriseUser enterpriseUser
     * @return List<EnterpriseUser>
     */
    List<EnterpriseUser> findEnterpriseUsers(EnterpriseUser enterpriseUser);

    /**
     * 新增
     *
     * @param enterpriseUser EnterpriseUser
     */
    Long createEnterpriseUser(EnterpriseUser enterpriseUser) throws ValidateCodeException;

    /**
     * 修改
     *
     * @param enterpriseUser enterpriseUser
     */
    void updateEnterpriseUser(EnterpriseUser enterpriseUser) throws EpsException;

    /**
     * Check enterprise user .
     *
     * @param  mobile
     */
    boolean checkEnterpriseUser(String creditCode, String mobile);

    boolean validatedCode(String key, String code);

    void updateMobile(ForgetMobile forgetMobile);

    void updateName(ForgetName forgetName);
}
