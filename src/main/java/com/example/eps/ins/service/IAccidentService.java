package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.Accident;


import java.util.List;

/**
 * 事故统计表 Service接口
 *
 * <AUTHOR>
 * @date 2021-10-27 14:28:15
 */
public interface IAccidentService extends IService<Accident> {
    /**
     * 查询（分页）
     *
     * @param request  QueryRequest
     * @param accident accident
     * @return IPage<Accident>
     */
    IPage<Accident> findAccidents(QueryRequest request, Accident accident);

    /**
     * 查询（所有）
     *
     * @param accident accident
     * @return List<Accident>
     */
    List<Accident> findAccidents(Accident accident);

    /**
     * 查询事故区县列表
     *
     * @return
     */
    List<Accident> findAccidentCountryList();

    /**
     * 新增
     *
     * @param accident accident
     */
    void createAccident(Accident accident);

    /**
     * 修改
     *
     * @param accident accident
     */
    void updateAccident(Accident accident);

    /**
     * 删除
     *
     * @param accident accident
     */
    void deleteAccident(Accident accident);
}
