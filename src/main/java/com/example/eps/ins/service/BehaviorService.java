package com.example.eps.ins.service;

import com.example.eps.ins.common.dto.report.req.BehaviorReq;
import com.example.eps.ins.common.dto.report.req.EnforcementReq;
import com.example.eps.ins.common.dto.report.resp.BehaviorResp;
import com.example.eps.ins.common.dto.report.resp.EnforcementResp;
import com.example.eps.ins.common.dto.report.resp.YearCheckListResp;

import java.util.List;

/**
 * @Author: zhanghongyin
 * @Date: Created in 2021/12/1 17:02
 * @Version: 1.0
 */
public interface BehaviorService {

    /**
     * 违法数据年度排序
     * @param behaviorReq 参数
     * @return 装有年度排序的集合
     */
    List<BehaviorResp> getSort(BehaviorReq behaviorReq);

    /**
     * 获取年度执法数据总览
     * @param behaviorReq 请求参数
     * @return 年度执法数据集合
     */
    EnforcementResp getAll(EnforcementReq behaviorReq);

    /**
     * 年度执法数据统计
     * @param behaviorReq 请求参数
     * @return 集合
     */
    YearCheckListResp getEnforcementList(EnforcementReq behaviorReq);
}
