package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.MessageSendOne;
import com.example.eps.ins.common.dto.enterprise.NoSeeResp;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2023/1/10 14:36
 * @Version: 1.0
 */
public interface MessageSendService extends IService<MessageSendOne> {

    /**
     * 点对点消息发送
     * @param id
     * @return
     */
    String updateStatus(Long id);

    NoSeeResp noSeeNum();
    /**
     * 点对点消息发送
     * @param messageSendOne
     * @return
     */
    String sendMessage(MessageSendOne messageSendOne);

    void deleteMessage(Long id);
    /**
     * 查询（分页）
     *
     * @param request        QueryRequest
     * @param messageSendOne messageSendOne
     * @return IPage<EnterpriseUser>
     */
    IPage<MessageSendOne> messageList(QueryRequest request, MessageSendOne messageSendOne);
}
