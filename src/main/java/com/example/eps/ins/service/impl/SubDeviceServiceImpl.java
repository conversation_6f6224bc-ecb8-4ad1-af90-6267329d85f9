package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.dto.device.SubDeviceInfo;
import com.example.eps.ins.common.dto.riskPush.CameraResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.device.SubDevice;
import com.example.eps.ins.mapper.SubDeviceMapper;
import com.example.eps.ins.service.ISubDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 子设备信息 Service实现
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@Service
public class SubDeviceServiceImpl extends ServiceImpl<SubDeviceMapper, SubDevice> implements ISubDeviceService {

    @Autowired
    private SubDeviceMapper subDeviceMapper;

    @Override
    public List<SubDevice> findByParentDeviceId(Long parentDeviceId) {
        return this.subDeviceMapper.selectByParentDeviceId(parentDeviceId);
    }

    @Override
    public SubDevice findByExternalSubDeviceId(Long externalSubDeviceId) {
        return this.subDeviceMapper.selectByExternalSubDeviceId(externalSubDeviceId);
    }

    @Override
    public SubDevice findByParentDeviceIdAndSerial(Long parentDeviceId, String serial) {
        return this.subDeviceMapper.selectByParentDeviceIdAndSerial(parentDeviceId, serial);
    }

    @Override
    public IPage<SubDevice> findSubDevices(QueryRequest request, SubDevice subDevice) {
        Page<SubDevice> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.subDeviceMapper.selectSubDevicePage(page, subDevice);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncSubDevices(Long parentDeviceId, List<SubDeviceInfo> subDeviceInfoList) {
        if (subDeviceInfoList == null || subDeviceInfoList.isEmpty()) {
            log.info("父设备ID: {} 的子设备信息列表为空，无需同步", parentDeviceId);
            return true;
        }

        try {
            List<SubDevice> subDevicesToSave = new ArrayList<>();
            Date now = new Date();

            for (SubDeviceInfo subDeviceInfo : subDeviceInfoList) {
                SubDevice existingSubDevice = null;
                
                // 优先通过外部子设备ID查找
                if (subDeviceInfo.getId() != null) {
                    existingSubDevice = findByExternalSubDeviceId(subDeviceInfo.getId());
                }
                
                // 如果通过外部ID没找到，再通过父设备ID和序列号查找
                if (existingSubDevice == null && StringUtils.hasText(subDeviceInfo.getSerial())) {
                    existingSubDevice = findByParentDeviceIdAndSerial(parentDeviceId, subDeviceInfo.getSerial());
                }
                
                if (existingSubDevice != null) {
                    // 更新现有子设备
                    updateSubDeviceFromInfo(existingSubDevice, subDeviceInfo);
                    existingSubDevice.setLastSyncTime(now);
                    existingSubDevice.setSyncStatus(1);
                    this.updateById(existingSubDevice);
                    log.info("更新子设备信息: {}", subDeviceInfo.getDeviceName());
                } else {
                    // 创建新子设备
                    SubDevice newSubDevice = createSubDeviceFromInfo(parentDeviceId, subDeviceInfo);
                    newSubDevice.setLastSyncTime(now);
                    newSubDevice.setSyncStatus(1);
                    subDevicesToSave.add(newSubDevice);
                    log.info("新增子设备信息: {}", subDeviceInfo.getDeviceName());
                }
            }

            // 批量保存新子设备
            if (!subDevicesToSave.isEmpty()) {
                return this.saveBatch(subDevicesToSave);
            }

            return true;
        } catch (Exception e) {
            log.error("同步子设备信息失败，父设备ID: {}", parentDeviceId, e);
            throw new RuntimeException("同步子设备信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SubDevice saveOrUpdateSubDevice(Long parentDeviceId, SubDeviceInfo subDeviceInfo) {
        SubDevice existingSubDevice = null;
        
        // 优先通过外部子设备ID查找
        if (subDeviceInfo.getId() != null) {
            existingSubDevice = findByExternalSubDeviceId(subDeviceInfo.getId());
        }
        
        // 如果通过外部ID没找到，再通过父设备ID和序列号查找
        if (existingSubDevice == null && StringUtils.hasText(subDeviceInfo.getSerial())) {
            existingSubDevice = findByParentDeviceIdAndSerial(parentDeviceId, subDeviceInfo.getSerial());
        }
        
        Date now = new Date();

        if (existingSubDevice != null) {
            // 更新现有子设备
            updateSubDeviceFromInfo(existingSubDevice, subDeviceInfo);
            existingSubDevice.setLastSyncTime(now);
            existingSubDevice.setSyncStatus(1);
            this.updateById(existingSubDevice);
            return existingSubDevice;
        } else {
            // 创建新子设备
            SubDevice newSubDevice = createSubDeviceFromInfo(parentDeviceId, subDeviceInfo);
            newSubDevice.setLastSyncTime(now);
            newSubDevice.setSyncStatus(1);
            this.save(newSubDevice);
            return newSubDevice;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveSubDevices(List<SubDevice> subDeviceList) {
        if (subDeviceList == null || subDeviceList.isEmpty()) {
            return true;
        }
        return this.saveBatch(subDeviceList);
    }

    @Override
    public List<SubDevice> findBySyncStatus(Integer syncStatus) {
        return this.subDeviceMapper.selectBySyncStatus(syncStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSyncStatus(Long subDeviceId, Integer syncStatus) {
        SubDevice subDevice = this.getById(subDeviceId);
        if (subDevice != null) {
            subDevice.setSyncStatus(syncStatus);
            subDevice.setLastSyncTime(new Date());
            return this.updateById(subDevice);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByParentDeviceId(Long parentDeviceId) {
        return this.subDeviceMapper.deleteByParentDeviceId(parentDeviceId) >= 0;
    }

    @Override
    public List<SubDevice> findByParentDeviceIdAndStatus(Long parentDeviceId, String status) {
        return this.subDeviceMapper.selectByParentDeviceIdAndStatus(parentDeviceId, status);
    }

    @Override
    public List<CameraResponse> findCamerasByEnterpriseId(Long enterpriseId) {
        log.info("开始查询企业ID: {} 的相机列表", enterpriseId);
        try {
            List<CameraResponse> cameras = this.subDeviceMapper.selectCamerasByEnterpriseId(enterpriseId);
            log.info("查询企业ID: {} 的相机列表完成，共找到 {} 个相机", enterpriseId, cameras.size());
            return cameras;
        } catch (Exception e) {
            log.error("查询企业ID: {} 的相机列表失败", enterpriseId, e);
            throw new RuntimeException("查询企业相机列表失败: " + e.getMessage());
        }
    }

    /**
     * 从SubDeviceInfo创建SubDevice实体
     */
    private SubDevice createSubDeviceFromInfo(Long parentDeviceId, SubDeviceInfo subDeviceInfo) {
        SubDevice subDevice = new SubDevice();
        subDevice.setParentDeviceId(parentDeviceId);
        subDevice.setExternalSubDeviceId(subDeviceInfo.getId());
        subDevice.setDeviceName(subDeviceInfo.getDeviceName());
        subDevice.setSerial(subDeviceInfo.getSerial());
        subDevice.setStatus(subDeviceInfo.getStatus());
        subDevice.setDeviceType(subDeviceInfo.getDeviceType());
        subDevice.setIp(subDeviceInfo.getIp());
        return subDevice;
    }

    /**
     * 从SubDeviceInfo更新SubDevice实体
     */
    private void updateSubDeviceFromInfo(SubDevice subDevice, SubDeviceInfo subDeviceInfo) {
        if (subDeviceInfo.getId() != null) {
            subDevice.setExternalSubDeviceId(subDeviceInfo.getId());
        }
        if (StringUtils.hasText(subDeviceInfo.getDeviceName())) {
            subDevice.setDeviceName(subDeviceInfo.getDeviceName());
        }
        if (StringUtils.hasText(subDeviceInfo.getSerial())) {
            subDevice.setSerial(subDeviceInfo.getSerial());
        }
        if (StringUtils.hasText(subDeviceInfo.getStatus())) {
            subDevice.setStatus(subDeviceInfo.getStatus());
        }
        if (StringUtils.hasText(subDeviceInfo.getDeviceType())) {
            subDevice.setDeviceType(subDeviceInfo.getDeviceType());
        }
        if (StringUtils.hasText(subDeviceInfo.getIp())) {
            subDevice.setIp(subDeviceInfo.getIp());
        }
    }
}
