package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.PresentSituation;

import java.io.IOException;

/**
 * @Author: <PERSON><PERSON><PERSON>n
 * @Date: Created in 2022/10/11 9:51
 * @Version: 1.0
 */

public interface PresentSituationService extends IService<PresentSituation> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param presentSituation presentSituation
     * @return IPage<EmergencyPlan>
     */
    IPage<PresentSituation> findPresentSituations(QueryRequest request, PresentSituation presentSituation);

    /**
     * 新增
     * @param presentSituation presentSituation
     * @throws IOException IOException
     */
    String createPresentSituation(PresentSituation presentSituation);

    /**
     * 详情
     * @param id  id
     * @return
     */
    PresentSituation findPresentSituation(Long id);


    /**
     * 删除
     *
     */
    void deletePresentSituation(Long id);

    /**
     * 删除文件
     * @param id
     * @param filePath
     */
    void deleteFile(Long id, String filePath);
}
