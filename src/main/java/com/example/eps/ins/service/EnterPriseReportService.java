package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.eps.ins.common.dto.report.model.*;
import com.example.eps.ins.common.dto.report.req.CompanyView;
import com.example.eps.ins.common.dto.report.resp.*;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.*;

import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/11/8 15:04
 * @Version: 1.0
 */
public interface EnterPriseReportService {
    CakeResp getRiskEnterPrise(Long regionId);

    List<RiskListResp> getRisk(Long regionId);

    CakeResp getUpAndDown(Long regionId);

    EnterpriseProportionResp getThreeNow(Long regionId);

    EnterpriseProportionResp getDangerStandard(Long regionId);

    EnterpriseProportionResp getPlanReport(Long regionId);

    EnterpriseProportionResp getHiddenReport(Long regionId);

    List<EnterpriseHistogramResp> getDrillReport(CompanyView companyView);

    List<HistogramResp> getListReport(CompanyView companyView);

    List<EnterpriseHistogramResp>  getTrainReport(CompanyView companyView);

    List<EnterpriseHistogramResp>  getMaintainReport(CompanyView companyView);

    EnterpriseProportionResp getOrderCard(Long regionId);

    /**
     * 安全三同时及现状评价列表

     * @param presentSituation
     * @return
     */
    IPage<PresentSituation> findPresentSituationLists(PresentSituationVo presentSituation);

    /**
     * 复工复产

     * @param outsourceWork
     * @return
     */
    IPage<ComplexWork> findComplexWork(ComplexWorkVo outsourceWork);

    /**
     * 应急预案

     * @param emergencyPlan
     * @return
     */
    IPage<EmergencyPlan> findEmergencyPlans(EmergencyPlanVO emergencyPlan);

    /**
     * 应急演练

     * @param emergencyPlan
     * @return
     */
    IPage<Emergencydrill> findEmergencyDrills(EmergencydrillVO emergencyPlan);

    /**
     * 设施设备维护

     * @param equipmentMaintain
     * @return
     */
    IPage<EquipmentMaintain> findEquipmentMaintains( EquipmentMaintainVO equipmentMaintain);

    /**
     * 有限空间作业许可证

     * @param finiteSpaceWork
     * @return
     */
    IPage<FiniteSpaceWork> findFiniteSpaceWorks(FiniteSpaceWorkVO finiteSpaceWork);

//    /**
//     * 隐患治理
//
//     * @param hiddenDangerList
//     * @return
//     */
//    IPage<HiddenDangerList> findHiddenDangerList(QueryRequest request, HiddenDangerList hiddenDangerList);

    /**
     * 隐患排查制度维护

     * @param emergencyPlan
     * @return
     */
    IPage<HiddenDanger> findHiddenDanger( HiddenDangerVo emergencyPlan);

    /**
     * 外委作业

     * @param outsourceWork
     * @return
     */
    IPage<OutsourceWork> findOutsourceWork (OutsourceWorkVO outsourceWork);

    /**
     * 特种作业许可证

     * @param specialCertificate
     * @return
     */
    IPage<SpecialCertificate> findSpecialCertificates( SpecialCertificateVo specialCertificate);

    /**
     * 特种人员

     * @param emergencyPlan
     * @return
     */
    IPage<SpecialPeople> findSpecialPeoples(SpecialPeopleVo emergencyPlan);

    /**
     * 培训教育

     * @param trainEducation
     * @return
     */
    IPage<TrainEducation> findTrainEducations(
            TrainEducationVo trainEducation);

    /**
     * 两单两卡预警

     * @param warningForCard
     * @return
     */
    IPage<WarningForCard> findWarningForCards(
            QueryRequest request, WarningForCard warningForCard);
}
