package com.example.eps.ins.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.dto.report.model.HiddenDangerVo;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.HiddenDanger;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.mapper.EnterpriseMapper;
import com.example.eps.ins.mapper.HiddenDangerMapper;
import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.HiddenDangerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT;

/** @Author: Zhanghongyin @Date: Created in 2022/10/11 9:52 @Version: 1.0 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class HiddenDangerServiceImpl extends ServiceImpl<HiddenDangerMapper, HiddenDanger>
    implements HiddenDangerService {

  private final FdfsClientService fdfsClientService;
  private final HiddenDangerMapper hiddenDangerMapper;
  private final EnterpriseMapper enterpriseMapper;
  private final EnterpriseDeclarationServiceImpl enterpriseDeclarationService;

  @Override
  public IPage<HiddenDanger> findHiddenDanger(QueryRequest request, HiddenDanger emergencyPlan) {
    Long userId = EpsUtil.getCurrentUser().getUserId();
    if (!ObjectUtils.isEmpty(emergencyPlan.getCreatorId())){
      userId=emergencyPlan.getCreatorId();
    }
    LambdaQueryWrapper<HiddenDanger> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(HiddenDanger::getCreatorId, userId);
    if (!ObjectUtils.isEmpty(emergencyPlan.getHiddenDangerName())) {
      queryWrapper.like(HiddenDanger::getHiddenDangerName, emergencyPlan.getHiddenDangerName());
    }
    queryWrapper.orderByDesc(HiddenDanger::getCreateTime);
    Page<HiddenDanger> page = new Page<>(request.getPageNum(), request.getPageSize());
    return this.page(page, queryWrapper);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String createHiddenDanger(HiddenDangerVo emergencyPlanVo) {
    //获取所有的企业申报信息
    List<EnterpriseDeclarationResponse> enterprise =
            enterpriseDeclarationService.currentUserEnterpriseDeclarationList();
    //获取所有申报状态
    List<Integer> collect = enterprise.stream().map(EnterpriseDeclarationResponse::getStatus).collect(Collectors.toList());
    if (ObjectUtils.isEmpty(enterprise)){
      throw new EpsException("请完善企业信息后创建企业隐患排查制度维护信息！");
    }
    if (!collect.contains(ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT)){
      throw new EpsException("企业信息暂未审核通过，请审核通过后创建企业隐患排查制度维护信息！");
    }
    // 保存消息
    HiddenDanger hiddenDanger = new HiddenDanger();
    hiddenDanger.setHiddenDangerName(emergencyPlanVo.getHiddenDangerName());
    if (ObjectUtils.isEmpty(emergencyPlanVo.getHiddenDangerFile())){
      hiddenDanger.setHiddenDangerFile("[]");
    } else {
      hiddenDanger.setHiddenDangerFile(emergencyPlanVo.getHiddenDangerFile());
    }
    hiddenDanger.setDigest(emergencyPlanVo.getDigest());
    hiddenDanger.setCreateTime(new Date());
    if (ObjectUtils.isEmpty(emergencyPlanVo.getId())) {
      CurrentUser currentUser = EpsUtil.getCurrentUser();
      hiddenDanger.setRegionId(enterprise.get(0).getRegionTownId());
      hiddenDanger.setCreatorId(currentUser.getUserId());
      this.save(hiddenDanger);
    } else {
      hiddenDanger.setCreatorId(emergencyPlanVo.getCreatorId());
      this.update(
              hiddenDanger, new QueryWrapper<HiddenDanger>().eq("id", emergencyPlanVo.getId()));
    }
    enterpriseMapper.updateHiddenStatus(emergencyPlanVo.getCreatorId());
    return "";
  }

  @Override
  public HiddenDanger findHiddenDanger(Long id) {
    return hiddenDangerMapper.selectById(id);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteHiddenDanger(Long id) {
    LambdaQueryWrapper<HiddenDanger> userWarpper = new LambdaQueryWrapper<>();
    userWarpper.eq(HiddenDanger::getId, id);
    HiddenDanger hiddenDanger = this.getById(id);
    if (hiddenDanger == null) {
      return;
    }
    JSONArray jsonArray = JSON.parseArray(hiddenDanger.getHiddenDangerFile());
    hiddenDangerMapper.delete(userWarpper);
    for (Object obj : jsonArray) {
      fdfsClientService.deleteFile(((JSONObject) obj).getString("filePath"));
    }
  }

  @Override
  public void deleteFile(Long id, String filePath) {
    if (!ObjectUtils.isEmpty(id)) {
      HiddenDanger hiddenDanger = hiddenDangerMapper.selectById(id);
      if (ObjectUtils.isEmpty(hiddenDanger)) {
        return;
      }
      JSONArray fileArray = JSON.parseArray(hiddenDanger.getHiddenDangerFile());
      JSONArray fileJson = getArray(filePath, fileArray);
      hiddenDanger.setHiddenDangerFile(fileJson.toString());
      hiddenDangerMapper.updateById(hiddenDanger);
    }
    fdfsClientService.deleteFile(filePath);
  }

  private JSONArray getArray(String filePath, JSONArray fileArray) {
    JSONArray json = new JSONArray();
    for (Object obj : fileArray) {
      String filePath1 = ((JSONObject) obj).getString("filePath");
      if (filePath1.equals(filePath)){
        continue;
      }
      Map<String, Object> map=new HashMap<>();
      map.put("fileUrl",((JSONObject) obj).getString("fileUrl"));
      map.put("filePath",((JSONObject) obj).getString("filePath"));
      map.put("fileName",((JSONObject) obj).getString("fileName"));
      json.add(EpsUtil.toJsonObj(map));
    }
    return json;
  }
}
