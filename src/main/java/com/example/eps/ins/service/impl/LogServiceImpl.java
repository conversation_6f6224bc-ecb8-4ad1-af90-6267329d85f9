package com.example.eps.ins.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.constant.EpsConstant;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.entity.system.Log;
import com.example.eps.ins.common.utils.AddressUtil;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.utils.SortUtil;
import com.example.eps.ins.mapper.LogMapper;
import com.example.eps.ins.mapper.RegionMapper;
import com.example.eps.ins.service.ILogService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class LogServiceImpl extends ServiceImpl<LogMapper, Log> implements ILogService {

    private final ObjectMapper objectMapper;
    private final RegionMapper regionMapper;
    private static final int LIVE_ONE = 1;// 未建立隐患制度
    private static final int LIVE_TWO = 2;// 未建立隐患制度
    private static final int LIVE_THREE = 3;// 未建立隐患制度

    @Override
    public IPage<Log> findLogs(Log log, QueryRequest request) {
        QueryWrapper<Log> queryWrapper = new QueryWrapper<>();
        Long currentUser = EpsUtil.getCurrentUser().getRegionId();
        Region region = regionMapper.selectById(currentUser);
        if (region.getLevel().equals(LIVE_TWO)){
            List<Long> regions = regionMapper.selectSon(currentUser);
            regions.add(currentUser);
            queryWrapper.lambda().in(Log::getRegionId,regions);
        }else if (region.getLevel().equals(LIVE_THREE)){
            queryWrapper.lambda().eq(Log::getRegionId,currentUser);
        }
        if (StringUtils.isNotBlank(log.getUsername())) {
            queryWrapper.lambda().eq(Log::getUsername, log.getUsername().toLowerCase());
        }
        if (StringUtils.isNotBlank(log.getOperation())) {
            queryWrapper.lambda().like(Log::getOperation, log.getOperation());
        }
        if (StringUtils.isNotBlank(log.getLocation())) {
            queryWrapper.lambda().like(Log::getLocation, log.getLocation());
        }
        if (StringUtils.isNotBlank(log.getCreateTimeFrom()) && StringUtils.isNotBlank(log.getCreateTimeTo())) {
            queryWrapper.lambda()
                    .ge(Log::getCreateTime, log.getCreateTimeFrom())
                    .le(Log::getCreateTime, log.getCreateTimeTo());
        }

        Page<Log> page = new Page<>(request.getPageNum(), request.getPageSize());
        SortUtil.handlePageSort(request, page, "createTime", EpsConstant.ORDER_DESC, true);

        return this.page(page, queryWrapper);
    }

    @Override
    public List<String> getOperations(){
        LambdaQueryWrapper<Log> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.select(Log::getOperation);
        queryWrapper.groupBy(Log::getOperation);
        List<Log> logs=this.list(queryWrapper);
        return logs.stream().map(e -> e.getOperation()).collect(Collectors.toList());
    }

    @Override
    public void deleteLogs(String[] logIds) {
        List<String> list = Arrays.asList(logIds);
        baseMapper.deleteBatchIds(list);
    }

    @Override
    public void saveLog(ProceedingJoinPoint point, Method method, String ip, String operation, String username,
                        long start, CurrentUser currentUser) {
        Log log = new Log();
        log.setIp(ip);
        log.setNickName(currentUser.getNickName());
        Region region = regionMapper.selectById(currentUser.getRegionId());
        if (region.getLevel().equals(LIVE_THREE)){
            log.setRegionName(regionMapper.selectById(region.getParentId()).getName());
        }else {
            log.setRegionName(region.getName());
        }
        log.setDept(currentUser.getDeptName());
        log.setRegionId(currentUser.getRegionId());
        log.setUsername(username);
        log.setTime(System.currentTimeMillis() - start);
        log.setOperation(operation);

        String className = point.getTarget().getClass().getName();
        String methodName = method.getName();
        log.setMethod(className + "." + methodName + "()");

        Object[] args = point.getArgs();
        LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
        String[] paramNames = u.getParameterNames(method);
        if (args != null && paramNames != null) {
            StringBuilder params = new StringBuilder();
            params = handleParams(params, args, Arrays.asList(paramNames));
            log.setParams(params.toString());
        }
        log.setCreateTime(new Date());
        log.setLocation(AddressUtil.getCityInfo(ip));
        // 保存系统日志
        save(log);
    }

    @SuppressWarnings("all")
    private StringBuilder handleParams(StringBuilder params, Object[] args, List paramNames) {
        try {
            for (int i = 0; i < args.length; i++) {
                if (args[i] instanceof Map) {
                    Set set = ((Map) args[i]).keySet();
                    List<Object> list = new ArrayList<>();
                    List<Object> paramList = new ArrayList<>();
                    for (Object key : set) {
                        list.add(((Map) args[i]).get(key));
                        paramList.add(key);
                    }
                    return handleParams(params, list.toArray(), paramList);
                } else {
                    if (args[i] instanceof Serializable) {
                        Class<?> aClass = args[i].getClass();
                        try {
                            aClass.getDeclaredMethod("toString", new Class[]{null});
                            // 如果不抛出 NoSuchMethodException 异常则存在 toString 方法 ，安全的 writeValueAsString ，否则 走 Object的 toString方法
                            params.append(" ").append(paramNames.get(i)).append(": ").append(objectMapper.writeValueAsString(args[i]));
                        } catch (NoSuchMethodException e) {
                            params.append(" ").append(paramNames.get(i)).append(": ").append(objectMapper.writeValueAsString(args[i].toString()));
                        }
                    } else if (args[i] instanceof MultipartFile) {
                        MultipartFile file = (MultipartFile) args[i];
                        params.append(" ").append(paramNames.get(i)).append(": ").append(file.getName());
                    } else {
                        params.append(" ").append(paramNames.get(i)).append(": ").append(args[i]);
                    }
                }
            }
        } catch (Exception ignore) {
            params.append("参数解析失败");
        }
        return params;
    }
}
