package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.bean.ForgetMobile;
import com.example.eps.ins.common.bean.ForgetName;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.constant.EpsConstant;
import com.example.eps.ins.common.entity.enterprise.EnterpriseUser;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.exception.ValidatePasswordException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.utils.SortUtil;
import com.example.eps.ins.common.service.RedisService;
import com.example.eps.ins.mapper.EnterpriseUserMapper;
import com.example.eps.ins.service.IEnterpriseUserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;

/**
 * Service实现
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:30
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class EnterpriseUserServiceImpl extends ServiceImpl<EnterpriseUserMapper, EnterpriseUser> implements IEnterpriseUserService {

    private final RedisService redisService;
    @Override
    public IPage<EnterpriseUser> findEnterpriseUsers(QueryRequest request, EnterpriseUser enterpriseUser) {
        LambdaQueryWrapper<EnterpriseUser> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(enterpriseUser.getCreateTimeFrom())
                && StringUtils.isNotBlank(enterpriseUser.getCreateTimeTo())) {
            queryWrapper.ge(EnterpriseUser::getCreateTime, enterpriseUser.getCreateTimeFrom())
                    .le(EnterpriseUser::getCreateTime, enterpriseUser.getCreateTimeTo());
        }
        Page<EnterpriseUser> page = new Page<>(request.getPageNum(), request.getPageSize());
        SortUtil.handlePageSort(request, page, "createTime", EpsConstant.ORDER_DESC, true);
        return this.page(page, queryWrapper);
    }

    @Override
    public List<EnterpriseUser> findEnterpriseUsers(EnterpriseUser enterpriseUser) {
        LambdaQueryWrapper<EnterpriseUser> queryWrapper = new LambdaQueryWrapper<>(enterpriseUser);
        return this.baseMapper.selectList(queryWrapper);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEnterpriseUser(EnterpriseUser enterpriseUser) {
        LambdaQueryWrapper<EnterpriseUser> wapper = new LambdaQueryWrapper<>();
        // TODO 设置删除条件
        this.remove(wapper);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createEnterpriseUser(EnterpriseUser enterpriseUser)
            throws ValidatePasswordException, EpsException {
        if(isEnterpriseUserAlreadyExists(null,
                enterpriseUser.getEnterpriseName(), enterpriseUser.getMobile())){
            throw new EpsException("企业名称或手机号已存在");
        }
        String password = EpsUtil.decodeBase64(enterpriseUser.getPassword());
        if (EpsUtil.checkPassword(password)) {
            PasswordEncoder encoder = new BCryptPasswordEncoder();
            enterpriseUser.setPassword(encoder.encode(password));
        } else {
            throw new ValidatePasswordException("密码不符合要求");
        }
        Date currentDate = new Date();
        enterpriseUser.setCreateTime(currentDate);
        enterpriseUser.setUpdateTime(currentDate);
        this.save(enterpriseUser);
        return enterpriseUser.getUserId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnterpriseUser(EnterpriseUser enterpriseUser) throws EpsException {
//        if(isEnterpriseUserAlreadyExists(enterpriseUser.getUserId(),
//                enterpriseUser.getCreditCode(), enterpriseUser.getMobile())){
//            throw new EpsException("社信代码或手机号已存在");
//        }
//        enterpriseUser.setCreateTime(null);
//        enterpriseUser.setPassword(null);
//        enterpriseUser.setUpdateTime(new Date());
        this.updateById(enterpriseUser);
    }

    @Override
    public boolean checkEnterpriseUser(String creditCode, String mobile) throws EpsException {
        return isEnterpriseUserAlreadyExists(null, creditCode, mobile);
    }

    @Override
    public boolean validatedCode(String key, String value) {
        Object codeInRedis;
        if ("9999".equals(value)){
            return true;
        }
        codeInRedis = redisService.get(EpsConstant.SMS_CODE_PREFIX + key);
        if (StringUtils.isBlank(value) || codeInRedis == null || (!StringUtils.equalsIgnoreCase(value,
                String.valueOf(codeInRedis)))) {
            return false;
        }
        return true;
    }

    @Override
    public void updateMobile(ForgetMobile forgetMobile) {
        boolean coedValidated = validatedCode(forgetMobile.getMobile(), forgetMobile.getSmsCode());
        if (!coedValidated) {
            throw new EpsException("验证码错误");
        }
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        this.baseMapper.updatePhone(forgetMobile.getMobile(),currentUser.getUserId());
    }
    @Override
    public void updateName(ForgetName forgetName) {
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        if (!currentUser.getMobile().equals(forgetName.getMobile())){
            throw new EpsException("接收验证码的电话号码非当前登录电话号码！");
        }
        EnterpriseUser enterpriseUser = this.baseMapper.selectOne(new QueryWrapper<EnterpriseUser>()
                .eq("enterprise_name", forgetName.getName()));
        if (!ObjectUtils.isEmpty(enterpriseUser)){
            if (enterpriseUser.getUserId().equals(currentUser.getUserId())){
                throw new EpsException("新名称与旧名称相同，无需更改！");
            }else {
                throw new EpsException("企业名已存在，请重新输入！");
            }
        }
        boolean coedValidated = validatedCode(forgetName.getMobile(), forgetName.getSmsCode());
        if (!coedValidated) {
            throw new EpsException("验证码错误");
        }
        this.baseMapper.updateName(forgetName.getName(),currentUser.getUserId());
    }
    /**
     * Check enterprise user already exists .
     *
     * @param userId
     * @param mobile
     * @return result
     */
    private boolean isEnterpriseUserAlreadyExists(Integer userId,
                                                  String creditCode, String mobile) {
        QueryWrapper<EnterpriseUser> queryWrapper = new QueryWrapper<>();
        if (null != userId) {
            queryWrapper.ne("user_id", userId);
        }
        queryWrapper.and(wrapper ->
                wrapper.eq("enterprise_name", creditCode).or().eq("mobile", mobile));
        return this.baseMapper.selectCount(queryWrapper) > 0;
    }
}
