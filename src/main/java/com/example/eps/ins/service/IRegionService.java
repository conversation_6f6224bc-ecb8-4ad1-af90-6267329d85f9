package com.example.eps.ins.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.eps.ins.common.bean.RegionVO;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Region;

import java.util.List;
import java.util.Map;

/**
 * 地区表 Service接口
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:39
 */
public interface IRegionService extends IService<Region> {
    /**
     * 查询（分页）
     *
     * @param request QueryRequest
     * @param region region
     * @return IPage<Region>
     */
    IPage<Region> findRegions(QueryRequest request, Region region);

    /**
     * 查询子数据（所有）
     *
     * @param parentId parentId
     * @return List<Region>
     */
    List<Region> findRegions(int parentId);

    /**
     * 查询地区树（供页面展示）
     *
     * @return RegionVO
     */
    RegionVO getAllRegions();

    /**
     * 新增
     *
     * @param region region
     */
    void createRegion(Region region);

    /**
     * 修改
     *
     * @param region region
     */
    void updateRegion(Region region);

    /**
     * 删除
     *
     * @param region region
     */
    void deleteRegion(Region region);

    /**
     * 查询地区树（供页面展示,需过滤组织名）
     *
     * @return RegionVO
     */
    RegionVO getAllRegionsFilterDept();

    /**
     * 获取当前用户所在（地区）组织及下级（地区）组织
     *
     * @return
     */
    RegionVO getCurrentUserRegions();

    /**
     * 获取当前用户所在（地区）组织及下级（地区）组织 供维护企业信息使用
     *
     * @return
     */
    RegionVO getCurrentUserForEnterprise();
    /**
     * 获取当前用户所在（地区）组织及下级（地区）组织 供维护企业信息使用
     *
     * @return
     */
    RegionVO currentUserRegion();
    /**
     * 获取当前用户所在（地区）组织及下级（地区）组织
     *
     * @return
     */
    RegionVO getCurrentUserRegionsByMessage();

    /**
     * 获取当前用户所在（地区）组织及下级（地区）组织
     *
     * @return
     */
    RegionVO getCurrentUserRegionsByLawEnforcement();

    /**
     * 获取当前用户所在（地区）组织及下级（地区）组织ID
     *
     * @return
     */
    List<Long> getCurrentUserRegionIds();

    /**
     * 条件查询
     *
     * @param region region
     * @return List<Region>
     */
    List<Region> findRegions(Region region);

    /**
     * 查询（所有）
     *
     * @return List<Region>
     */
    List<Region> getRegions();

    /**
     * 根据参数地区ID递归获取子地区列表
     *
     * @param id 父地区ID
     * @return List<Long>
     */
    List<Long> getRegionIds(Long id);

    /**
     * 获取管理局信息
     *
     * @return
     */
    Map<Long, String> getDeptMap();
}
