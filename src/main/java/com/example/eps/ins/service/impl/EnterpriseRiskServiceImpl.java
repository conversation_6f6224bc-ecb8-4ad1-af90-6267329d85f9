package com.example.eps.ins.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EnterpriseRisk;
import com.example.eps.ins.mapper.EnterpriseRiskMapper;
import com.example.eps.ins.service.IEnterpriseRiskService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 企业风险表 Service实现
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:43
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class EnterpriseRiskServiceImpl extends ServiceImpl<EnterpriseRiskMapper, EnterpriseRisk> implements IEnterpriseRiskService {

    private final EnterpriseRiskMapper enterpriseRiskMapper;

    @Override
    public IPage<EnterpriseRisk> findEnterpriseRisks(QueryRequest request, EnterpriseRisk enterpriseRisk) {
        LambdaQueryWrapper<EnterpriseRisk> queryWrapper = new LambdaQueryWrapper<>();
        // TODO 设置查询条件
        Page<EnterpriseRisk> page = new Page<>(request.getPageNum(), request.getPageSize());
        return this.page(page, queryWrapper);
    }

    @Override
    public List<EnterpriseRisk> findEnterpriseRisks(EnterpriseRisk enterpriseRisk) {
        LambdaQueryWrapper<EnterpriseRisk> queryWrapper = new LambdaQueryWrapper<>(enterpriseRisk);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createEnterpriseRisk(EnterpriseRisk enterpriseRisk) {
        this.save(enterpriseRisk);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEnterpriseRisk(EnterpriseRisk enterpriseRisk) {
        this.saveOrUpdate(enterpriseRisk);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEnterpriseRisk(EnterpriseRisk enterpriseRisk) {
        LambdaQueryWrapper<EnterpriseRisk> wapper = new LambdaQueryWrapper<>();
        // TODO 设置删除条件
        this.remove(wapper);
    }
}
