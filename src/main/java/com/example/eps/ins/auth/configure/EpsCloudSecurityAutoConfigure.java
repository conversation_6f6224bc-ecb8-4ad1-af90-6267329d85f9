//package com.example.eps.ins.auth.configure;
//
//import com.example.eps.ins.common.handler.EpsAccessDeniedHandler;
//import com.example.eps.ins.common.handler.EpsAuthExceptionEntryPoint;
//import com.example.eps.ins.common.properties.EpsCloudSecurityProperties;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Primary;
//import org.springframework.http.HttpHeaders;
//import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
//import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
//import org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration;
//import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
//import org.springframework.security.crypto.password.PasswordEncoder;
//import org.springframework.security.oauth2.provider.expression.OAuth2MethodSecurityExpressionHandler;
//import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
//import org.springframework.util.Base64Utils;
//
///**
// * <AUTHOR>
// */
//@EnableGlobalMethodSecurity(prePostEnabled = true)
//@EnableConfigurationProperties(EpsCloudSecurityProperties.class)
//@ConditionalOnProperty(value = "eps.cloud.security.enable", havingValue = "true", matchIfMissing = true)
//public class EpsCloudSecurityAutoConfigure extends GlobalMethodSecurityConfiguration {
//
//    @Bean
//    @ConditionalOnMissingBean(name = "accessDeniedHandler")
//    public EpsAccessDeniedHandler accessDeniedHandler() {
//        return new EpsAccessDeniedHandler();
//    }
//
//    @Bean
//    @ConditionalOnMissingBean(name = "authenticationEntryPoint")
//    public EpsAuthExceptionEntryPoint authenticationEntryPoint() {
//        return new EpsAuthExceptionEntryPoint();
//    }
//
//    @Bean
//    @ConditionalOnMissingBean(value = PasswordEncoder.class)
//    public PasswordEncoder passwordEncoder() {
//        return new BCryptPasswordEncoder();
//    }
//
////    @Bean
////    public EpsCloudSecurityInteceptorConfigure epsCloudSecurityInteceptorConfigure() {
////        return new EpsCloudSecurityInteceptorConfigure();
////    }
//
//    @Bean
//    @Primary
//    @ConditionalOnMissingBean(DefaultTokenServices.class)
//    public EpsUserInfoTokenServices epsUserInfoTokenServices(ResourceServerProperties properties) {
//        return new EpsUserInfoTokenServices(properties.getUserInfoUri(), properties.getClientId());
//    }
//
//
//    @Override
//    protected MethodSecurityExpressionHandler createExpressionHandler() {
//        return new OAuth2MethodSecurityExpressionHandler();
//    }
//}
