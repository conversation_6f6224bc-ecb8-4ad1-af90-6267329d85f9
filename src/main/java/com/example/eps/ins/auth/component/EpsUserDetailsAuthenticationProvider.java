package com.example.eps.ins.auth.component;

import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.system.LoginLog;
import com.example.eps.ins.common.service.RedisService;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.EpsUserDetailsCanProcessor;
import com.example.eps.ins.service.ILoginLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserCache;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.core.userdetails.cache.NullUserCache;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 定义基于手机验证码登录的验证。
 */
public class EpsUserDetailsAuthenticationProvider extends AbstractUserDetailsAuthenticationProvider {
    public static final String APP_TYPE = "app_type";
    private UserDetailsService userDetailsService;
    private RedisService redisService;
    private UserCache userCache = new NullUserCache();
    private LinkedList<UserDetailsService> userDetailsServiceList;
    private boolean forcePrincipalAsString = false;
    private PasswordEncoder passwordEncoder;
    private ILoginLogService loginLogService;

    public List<UserDetailsService> getUserDetailsServiceList() {
        return userDetailsServiceList;
    }

    public ILoginLogService getLoginLogService() {
        return loginLogService;
    }

    public void setLoginLogService(ILoginLogService loginLogService) {
        this.loginLogService = loginLogService;
    }


    public void setUserDetailsServiceList(LinkedList<UserDetailsService> userDetailsServiceList) {
        this.userDetailsServiceList = userDetailsServiceList;
    }

    public PasswordEncoder getPasswordEncoder() {
        return passwordEncoder;
    }

    public void setPasswordEncoder(PasswordEncoder passwordEncoder) {
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    public boolean isForcePrincipalAsString() {
        return forcePrincipalAsString;
    }

    @Override
    public void setForcePrincipalAsString(boolean forcePrincipalAsString) {
        this.forcePrincipalAsString = forcePrincipalAsString;
    }

    public UserDetailsService getUserDetailsService() {
        return userDetailsService;
    }

    public void setUserDetailsService(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }

    public void setRedisService(RedisService redisService) {
        this.redisService = redisService;
    }

    /**
     * 这里进行后台用户，前台用户，手机号验证码登录的判断。
     *
     * @param authentication
     * @return
     * @throws AuthenticationException
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        Assert.isInstanceOf(UsernamePasswordAuthenticationToken.class, authentication,
                () -> messages.getMessage(
                        "AbstractUserDetailsAuthenticationProvider.onlySupports",
                        "Only UsernamePasswordAuthenticationToken is supported"));

        // Determine username
        String username = (authentication.getPrincipal() == null) ? "NONE_PROVIDED"
                : authentication.getName();
        Map details = (Map) authentication.getDetails();
        String appType = (String) details.get(APP_TYPE);
        if ("enterprise_phone".equals(appType)){
            username= details.get("mobile").toString();
        }
        UserDetailTypeEnum userDetailsEnum;
        if (StringUtils.isNotEmpty(appType)) {
            userDetailsEnum = UserDetailTypeEnum.getUserDetailsEnum(appType);
        } else {
            userDetailsEnum = UserDetailTypeEnum.CONSOLE;
        }
        if (userDetailsServiceList == null || userDetailsServiceList.size() == 0) {
            throw new BadCredentialsException(messages.getMessage(
                    "AbstractUserDetailsAuthenticationProvider.badCredentials",
                    "not found UserDetailsService impl..."));
        }
        for (UserDetailsService userDetailsService : userDetailsServiceList) {
            if (userDetailsService instanceof EpsUserDetailsCanProcessor) {
                EpsUserDetailsCanProcessor epsUserDetailsCanProcessor = (EpsUserDetailsCanProcessor) userDetailsService;
                if (epsUserDetailsCanProcessor.canProcessor(userDetailsEnum)) {
                    this.setUserDetailsService(userDetailsService);
                }
            }
        }
        boolean cacheWasUsed = true;
        String userCacheKey = appType + "|" + username;
        UserDetails user = this.userCache.getUserFromCache(userCacheKey);

        if (user == null) {
            cacheWasUsed = false;

            try {
                user = retrieveUser(username,
                        (UsernamePasswordAuthenticationToken) authentication);
//                PasswordEncoder encoder = new BCryptPasswordEncoder();
//                boolean password = encoder.matches(details.get("password").toString(), user.getPassword());
//                if (ObjectUtils.isEmpty(details.get("password"))){
//                    throw new EpsException("密码为空!");
//                }
//                if (!password){
//                    throw new EpsException("密码错误!");
//                }

            } catch (UsernameNotFoundException notFound) {
                logger.debug("User '" + username + "' not found");

                if (hideUserNotFoundExceptions) {
                    throw new BadCredentialsException(messages.getMessage(
                            "AbstractUserDetailsAuthenticationProvider.badCredentials",
                            "Bad credentials"));
                } else {
                    throw notFound;
                }
            }

            Assert.notNull(user,
                    "retrieveUser returned null - a violation of the interface contract");
        }

        if (!cacheWasUsed) {
            this.userCache.putUserInCache(user);
        }
/*        if (this.getUserDetailsService() instanceof IUserAuthChecker) {
            IUserAuthChecker checker = (IUserAuthChecker) this.getUserDetailsService();
            boolean result = checker.check(user, (UsernamePasswordAuthenticationToken) authentication);
            if (!result) {
                logger.error("Authentication failed:");
                throw new BadCredentialsException(messages.getMessage(
                        "AbstractUserDetailsAuthenticationProvider.badCredentials",
                        "Bad credentials"));
            }
        }*/
//        if(userDetailsEnum == UserDetailTypeEnum.CONSOLE){
//            additionalAuthenticationChecks(user,
//                    (UsernamePasswordAuthenticationToken) authentication);
//        }
        additionalAuthenticationChecks(user,
                (UsernamePasswordAuthenticationToken) authentication);
        Object principalToReturn = user;

        if (forcePrincipalAsString) {
            principalToReturn = user.getUsername();
        }
        LoginLog loginLog=new LoginLog();
        loginLog.setLoginTime(new Date());
        if (ObjectUtils.isEmpty(appType)){
          loginLog.setSystem("监管端");
        } else {
          loginLog.setSystem("企业端");
        }
        loginLog.setUsername(username);
        String ip = EpsUtil.getHttpServletRequestIpAddress();
        loginLog.setIp(ip);
        loginLogService.addLoginLog(loginLog);
        Authentication successAuthentication = createSuccessAuthentication(principalToReturn, authentication, user);
        CurrentUser currentUser = EpsUtil.getCurrentUser();
        return successAuthentication;
    }

    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        if (authentication.getCredentials() == null) {
            logger.debug("Authentication failed: no credentials provided");

            throw new BadCredentialsException(messages.getMessage(
                    "AbstractUserDetailsAuthenticationProvider.badCredentials",
                    "Bad credentials"));
        }

        String presentedPassword = authentication.getCredentials().toString();
        if (!passwordEncoder.matches(presentedPassword, userDetails.getPassword())) {
            logger.debug("Authentication failed: password does not match stored value");

            throw new BadCredentialsException(messages.getMessage(
                    "AbstractUserDetailsAuthenticationProvider.badCredentials",
                    "Bad credentials"));
        }
    }

    @Override
    protected UserDetails retrieveUser(String username, UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        try {
            UserDetails loadedUser = this.getUserDetailsService().loadUserByUsername(username);
            if (loadedUser == null) {
                throw new InternalAuthenticationServiceException(
                        "UserDetailsService returned null, which is an interface contract violation");
            }
            return loadedUser;
        } catch (UsernameNotFoundException ex) {
            throw ex;
        } catch (InternalAuthenticationServiceException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex);
        }
    }
}
