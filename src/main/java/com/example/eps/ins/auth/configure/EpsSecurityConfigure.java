package com.example.eps.ins.auth.configure;

import com.example.eps.ins.auth.component.EpsUserDetailsAuthenticationProvider;
import com.example.eps.ins.auth.filter.ValidateCodeFilter;
import com.example.eps.ins.auth.handler.EpsWebLoginFailureHandler;
import com.example.eps.ins.auth.handler.EpsWebLoginSuccessHandler;
import com.example.eps.ins.common.constant.EndpointConstant;
import com.example.eps.ins.common.service.RedisService;
import com.example.eps.ins.service.ILoginLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Bean;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * WebSecurity配置
 *
 * <AUTHOR>
 */
@Order(2)
@EnableWebSecurity
@Configurable
public class EpsSecurityConfigure extends WebSecurityConfigurerAdapter {

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Qualifier("adminUserDetailService")
    @Autowired
    @Lazy
    private UserDetailsService adminUserDetailService;
    @Qualifier("phoneUserDetailsService")
    @Autowired
    @Lazy
    private UserDetailsService phoneUserDetailsService;
    @Autowired
    private ValidateCodeFilter validateCodeFilter;
    @Autowired
    private EpsWebLoginSuccessHandler successHandler;
    @Autowired
    private EpsWebLoginFailureHandler failureHandler;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ILoginLogService loginLogService;

    /**
     * 设置框架中使用两个用户数据源进行用户登录认证.
     *
     * @return
     * @throws Exception
     */
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() {
        EpsUserDetailsAuthenticationProvider provider = new EpsUserDetailsAuthenticationProvider();
        LinkedList<UserDetailsService> userDetailsServices = new LinkedList();
        userDetailsServices.add(adminUserDetailService);
        userDetailsServices.add(phoneUserDetailsService);
        provider.setUserDetailsServiceList(userDetailsServices);
        provider.setRedisService(redisService);
        provider.setLoginLogService(loginLogService);
        provider.setPasswordEncoder(passwordEncoder());
        List<AuthenticationProvider> list = new ArrayList<>();
        list.add(provider);
        return new ProviderManager(list);
    }


    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.addFilterBefore(validateCodeFilter, UsernamePasswordAuthenticationFilter.class)
                .requestMatchers()
                .antMatchers(EndpointConstant.OAUTH_ALL, EndpointConstant.LOGIN)
                .and()
                .authorizeRequests()
                .antMatchers(EndpointConstant.OAUTH_ALL).authenticated()
                .and()
                .formLogin()
                .loginPage(EndpointConstant.LOGIN)
                .loginProcessingUrl(EndpointConstant.LOGIN)
                .successHandler(successHandler)
                .failureHandler(failureHandler)
                .permitAll()
                .and().csrf().disable()
                .httpBasic().disable();
    }

//    @Override
//    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
//        auth.userDetailsService(adminUserDetailService).passwordEncoder(passwordEncoder);
//    }
}
