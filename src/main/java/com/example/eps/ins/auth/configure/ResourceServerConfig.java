package com.example.eps.ins.auth.configure;

import com.example.eps.ins.common.constant.EndpointConstant;
import com.example.eps.ins.common.constant.StringConstant;
import com.example.eps.ins.common.properties.EpsCloudSecurityProperties;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;

import javax.annotation.Resource;

@Configuration
@EnableAutoConfiguration(exclude = UserDetailsServiceAutoConfiguration.class)
@EnableResourceServer // 关键注解
public class ResourceServerConfig extends ResourceServerConfigurerAdapter {
    @Resource
    private EpsCloudSecurityProperties properties;
//    @Override
//    public void configure(HttpSecurity http) throws Exception {
//        http
//                .authorizeRequests()
//                // 配置无需认证的接口
//                .antMatchers(
//                        "/actuator/**",
//                        "/auth/captcha",
//                        "/smsCaptcha",
//                        "/social/**",
//                        "/v2/api-docs",
//                        "/v2/api-docs-ext",
//                        "/login",
//                        "/resource/**",
//                        "/validateCode",
//                        "/enterpriseUser/check/**").permitAll()
//                // 默认所有其他接口需要认证
//                .anyRequest().authenticated();
//    }
    @Override
    public void configure(HttpSecurity http) throws Exception {
        if (properties == null) {
            permitAll(http);
            return;
        }
        String[] anonUrls = StringUtils.splitByWholeSeparatorPreserveAllTokens(properties.getAnonUris(), StringConstant.COMMA);
        if (ArrayUtils.isEmpty(anonUrls)) {
            anonUrls = new String[]{};
        }
        if (ArrayUtils.contains(anonUrls, EndpointConstant.ALL)) {
            permitAll(http);
            return;
        }
        http.csrf().disable()
                .requestMatchers().antMatchers(properties.getAuthUri())
                .and()
                .authorizeRequests()
                .antMatchers(anonUrls).permitAll()
                .antMatchers(properties.getAuthUri()).authenticated()
                .and()
                .httpBasic();
    }
    private void permitAll(HttpSecurity http) throws Exception {
        http.csrf().disable();
        http.authorizeRequests().anyRequest().permitAll();
    }
}