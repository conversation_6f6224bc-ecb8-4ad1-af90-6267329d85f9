package com.example.eps.ins.auth.component;

public enum UserDetailTypeEnum {
    ENTERPRISE_USER("enterprise_user"),
    ENTERPRISE_PHONE("enterprise_phone"),
    CONSOLE("console");
    private final String value;

    UserDetailTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
    /**
     * Get related class name.
     *
     * @param value The action.
     */
    public static UserDetailTypeEnum getUserDetailsEnum(String value) {
        for (UserDetailTypeEnum userDetailTypeEnum : UserDetailTypeEnum.values()) {
            if (userDetailTypeEnum.getValue().equals(value)) {
                return userDetailTypeEnum;
            }
        }
        return null;
    }
}
