package com.example.eps.ins.auth.component;

import com.example.eps.ins.common.entity.EpsAuthUser;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;

import java.util.HashMap;
import java.util.Map;

public class EpsTokenEnhancer implements TokenEnhancer {
    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        Map<String, Object> map = new HashMap<>();
        if (authentication.getPrincipal() instanceof EpsAuthUser) {
            EpsAuthUser user = (EpsAuthUser) authentication.getPrincipal();
            if (user.getLastLoginTime() == null) {
                map.put("first", "true");
            }
        }
        if (accessToken instanceof DefaultOAuth2AccessToken) {
            ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(map);

        }
        return accessToken;
    }
}
