package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.dto.report.model.*;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.EnterPriseReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/** @Author: Zhanghongyin @Date: Created in 2022/10/11 9:46 @Version: 1.0 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/enterpriseReport")
@RequiredArgsConstructor
public class EnterpriseReportSystemController {

  private final EnterPriseReportService enterpriseReportService;

  @PostMapping("presentSituation")
  public EpsResponse presentSituationList( @RequestBody PresentSituationVo emergencyPlan) {
    Map<String, Object> dataTable =
        EpsUtil.getDataTable(
            this.enterpriseReportService.findPresentSituationLists( emergencyPlan));
    return new EpsResponse().data(dataTable);
  }

  @PostMapping("complexWork")
  public EpsResponse complexWorkList( @RequestBody ComplexWorkVo outsourceWork) {
    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findComplexWork(
            outsourceWork));
    return new EpsResponse().data(dataTable);
  }

  @PostMapping("emergencyDrill")
  public EpsResponse emergencyDrillList(@RequestBody EmergencydrillVO emergencyPlan) {
    Map<String, Object> dataTable =
            EpsUtil.getDataTable(
                    this.enterpriseReportService.findEmergencyDrills( emergencyPlan));
    return new EpsResponse().data(dataTable);
  }

  @PostMapping("emergencyPlan")
  public EpsResponse emergencyPlanList(@RequestBody EmergencyPlanVO emergencyPlan) {
    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findEmergencyPlans(
            emergencyPlan));
    return new EpsResponse().data(dataTable);
  }
  /**
   * 设施设备维护

   * @param equipmentMaintain
   * @return
   */
  @PostMapping("equipmentMaintain")
  public EpsResponse equipmentMaintainList(@RequestBody EquipmentMaintainVO equipmentMaintain) {
    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findEquipmentMaintains(
            equipmentMaintain));
    return new EpsResponse().data(dataTable);
  }
  /**
   * 有限空间作业许可证

   * @param finiteSpaceWork
   * @return
   */
  @PostMapping("finiteSpaceWork")
  public EpsResponse finiteSpaceWorkList(@RequestBody FiniteSpaceWorkVO finiteSpaceWork) {
    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findFiniteSpaceWorks(
            finiteSpaceWork));
    return new EpsResponse().data(dataTable);
  }
//  /**
//   * 隐患治理
//
//   * @param hiddenDangerList
//   * @return
//   */
//  @PostMapping("hiddenDangerList")
//  public EpsResponse hiddenDangerList(@RequestBody HiddenDangerList hiddenDangerList) {
//    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findHiddenDangerList(
//            hiddenDangerList));
//    return new EpsResponse().data(dataTable);
//  }
  /**
   * 隐患排查制度维护

   * @param hiddenDanger
   * @return
   */
  @PostMapping("hiddenDanger")
  public EpsResponse hiddenDangerList(@RequestBody HiddenDangerVo hiddenDanger) {
    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findHiddenDanger(
            hiddenDanger));
    return new EpsResponse().data(dataTable);
  }
  /**
   * 外委作业

   * @param outsourceWork
   * @return
   */
  @PostMapping("outsourceWork")
  public EpsResponse emergencyPlanList(@RequestBody OutsourceWorkVO outsourceWork) {
    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findOutsourceWork(
            outsourceWork));
    return new EpsResponse().data(dataTable);
  }
  /**
   * 特种作业许可证

   * @param specialCertificate
   * @return
   */
  @PostMapping("specialCertificate")
  public EpsResponse emergencyPlanList(@RequestBody SpecialCertificateVo specialCertificate) {
    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findSpecialCertificates(
            specialCertificate));
    return new EpsResponse().data(dataTable);
  }
  /**
   * 特种人员

   * @param specialPeople
   * @return
   */
  @PostMapping("specialPeople")
  public EpsResponse emergencyPlanList(@RequestBody SpecialPeopleVo specialPeople) {
    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findSpecialPeoples(
            specialPeople));
    return new EpsResponse().data(dataTable);
  }

  /**
   * 培训教育

   * @param trainEducation
   * @return
   */
  @PostMapping("trainEducation")
  public EpsResponse emergencyPlanList(@RequestBody TrainEducationVo trainEducation) {
    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findTrainEducations(
            trainEducation));
    return new EpsResponse().data(dataTable);
  }
//  /**
//   * 两单两卡预警
//
//   * @param warningForCard
//   * @return
//   */
//  @PostMapping("warningForCard")
//  public EpsResponse warningForCardList(@RequestBody WarningForCard warningForCard) {
//    Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseReportService.findWarningForCards(
//            warningForCard));
//    return new EpsResponse().data(dataTable);
//  }
}
