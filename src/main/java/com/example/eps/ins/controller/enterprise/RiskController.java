package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Risk;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IRiskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 风险类型表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/risk")
@RequiredArgsConstructor
public class RiskController {

    private final IRiskService riskService;

    @GetMapping("list")
    public EpsResponse riskList(QueryRequest request, Risk risk) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.riskService.findRisks(request, risk));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping
    public void addRisk(@Valid Risk risk) throws EpsException {
        try {
            this.riskService.createRisk(risk);
        } catch (Exception e) {
            String message = "新增Risk失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping
    public void deleteRisk(Risk risk) throws EpsException {
        try {
            this.riskService.deleteRisk(risk);
        } catch (Exception e) {
            String message = "删除Risk失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
    public void updateRisk(Risk risk) throws EpsException {
        try {
            this.riskService.updateRisk(risk);
        } catch (Exception e) {
            String message = "修改Risk失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}
