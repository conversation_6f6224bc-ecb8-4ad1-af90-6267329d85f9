package com.example.eps.ins.controller.riskPush;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.eps.ins.common.dto.riskPush.AlarmQueryRequest;
import com.example.eps.ins.common.dto.riskPush.AlarmQueryResponse;
import com.example.eps.ins.common.dto.riskPush.CameraResponse;
import com.example.eps.ins.common.dto.riskPush.CityEventObject;
import com.example.eps.ins.common.dto.riskPush.CityEventWrapper;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.safety.SafetyMonitorEvent;
import com.example.eps.ins.service.AlarmNotificationService;
import com.example.eps.ins.service.ISubDeviceService;
import com.example.eps.ins.service.SafetyMonitorEventService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/risk/push")
@Slf4j
@RequiredArgsConstructor
public class RiskPushController {
    private static final ObjectMapper MAPPER = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    private final SafetyMonitorEventService safetyMonitorEventService;
    private final AlarmNotificationService alarmNotificationService;
    private final ISubDeviceService subDeviceService;
    @PostMapping(value = "/message", consumes = "application/json")
    public Map<String, Object> handleSafetyHelmetEvent(
            @RequestBody String jsonPayload,
            @RequestHeader(value = "X-Request-Time", required = false) String requestTime) {

        log.info("接收到安全监控事件推送，请求时间: {}", requestTime);

        try {
            // 1. 解析JSON数据
            JsonNode rootNode = MAPPER.readTree(jsonPayload);

            // 2. 提取设备信息
            Map<String, Object> deviceInfo = extractDeviceInfo(rootNode);
            log.info("设备信息: {}", deviceInfo);

            // 3. 解析事件内容
            JsonNode contentNode = rootNode.path("content");
            if (!contentNode.isMissingNode()) {
                // content字段是一个JSON字符串，需要先解析
                String contentStr = contentNode.asText();
                if (contentStr != null && !contentStr.isEmpty()) {
                    JsonNode contentJsonNode = MAPPER.readTree(contentStr);
                    String contentType = contentJsonNode.path("type").asText();
                    log.info("事件类型: {}", contentType);

                    if ("warn".equals(contentType)) {
                        // 4. 处理告警事件
                        processWarningEvent(contentJsonNode, deviceInfo, jsonPayload);
                    }
                }
            }

            // 5. 构建成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("msg", "success");
            response.put("timestamp", System.currentTimeMillis());

            return response;

        } catch (Exception e) {
            log.error("处理安全监控事件异常", e);

            // 6. 构建错误响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", "处理失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());

            return response;
        }
    }
    /**
     * 提取设备信息
     */
    private Map<String, Object> extractDeviceInfo(JsonNode rootNode) {
        Map<String, Object> deviceInfo = new HashMap<>();
        deviceInfo.put("deviceId", rootNode.path("deviceId").asLong());
        deviceInfo.put("deviceType", rootNode.path("deviceType").asText());
        deviceInfo.put("ip", rootNode.path("ip").asText());
        deviceInfo.put("serial", rootNode.path("serial").asText());
        deviceInfo.put("status", rootNode.path("status").asText());
        return deviceInfo;
    }

    /**
     * 处理告警事件
     */
    private void processWarningEvent(JsonNode contentJsonNode, Map<String, Object> deviceInfo, String rawJsonData) {
        try {
            // contentJsonNode现在已经是解析后的JSON对象了
            JsonNode paramsNode = contentJsonNode.path("params");

            if (!paramsNode.isMissingNode()) {
                // params字段也可能是字符串，需要检查
                if (paramsNode.isTextual()) {
                    // params是JSON字符串，需要解析
                    String paramsStr = paramsNode.asText();
                    if (paramsStr != null && !paramsStr.isEmpty()) {
                        JsonNode parsedParamsNode = MAPPER.readTree(paramsStr);
                        processEventParams(parsedParamsNode, deviceInfo, rawJsonData);
                    }
                } else {
                    // params已经是JSON对象
                    processEventParams(paramsNode, deviceInfo, rawJsonData);
                }
            }
        } catch (Exception e) {
            log.error("处理告警事件异常", e);
        }
    }

    /**
     * 处理事件参数
     */
    private void processEventParams(JsonNode paramsNode, Map<String, Object> deviceInfo, String rawJsonData) {
        try {
            JsonNode cityEventListNode = paramsNode.path("CityEventListObject");

            if (!cityEventListNode.isMissingNode()) {
                JsonNode cityEventArrayNode = cityEventListNode.path("CityEventObject");

                if (cityEventArrayNode.isArray()) {
                    // 处理每个事件
                    for (JsonNode eventNode : cityEventArrayNode) {
                        processIndividualEvent(eventNode, deviceInfo, rawJsonData);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理事件参数异常", e);
        }
    }

    /**
     * 处理单个事件
     */
    private void processIndividualEvent(JsonNode eventNode, Map<String, Object> deviceInfo, String rawJsonData) {
        try {
            // 将JsonNode转换为CityEventObject
            CityEventObject eventObject = MAPPER.treeToValue(eventNode, CityEventObject.class);

            log.info("处理事件: ID={}, 类型={}, 告警={}",
                    eventObject.getId(), eventObject.getEventType(), eventObject.getHaveAlarm());

            // 1. 保存事件到数据库
            SafetyMonitorEvent savedEvent = safetyMonitorEventService.saveSafetyEvent(eventObject, deviceInfo, rawJsonData);

            // 2. 根据事件类型进行不同处理
            handleEventByType(savedEvent, eventObject);

//            // 3. 如果有告警，发送通知
//            if (eventObject.getHaveAlarm() != null && eventObject.getHaveAlarm() == 1) {
//                sendAlarmNotification(savedEvent);
//            }

            // 4. 更新处理状态
            safetyMonitorEventService.processEvent(savedEvent.getId(), "事件处理完成");

        } catch (Exception e) {
            log.error("处理单个事件异常", e);
        }
    }

    /**
     * 根据事件类型进行不同处理
     */
    private void handleEventByType(SafetyMonitorEvent savedEvent, CityEventObject eventObject) {
        Integer eventType = eventObject.getEventType();

        if (eventType == null) {
            log.warn("事件类型为空，跳过处理: {}", eventObject.getId());
            return;
        }

        switch (eventType) {
            case 121:
                log.info("处理安全帽检测事件: {}", eventObject.getId());
                handleSafetyHelmetDetection(savedEvent, eventObject);
                break;
            case 122:
                log.info("处理反光衣检测事件: {}", eventObject.getId());
                handleReflectiveVestDetection(savedEvent, eventObject);
                break;
            case 123:
                log.info("处理人员入侵检测事件: {}", eventObject.getId());
                handlePersonIntrusionDetection(savedEvent, eventObject);
                break;
            default:
                log.info("处理通用安全事件，类型: {}", eventType);
                handleGenericSafetyEvent(savedEvent, eventObject);
                break;
        }
    }

    /**
     * 处理安全帽检测事件
     */
    private void handleSafetyHelmetDetection(SafetyMonitorEvent savedEvent, CityEventObject eventObject) {
        log.info("=== 安全帽检测事件处理 ===");
        log.info("事件ID: {}", eventObject.getId());
        log.info("设备序列号: {}", eventObject.getSerialNumber());
        log.info("摄像头ID: {}", eventObject.getCameraId());
        log.info("置信度: {}", eventObject.getConfidence());

        // 判断是否需要告警
        if (eventObject.getHaveAlarm() != null && eventObject.getHaveAlarm() == 1) {
            log.warn("⚠️ 安全帽违规告警！");

            // 获取告警详情
            if (eventObject.getExtInfo() != null &&
                eventObject.getExtInfo().getDataInfo() != null &&
                eventObject.getExtInfo().getDataInfo().getOptParam() != null) {
                Integer alarmTimes = eventObject.getExtInfo().getDataInfo().getOptParam().getAlarmTimes();
                String objectName = eventObject.getExtInfo().getDataInfo().getOptParam().getObjectName();
                log.warn("累计告警次数: {}, 对象名称: {}", alarmTimes, objectName);
            }
        } else {
            log.info("✅ 安全帽佩戴正常");
        }

        log.info("安全帽检测事件处理完成");
    }

    /**
     * 处理反光衣检测事件
     */
    private void handleReflectiveVestDetection(SafetyMonitorEvent savedEvent, CityEventObject eventObject) {
        log.info("=== 反光衣检测事件处理 ===");
        log.info("事件ID: {}", eventObject.getId());

        // 判断是否需要告警
        if (eventObject.getHaveAlarm() != null && eventObject.getHaveAlarm() == 1) {
            log.warn("⚠️ 反光衣违规告警！");
        } else {
            log.info("✅ 反光衣穿着正常");
        }

        log.info("反光衣检测事件处理完成");
    }

    /**
     * 处理人员入侵检测事件
     */
    private void handlePersonIntrusionDetection(SafetyMonitorEvent savedEvent, CityEventObject eventObject) {
        log.info("=== 人员入侵检测事件处理 ===");
        log.info("事件ID: {}", eventObject.getId());

        // 判断是否需要告警
        if (eventObject.getHaveAlarm() != null && eventObject.getHaveAlarm() == 1) {
            log.warn("⚠️ 人员入侵告警！");
        } else {
            log.info("✅ 区域安全正常");
        }

        log.info("人员入侵检测事件处理完成");
    }

    /**
     * 处理通用安全事件
     */
    private void handleGenericSafetyEvent(SafetyMonitorEvent savedEvent, CityEventObject eventObject) {
        log.info("=== 通用安全事件处理 ===");
        log.info("事件ID: {}", eventObject.getId());
        log.info("事件类型: {}", eventObject.getEventType());

        // 判断是否需要告警
        if (eventObject.getHaveAlarm() != null && eventObject.getHaveAlarm() == 1) {
            log.warn("⚠️ 安全事件告警！");
        } else {
            log.info("✅ 安全状态正常");
        }

        log.info("通用安全事件处理完成");
    }

    /**
     * 发送告警通知
     */
    private void sendAlarmNotification(SafetyMonitorEvent savedEvent) {
        try {
            Integer eventType = savedEvent.getEventType();
            boolean notificationSent = false;

            if (eventType != null) {
                switch (eventType) {
                    case 121:
                        notificationSent = alarmNotificationService.sendSafetyHelmetAlarm(savedEvent);
                        break;
                    case 122:
                        notificationSent = alarmNotificationService.sendReflectiveVestAlarm(savedEvent);
                        break;
                    case 123:
                        notificationSent = alarmNotificationService.sendPersonIntrusionAlarm(savedEvent);
                        break;
                    default:
                        notificationSent = alarmNotificationService.sendGenericSafetyAlarm(savedEvent);
                        break;
                }
            }

            if (notificationSent) {
                // 更新告警状态
                safetyMonitorEventService.updateAlarmStatus(savedEvent.getId(), 1);
                log.info("告警通知发送成功: {}", savedEvent.getExternalEventId());
            } else {
                log.error("告警通知发送失败: {}", savedEvent.getExternalEventId());
            }

        } catch (Exception e) {
            log.error("发送告警通知异常: {}", savedEvent.getExternalEventId(), e);
        }
    }

    /**
     * 测试JSON解析方法（用于调试）
     */
    @PostMapping(value = "/test-parse", consumes = "application/json")
    public Map<String, Object> testJsonParse(@RequestBody String jsonPayload) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("接收到测试JSON: {}", jsonPayload);

            // 1. 解析根节点
            JsonNode rootNode = MAPPER.readTree(jsonPayload);
            log.info("根节点解析成功");

            // 2. 提取设备信息
            Map<String, Object> deviceInfo = extractDeviceInfo(rootNode);
            log.info("设备信息: {}", deviceInfo);

            // 3. 解析content字段
            JsonNode contentNode = rootNode.path("content");
            if (!contentNode.isMissingNode()) {
                String contentStr = contentNode.asText();
                log.info("Content字符串: {}", contentStr);

                if (contentStr != null && !contentStr.isEmpty()) {
                    JsonNode contentJsonNode = MAPPER.readTree(contentStr);
                    String contentType = contentJsonNode.path("type").asText();
                    log.info("Content类型: {}", contentType);

                    // 4. 解析params
                    JsonNode paramsNode = contentJsonNode.path("params");
                    if (!paramsNode.isMissingNode()) {
                        if (paramsNode.isTextual()) {
                            String paramsStr = paramsNode.asText();
                            log.info("Params字符串: {}", paramsStr);

                            JsonNode parsedParamsNode = MAPPER.readTree(paramsStr);
                            JsonNode cityEventListNode = parsedParamsNode.path("CityEventListObject");

                            if (!cityEventListNode.isMissingNode()) {
                                JsonNode cityEventArrayNode = cityEventListNode.path("CityEventObject");
                                if (cityEventArrayNode.isArray()) {
                                    log.info("找到 {} 个事件", cityEventArrayNode.size());

                                    for (int i = 0; i < cityEventArrayNode.size(); i++) {
                                        JsonNode eventNode = cityEventArrayNode.get(i);
                                        String eventId = eventNode.path("Id").asText();
                                        Integer eventType = eventNode.path("EventType").asInt();
                                        Integer haveAlarm = eventNode.path("HaveAlarm").asInt();

                                        log.info("事件 {}: ID={}, 类型={}, 告警={}",
                                                i + 1, eventId, eventType, haveAlarm);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            result.put("code", 200);
            result.put("msg", "解析成功");
            result.put("deviceInfo", deviceInfo);

        } catch (Exception e) {
            log.error("JSON解析测试失败", e);
            result.put("code", 500);
            result.put("msg", "解析失败: " + e.getMessage());
        }

        return result;
    }

    private String formatTime(String timeStr) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                    .format(new SimpleDateFormat("yyyyMMddHHmmss").parse(timeStr));
        } catch (Exception e) {
            return timeStr; // 原始格式返回
        }
    }

    /**
     * 查询预警列表
     * 支持根据企业名称、事件类型、告警时间、子设备查询预警数据
     *
     * @param request 查询请求参数
     * @return 预警列表分页数据
     */
    @PostMapping("/alarms")
    public EpsResponse getAlarmList(@RequestBody AlarmQueryRequest request) {
        log.info("开始查询预警列表，查询条件: {}", request);

        try {
            // 设置默认分页参数
            if (request.getPageNum() == null || request.getPageNum() <= 0) {
                request.setPageNum(1);
            }
            if (request.getPageSize() == null || request.getPageSize() <= 0) {
                request.setPageSize(10);
            }

            // 调用服务查询预警列表
            IPage<AlarmQueryResponse> alarmPage = safetyMonitorEventService.getAlarmPageWithEnterpriseInfo(request);

            return new EpsResponse()
                    .put("success", true)
                    .put("total", alarmPage.getTotal())
                    .put("current", alarmPage.getCurrent())
                    .put("size", alarmPage.getSize())
                    .put("pages", alarmPage.getPages())
                    .data(alarmPage.getRecords())
                    .message("预警列表查询成功");

        } catch (Exception e) {
            log.error("查询预警列表失败", e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询预警列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据事件类型查询预警统计
     *
     * @param eventTypes 事件类型列表
     * @param alarmStartTime 开始时间
     * @param alarmEndTime 结束时间
     * @return 预警统计数据
     */
    @GetMapping("/alarms/statistics")
    public EpsResponse getAlarmStatistics(
            @RequestParam(required = false) String[] eventTypes,
            @RequestParam(required = false) String alarmStartTime,
            @RequestParam(required = false) String alarmEndTime) {

        log.info("开始查询预警统计，事件类型: {}, 时间范围: {} - {}",
                eventTypes, alarmStartTime, alarmEndTime);

        try {
            AlarmQueryRequest request = new AlarmQueryRequest();
            request.setPageNum(1);
            request.setPageSize(Integer.MAX_VALUE); // 查询所有数据用于统计

            // 设置事件类型
            if (eventTypes != null && eventTypes.length > 0) {
                java.util.List<Integer> eventTypeList = new java.util.ArrayList<>();
                for (String eventType : eventTypes) {
                    try {
                        eventTypeList.add(Integer.parseInt(eventType));
                    } catch (NumberFormatException e) {
                        log.warn("无效的事件类型: {}", eventType);
                    }
                }
                request.setEventTypes(eventTypeList);
            }

            // 设置时间范围
            if (alarmStartTime != null && !alarmStartTime.isEmpty()) {
                try {
                    request.setAlarmStartTime(alarmStartTime);
                } catch (Exception e) {
                    log.warn("无效的开始时间格式: {}", alarmStartTime);
                }
            }

            if (alarmEndTime != null && !alarmEndTime.isEmpty()) {
                try {
                    request.setAlarmEndTime(alarmEndTime);
                } catch (Exception e) {
                    log.warn("无效的结束时间格式: {}", alarmEndTime);
                }
            }

            // 查询数据
            IPage<AlarmQueryResponse> alarmPage = safetyMonitorEventService.getAlarmPageWithEnterpriseInfo(request);

            // 统计数据
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", alarmPage.getTotal());

            // 按事件类型统计
            Map<Integer, Long> eventTypeCount = alarmPage.getRecords().stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            AlarmQueryResponse::getEventType,
                            java.util.stream.Collectors.counting()));
            statistics.put("eventTypeCount", eventTypeCount);

            // 按告警状态统计
            Map<Integer, Long> alarmStatusCount = alarmPage.getRecords().stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            AlarmQueryResponse::getHaveAlarm,
                            java.util.stream.Collectors.counting()));
            statistics.put("alarmStatusCount", alarmStatusCount);

            // 按处理状态统计
            Map<Integer, Long> processStatusCount = alarmPage.getRecords().stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            AlarmQueryResponse::getProcessStatus,
                            java.util.stream.Collectors.counting()));
            statistics.put("processStatusCount", processStatusCount);

            return new EpsResponse()
                    .put("success", true)
                    .data(statistics)
                    .message("预警统计查询成功");

        } catch (Exception e) {
            log.error("查询预警统计失败", e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询预警统计失败: " + e.getMessage());
        }
    }

    /**
     * 根据企业ID获取相机列表
     *
     * @param enterpriseId 企业ID
     * @return 相机列表
     */
    @GetMapping("/cameras/{enterpriseId}")
    public EpsResponse getCamerasByEnterpriseId(@PathVariable Long enterpriseId) {
        log.info("开始查询企业ID: {} 的相机列表", enterpriseId);

        try {
            // 参数验证
            if (enterpriseId == null || enterpriseId <= 0) {
                return new EpsResponse()
                        .put("success", false)
                        .message("企业ID不能为空且必须大于0");
            }

            // 查询企业的相机列表
            List<CameraResponse> cameras = subDeviceService.findCamerasByEnterpriseId(enterpriseId);

            // 统计相机状态
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", cameras.size());

            // 按相机状态统计
            Map<String, Long> statusCount = cameras.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            camera -> camera.getCameraStatus() != null ? camera.getCameraStatus() : "UNKNOWN",
                            java.util.stream.Collectors.counting()));
            statistics.put("statusCount", statusCount);

            // 按同步状态统计
            Map<Integer, Long> syncStatusCount = cameras.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            camera -> camera.getSyncStatus() != null ? camera.getSyncStatus() : -1,
                            java.util.stream.Collectors.counting()));
            statistics.put("syncStatusCount", syncStatusCount);

            return new EpsResponse()
                    .put("success", true)
                    .put("enterpriseId", enterpriseId)
                    .put("totalCount", cameras.size())
                    .put("statistics", statistics)
                    .data(cameras)
                    .message("企业相机列表查询成功");

        } catch (Exception e) {
            log.error("查询企业ID: {} 的相机列表失败", enterpriseId, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询企业相机列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据企业ID获取在线相机列表
     *
     * @param enterpriseId 企业ID
     * @return 在线相机列表
     */
    @GetMapping("/cameras/{enterpriseId}/online")
    public EpsResponse getOnlineCamerasByEnterpriseId(@PathVariable Long enterpriseId) {
        log.info("开始查询企业ID: {} 的在线相机列表", enterpriseId);

        try {
            // 参数验证
            if (enterpriseId == null || enterpriseId <= 0) {
                return new EpsResponse()
                        .put("success", false)
                        .message("企业ID不能为空且必须大于0");
            }

            // 查询企业的所有相机列表
            List<CameraResponse> allCameras = subDeviceService.findCamerasByEnterpriseId(enterpriseId);

            // 过滤出在线的相机
            List<CameraResponse> onlineCameras = allCameras.stream()
                    .filter(camera -> "ONLINE".equalsIgnoreCase(camera.getCameraStatus()))
                    .collect(java.util.stream.Collectors.toList());

            return new EpsResponse()
                    .put("success", true)
                    .put("enterpriseId", enterpriseId)
                    .put("totalCount", allCameras.size())
                    .put("onlineCount", onlineCameras.size())
                    .put("offlineCount", allCameras.size() - onlineCameras.size())
                    .data(onlineCameras)
                    .message("企业在线相机列表查询成功");

        } catch (Exception e) {
            log.error("查询企业ID: {} 的在线相机列表失败", enterpriseId, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询企业在线相机列表失败: " + e.getMessage());
        }
    }
}
