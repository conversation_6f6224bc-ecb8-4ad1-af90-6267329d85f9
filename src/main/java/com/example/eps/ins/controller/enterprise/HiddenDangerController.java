package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.dto.report.model.HiddenDangerVo;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.HiddenDanger;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.HiddenDangerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 企业隐患排查制度维护
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 9:46
 * @Version: 1.0 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/hiddenDanger")
@RequiredArgsConstructor
public class HiddenDangerController {
    private final HiddenDangerService hiddenDangerService;

    @GetMapping("list")
    public EpsResponse hiddenDangerList(QueryRequest request, HiddenDanger emergencyPlan) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.hiddenDangerService.findHiddenDanger(request,
                emergencyPlan));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping("add")
    public void addHiddenDanger( HiddenDangerVo dto) throws EpsException {
        try {
            this.hiddenDangerService.createHiddenDanger(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }
    @DeleteMapping("{id}")
    public void deleteHiddenDanger(@NotNull @PathVariable Long id) throws EpsException {
        try {
            this.hiddenDangerService.deleteHiddenDanger(id);
        } catch (Exception e) {
            String message = "删除应急预案失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
    @DeleteMapping(value = "delete")
    @ResponseBody
    public void deleteFile(Long id,String filePath) {
        try {
            this.hiddenDangerService.deleteFile(id,filePath);
        } catch (Exception e) {
            String message = "删除文件失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    @GetMapping("{id}")
    public EpsResponse queryHiddenDanger(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(hiddenDangerService.findHiddenDanger(id));
    }
}
