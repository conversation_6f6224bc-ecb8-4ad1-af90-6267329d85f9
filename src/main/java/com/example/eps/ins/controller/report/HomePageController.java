package com.example.eps.ins.controller.report;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.service.HomePageService;
import com.example.eps.ins.service.NumberUnitsService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2021/12/14 15:09
 * @Version: 1.0
 */
@Api(value = "dateAnalysis",description = "首页统计接口")
@Slf4j
@RestController
@RequestMapping("/report/home")
@RequiredArgsConstructor
public class HomePageController {

    private final HomePageService homePageService;
    private final NumberUnitsService numberUnitsService;

    /**
     * 首页单位总览
     * @return
     */
    @PostMapping("getList")
    public EpsResponse getList(){
        return new EpsResponse().data(homePageService.getList());
    }
    /**
     * 首页事故统计
     */
    @PostMapping("year")
    public EpsResponse getAll(){
        return new EpsResponse().data(homePageService.getYear());
    }

    /**
     * 首页执法统计
     */
    @PostMapping("lawEnforcement")
    public EpsResponse getLawEnforcement(){
        return new EpsResponse().data(homePageService.getLawEnforcement());
    }

    /**
     * 首页（大屏）风险统计
     */
    @PostMapping("risk")
    public EpsResponse getRisk(){
        return new EpsResponse().data(numberUnitsService.riskList(null));
    }

    /**
     * 首页（大屏）行业分布
     */
    @PostMapping("getIndustry")
    public EpsResponse getIndustry(){
        return new EpsResponse().data(homePageService.getIndustry());
    }

    /**
     *首页（大屏）事故类型排序
     */
    @PostMapping("sort")
    public EpsResponse getSort(){
        return new EpsResponse().data(homePageService.getSort());
    }
}
