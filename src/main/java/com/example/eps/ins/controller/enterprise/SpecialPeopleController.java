package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.SpecialPeople;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.SpecialPeopleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 *
 * 特种人员持证上岗情况
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 9:46
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/specialPeople")
@RequiredArgsConstructor
public class SpecialPeopleController {
    private final SpecialPeopleService specialPeopleService;

    @GetMapping("list")
    public EpsResponse specialPeopleList(QueryRequest request, SpecialPeople specialPeople) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.specialPeopleService.findSpecialPeoples(request,
                specialPeople));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping("add")
    public void addSpecialPeople(SpecialPeople dto) throws EpsException {
        try {
            this.specialPeopleService.createSpecialPeople(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }
    @DeleteMapping("{id}")
    public void deleteSpecialPeople(@NotNull @PathVariable Long id) throws EpsException {
        try {
            this.specialPeopleService.deleteSpecialPeople(id);
        } catch (Exception e) {
            String message = "删除应急预案失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
//    /**
//     * 删除文件
//     *
//     * @param filePath 文件路径
//     */
//    @DeleteMapping(value = "delete")
//    @ResponseBody
//    public void deleteFile(Long id,String filePath) {
//        try {
//            this.specialPeopleService.deleteFile(id,filePath);
//        } catch (Exception e) {
//            String message = "删除文件失败";
//            log.error(message, e);
//            throw new EpsException(message);
//        }
//    }
    @GetMapping("{id}")
    public EpsResponse querySpecialPeople(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(specialPeopleService.findSpecialPeople(id));
    }
}
