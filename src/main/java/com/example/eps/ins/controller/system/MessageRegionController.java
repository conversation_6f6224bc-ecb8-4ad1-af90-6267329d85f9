package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.MessageRegion;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IMessageRegionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * @Description: <消息地区表前端控制器>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/MessageRegion")
@RequiredArgsConstructor
public class MessageRegionController {

    private final IMessageRegionService iMessageRegionService;

    @GetMapping("list")
    public EpsResponse MessageRegionList(QueryRequest request, MessageRegion dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.iMessageRegionService.findMessageRegions(request, dto));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping
    public void addMessageRegion(@Valid MessageRegion dto) throws EpsException {
        try {
            this.iMessageRegionService.createMessageRegion(dto);
        } catch (Exception e) {
            String message = "新增MessageRegion失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping
    public void deleteMessageRegion(MessageRegion dto) throws EpsException {
        try {
            this.iMessageRegionService.deleteMessageRegion(dto);
        } catch (Exception e) {
            String message = "删除MessageRegion失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
    public void updateMessageRegion(MessageRegion dto) throws EpsException {
        try {
            this.iMessageRegionService.updateMessageRegion(dto);
        } catch (Exception e) {
            String message = "修改MessageRegion失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}