package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.FiniteSpaceWork;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.FiniteSpaceWorkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/9 10:38
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/finiteSpaceWork")
@RequiredArgsConstructor
public class FiniteSpaceWorkController {

    private final FiniteSpaceWorkService finiteSpaceWorkService;

    @GetMapping("list")
    public EpsResponse finiteSpaceWorkList(QueryRequest request, FiniteSpaceWork finiteSpaceWork) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.finiteSpaceWorkService.findFiniteSpaceWorks(request,
                finiteSpaceWork));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping("add")
    public void addFiniteSpaceWork(FiniteSpaceWork dto) throws EpsException {
        try {
            this.finiteSpaceWorkService.createFiniteSpaceWork(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }
    @DeleteMapping("{id}")
    public void deleteFiniteSpaceWork(@NotNull @PathVariable Long id) throws EpsException {
        try {
            this.finiteSpaceWorkService.deleteFiniteSpaceWork(id);
        } catch (Exception e) {
            String message = "删除应急预案失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
    @DeleteMapping(value = "delete")
    @ResponseBody
    public void deleteFile(Long id,String filePath) {
        try {
            this.finiteSpaceWorkService.deleteFile(id,filePath);
        } catch (Exception e) {
            String message = "删除文件失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    @GetMapping("{id}")
    public EpsResponse queryFiniteSpaceWork(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(finiteSpaceWorkService.findFiniteSpaceWork(id));
    }
}
