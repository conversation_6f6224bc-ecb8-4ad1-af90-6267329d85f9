package com.example.eps.ins.controller.system;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.example.eps.ins.common.annotation.ControllerEndpoint;
import com.example.eps.ins.common.constant.RedisConstant;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.service.RedisService;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IRegionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 地区表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:39
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/region")
@RequiredArgsConstructor
public class RegionSystemController {

    private final IRegionService regionService;
    private final RedisService redisService;

    @GetMapping("all")
    public EpsResponse getAllRegionsFilterDept() {
        return new EpsResponse().data(regionService.getAllRegionsFilterDept());
    }

    @GetMapping("all/manage")
    public EpsResponse getAllRegions() {
        return new EpsResponse().data(regionService.getAllRegions());
    }

    @GetMapping("currentUser")
    public EpsResponse currentUserRegionTree() {
        return new EpsResponse().data(regionService.getCurrentUserRegions());
    }
    @GetMapping("currentUserForEnterprise")
    public EpsResponse currentUserForEnterprise() {
        return new EpsResponse().data(regionService.getCurrentUserForEnterprise());
    }
    @GetMapping("currentUserRegion")
    public EpsResponse currentUserRegion() {
        return new EpsResponse().data(regionService.currentUserRegion());
    }
    @GetMapping("currentUserByMessage")
    public EpsResponse currentUserByMessage() {
        return new EpsResponse().data(regionService.getCurrentUserRegionsByMessage());
    }
    @GetMapping("currentUserByLawEnforcement")
    public EpsResponse currentUserByLawEnforcement() {
        return new EpsResponse().data(regionService.getCurrentUserRegionsByLawEnforcement());
    }

    @GetMapping("/{id}")
    public EpsResponse getRegion(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(regionService.getById(id));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('region:update')")
    @ControllerEndpoint(operation = "编辑管理局名称", exceptionMessage = "编辑管理局名称失败")
    public void updateRegion(@NotNull @PathVariable Long id, @RequestBody Region region) throws EpsException {
        Region one = regionService.getById(id);
        if (null == one) {
            throw new EpsException("地区信息不存在");
        }
        LambdaUpdateWrapper<Region> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(Region::getId, id);
        updateWrapper.set(Region::getDept, region.getDept() == null ? null : region.getDept().trim());
        updateWrapper.set(Region::getModifyUserId, EpsUtil.getCurrentUser().getUserId());
        updateWrapper.set(Region::getModifyTime, new Date());
        this.regionService.update(updateWrapper);
        redisService.del(RedisConstant.REGION_VO);
    }
}
