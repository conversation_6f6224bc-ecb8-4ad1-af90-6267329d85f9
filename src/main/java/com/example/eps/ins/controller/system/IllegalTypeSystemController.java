package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.IllegalType;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.service.IIllegalTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 风险类型 Controller
 *
 * <AUTHOR>
 * @date 2022-01-24 15:39:12
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/illegalType")
@RequiredArgsConstructor
public class IllegalTypeSystemController {
    private final IIllegalTypeService illegalTypeService;

    @GetMapping
    public EpsResponse getAllIllegalType(IllegalType illegalType) {
        return new EpsResponse().data(illegalTypeService.findIllegalTypes(illegalType));
    }

    @GetMapping("category")
    public EpsResponse getAllIllegalCategory() {
        return new EpsResponse().data(illegalTypeService.findIllegalCategory(new IllegalType()));
    }

    @GetMapping("list")
    public EpsResponse illegalTypeList(QueryRequest request, IllegalType illegalType) {
        return new EpsResponse().data(illegalTypeService.findIllegalTypes(request, illegalType));
    }

    @PostMapping
    public void addIllegalType(@RequestBody IllegalType illegalType) throws EpsException {
        try {
            this.illegalTypeService.createIllegalType(illegalType);
        } catch (Exception e) {
            String message = "新增IllegalType失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping
    public void deleteIllegalType(@RequestBody IllegalType illegalType) throws EpsException {
        try {
            this.illegalTypeService.deleteIllegalType(illegalType);
        } catch (Exception e) {
            String message = "删除IllegalType失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
    public void updateIllegalType(@RequestBody IllegalType illegalType) throws EpsException {
        try {
            this.illegalTypeService.updateIllegalType(illegalType);
        } catch (Exception e) {
            String message = "修改IllegalType失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}