package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.dto.report.model.MessageAllVo;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.MessageAllService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @Description: <消息通知表前端控制器>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/message/all")
@RequiredArgsConstructor
public class MessageAllController {

    private final MessageAllService messageAllService;

    @PostMapping("list")
    public EpsResponse messageList(@RequestBody MessageAllVo dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.messageAllService.findMessages(dto));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping("detail")
    public EpsResponse detail(@RequestBody MessageAllVo dto) {
        return new EpsResponse().data(messageAllService.detail(dto.getId()));
    }

    @PostMapping("lookMessage")
    public EpsResponse lookMessage(@RequestBody MessageAllVo dto) {
        messageAllService.lookMessage(dto.getId());
        return new EpsResponse().data("true");
    }
}