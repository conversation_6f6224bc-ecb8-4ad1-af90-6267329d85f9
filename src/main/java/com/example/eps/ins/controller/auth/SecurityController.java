package com.example.eps.ins.controller.auth;

import com.example.eps.ins.common.constant.StringConstant;
import com.example.eps.ins.common.entity.CurrentUser;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.exception.ValidateCodeException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.controller.auth.manager.UserManager;
import com.example.eps.ins.service.ValidateCodeService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.Principal;

import static com.example.eps.ins.service.impl.ValidateCodeServiceImpl.CAPTCHA_TYPE;
import static com.example.eps.ins.service.impl.ValidateCodeServiceImpl.SMS;

/**
 * <AUTHOR>
 */
@Controller
@RequiredArgsConstructor
@RequestMapping("auth")
public class SecurityController {

    private final ValidateCodeService validateCodeService;
    private final ValidateCodeService smsValidateCodeService;
    private final UserManager userManager;
    private final ConsumerTokenServices consumerTokenServices;

    @ResponseBody
    @GetMapping("user")
    public Principal currentUser(Principal principal) {
        return principal;
    }

    @ResponseBody
    @GetMapping("captcha")
    public void captcha(HttpServletRequest request, HttpServletResponse response) throws IOException, ValidateCodeException {
        validateCodeService.create(request, response);
    }

    @ResponseBody
    @GetMapping("validateCode")
    public EpsResponse validateCode(HttpServletRequest request, HttpServletResponse response) throws IOException,
            ValidateCodeException {
        Boolean code=validateCodeService.validateCode(request, response);
        return new EpsResponse().data(code);
    }

    @ResponseBody
    @GetMapping("smsCaptcha")
    public void smsCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        request.setAttribute(CAPTCHA_TYPE, SMS);
        smsValidateCodeService.create(request, response);
    }

    @RequestMapping("login")
    public String login() {
        return "login";
    }

    @ResponseBody
    @DeleteMapping("signout")
    public EpsResponse signout(HttpServletRequest request, @RequestHeader("Authorization") String token) {
        token = StringUtils.replaceIgnoreCase(token, "bearer ", StringConstant.EMPTY);
        consumerTokenServices.revokeToken(token);
        return new EpsResponse().message("signout");
    }
}
