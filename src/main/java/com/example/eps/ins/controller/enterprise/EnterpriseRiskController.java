package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EnterpriseRisk;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IEnterpriseRiskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 企业风险表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:43
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/enterpriseRisk")
@RequiredArgsConstructor
public class EnterpriseRiskController {

    private final IEnterpriseRiskService enterpriseRiskService;

    @GetMapping
    public EpsResponse getAllEnterpriseRisks(EnterpriseRisk enterpriseRisk) {
        return new EpsResponse().data(enterpriseRiskService.findEnterpriseRisks(enterpriseRisk));
    }

    @GetMapping("list")
    public EpsResponse enterpriseRiskList(QueryRequest request, EnterpriseRisk enterpriseRisk) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseRiskService.findEnterpriseRisks(request, enterpriseRisk));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping
    public void addEnterpriseRisk(@Valid EnterpriseRisk enterpriseRisk) throws EpsException {
        try {
            this.enterpriseRiskService.createEnterpriseRisk(enterpriseRisk);
        } catch (Exception e) {
            String message = "新增EnterpriseRisk失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping
    public void deleteEnterpriseRisk(EnterpriseRisk enterpriseRisk) throws EpsException {
        try {
            this.enterpriseRiskService.deleteEnterpriseRisk(enterpriseRisk);
        } catch (Exception e) {
            String message = "删除EnterpriseRisk失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
    public void updateEnterpriseRisk(EnterpriseRisk enterpriseRisk) throws EpsException {
        try {
            this.enterpriseRiskService.updateEnterpriseRisk(enterpriseRisk);
        } catch (Exception e) {
            String message = "修改EnterpriseRisk失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}
