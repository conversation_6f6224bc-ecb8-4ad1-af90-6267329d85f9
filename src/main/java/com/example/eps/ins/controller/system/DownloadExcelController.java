package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.dto.report.model.RiskContentVo;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.model.EnterpriseVo;
import com.example.eps.ins.service.DownloadExcelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/11 15:16
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/excel")
@RequiredArgsConstructor
public class DownloadExcelController {
    private final DownloadExcelService downloadExcelService;

    @GetMapping("download")
    public void download(HttpServletResponse httpServletResponse, Enterprise enterprise) {
        downloadExcelService.download(httpServletResponse,enterprise);
    }

    @GetMapping("message")
    public void message(HttpServletResponse httpServletResponse, MessageUser messageUser) {
        downloadExcelService.message(httpServletResponse,messageUser);
    }
    @GetMapping("hidden")
    public void download(HttpServletResponse httpServletResponse,EnterpriseVo enterprise) {
        downloadExcelService.hidden(httpServletResponse,enterprise);
    }
    @GetMapping("warningForCard")
    public void warningForCard(HttpServletResponse httpServletResponse, WarningForCard warningForCard) {
        downloadExcelService.warningForCard(httpServletResponse,warningForCard);
    }
    @GetMapping("riskContent")
    public void riskContent(HttpServletResponse httpServletResponse, RiskContentVo riskContentVo) {
        downloadExcelService.riskContent(httpServletResponse,riskContentVo);
    }
}
