package com.example.eps.ins.controller.report;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.dto.report.req.CompanyView;
import com.example.eps.ins.service.EnterPriseReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/11/8 15:02
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/report/enterPriseReport")
@RequiredArgsConstructor
public class EnterPriseReportController {

    private final EnterPriseReportService enterPriseReportService;

    /**
     *风险企业占比
     */
    @PostMapping("getRiskEnterPrise")
    public EpsResponse getAll(@RequestBody CompanyView companyView){
        if (ObjectUtils.isEmpty(companyView.getRegionId())){
            Long regionId = EpsUtil.getCurrentUser().getRegionId();
            companyView.setRegionId(regionId);
        }
        return new EpsResponse().data(enterPriseReportService.getRiskEnterPrise(companyView.getRegionId()));
    }

    /**
     *风险情况统计
     */
    @PostMapping("getRisk")
    public EpsResponse getRisk(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getRisk(companyView.getRegionId()));
    }

    /**
     *规上规下企业占比
     */
    @PostMapping("getUpAndDown")
    public EpsResponse getUpAndDown(@RequestBody CompanyView companyView){
        if (ObjectUtils.isEmpty(companyView.getRegionId())){
            Long regionId = EpsUtil.getCurrentUser().getRegionId();
            companyView.setRegionId(regionId);
        }
        return new EpsResponse().data(enterPriseReportService.getUpAndDown(companyView.getRegionId()));
    }

    /**
     *安全三同时已做企业
     */
    @PostMapping("getThreeNow")
    public EpsResponse getThreeNow(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getThreeNow(companyView.getRegionId()));
    }

    /**
     *安全生产标准化
     */
    @PostMapping("getDangerStandard")
    public EpsResponse getDangerStandard(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getDangerStandard(companyView.getRegionId()));
    }

    /**
     *企业已有应急预案
     */
    @PostMapping("getPlanReport")
    public EpsResponse getPlanReport(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getPlanReport(companyView.getRegionId()));
    }

    /**
     *隐患排查制度建立
     */
    @PostMapping("getHiddenReport")
    public EpsResponse getHiddenReport(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getHiddenReport(companyView.getRegionId()));
    }

    /**
     *应急演练月度趋势
     */
    @PostMapping("getDrillReport")
    public EpsResponse getDrillReport(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getDrillReport(companyView));
    }

    /**
     *隐患排查月度趋势
     */
    @PostMapping("getListReport")
    public EpsResponse getListReport(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getListReport(companyView));
    }

    /**
     *教育培训月度趋势
     */
    @PostMapping("getTrainReport")
    public EpsResponse getTrainReport(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getTrainReport(companyView));
    }

    /**
     *设施设备维护月度趋势
     */
    @PostMapping("getMaintainReport")
    public EpsResponse getMaintainReport(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getMaintainReport(companyView));
    }

    /**
     *两单两卡
     */
    @PostMapping("getOrderCard")
    public EpsResponse getOrderCard(@RequestBody CompanyView companyView){
        return new EpsResponse().data(enterPriseReportService.getOrderCard(companyView.getRegionId()));
    }
}
