package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.system.TradeLog;
import com.example.eps.ins.service.ITradeLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController("/system")
@RequiredArgsConstructor
public class TestController {

    private final ITradeLogService tradeLogService;

    @PostMapping("/systempackage/send")
    public void packageSend(@RequestBody TradeLog tradeLog) {
        this.tradeLogService.packageAndSend(tradeLog);
    }

    @GetMapping("scope/test")
    @PreAuthorize("#oauth2.hasScope('write')")
    public EpsResponse testScope() {
        return new EpsResponse().message("当前client包含write scope");
    }
}
