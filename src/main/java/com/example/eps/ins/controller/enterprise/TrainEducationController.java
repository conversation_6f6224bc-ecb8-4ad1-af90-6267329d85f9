package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.TrainEducation;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.TrainEducationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/** 企业安全培训教育 @Author: Zhanghongyin @Date: Created in 2022/10/11 9:46 @Version: 1.0 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/trainEducation")
@RequiredArgsConstructor
public class TrainEducationController {
  private final TrainEducationService trainEducationService;

  @GetMapping("list")
  public EpsResponse trainEducationList(QueryRequest request, TrainEducation trainEducation) {
    Map<String, Object> dataTable =
        EpsUtil.getDataTable(
            this.trainEducationService.findTrainEducations(request, trainEducation));
    return new EpsResponse().data(dataTable);
  }

  @PostMapping("add")
  public void addTrainEducation(TrainEducation dto) throws EpsException {
    try {
      this.trainEducationService.createTrainEducation(dto);
    } catch (Exception e) {
      String message;
      if (!ObjectUtils.isEmpty(dto.getId())) {
        message = "编辑企业安全培训教育失败";
      } else {
        message = "新增企业安全培训教育失败";
      }
      log.error(message, e);
      throw new EpsException(e.getMessage());
    }
  }

  @DeleteMapping("{id}")
  public void deleteTrainEducation(@NotNull @PathVariable Long id) throws EpsException {
    try {
      this.trainEducationService.deleteTrainEducation(id);
    } catch (Exception e) {
      String message = "删除企业安全培训教育失败";
      log.error(message, e);
      throw new EpsException(message);
    }
  }

  /**
   * 删除文件
   *
   * @param filePath 文件路径
   */
  @DeleteMapping(value = "delete")
  @ResponseBody
  public void deleteFile(Long id,String filePath) {
    try {
      this.trainEducationService.deleteFile(id,filePath);
    } catch (Exception e) {
      String message = "删除文件失败";
      log.error(message, e);
      throw new EpsException(message);
    }
  }
  @GetMapping("{id}")
  public EpsResponse queryTrainEducation(@NotNull @PathVariable Long id) {
    return new EpsResponse().data(trainEducationService.findTrainEducation(id));
  }
}
