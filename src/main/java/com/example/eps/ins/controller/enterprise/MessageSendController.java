package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.MessageSendOne;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.MessageSendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/10 14:34
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/send")
@RequiredArgsConstructor
public class MessageSendController {

    private final MessageSendService messageSendService;

    @PostMapping("updateStatus")
    public void updateStatus(@Validated @NotNull Long id) throws EpsException {
        try {
            this.messageSendService.updateStatus(id);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }

    @GetMapping("noSeeNum")
    public EpsResponse noSeeNum() throws EpsException {
        return new EpsResponse().data(this.messageSendService.noSeeNum());
    }
    @GetMapping("list")
    public EpsResponse messageList(QueryRequest request, MessageSendOne messageSendOne) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.messageSendService.messageList(request,
                messageSendOne));
        return new EpsResponse().data(dataTable);
    }
}
