package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.PresentSituation;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.PresentSituationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 *
 * 安全三同时及现状评价
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 9:46
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/presentSituation")
@RequiredArgsConstructor
public class PresentSituationController {
    private final PresentSituationService presentSituationService;

    @GetMapping("list")
    public EpsResponse presentSituationList(QueryRequest request, PresentSituation emergencyPlan) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.presentSituationService.findPresentSituations(request,
                emergencyPlan));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping("add")
    public void addPresentSituation(PresentSituation dto) throws EpsException {
        try {
            this.presentSituationService.createPresentSituation(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }
    @DeleteMapping("{id}")
    public void deletePresentSituation(@NotNull @PathVariable Long id) throws EpsException {
        try {
            this.presentSituationService.deletePresentSituation(id);
        } catch (Exception e) {
            String message = "删除安全三同时及现状评价失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
    @DeleteMapping(value = "delete")
    @ResponseBody
    public void deleteFile(Long id,String filePath) {
        try {
            this.presentSituationService.deleteFile(id,filePath);
        } catch (Exception e) {
            String message = "删除文件失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    @GetMapping("{id}")
    public EpsResponse queryPresentSituation(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(presentSituationService.findPresentSituation(id));
    }
}
