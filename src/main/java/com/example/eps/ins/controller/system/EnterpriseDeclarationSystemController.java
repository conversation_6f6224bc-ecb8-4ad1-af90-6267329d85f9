package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.bean.EnterpriseDeclarationReview;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EnterpriseDeclaration;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.annotation.ControllerEndpoint;
import com.example.eps.ins.service.IEnterpriseDeclarationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 企业信息申报表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/enterpriseDeclaration")
@RequiredArgsConstructor
public class EnterpriseDeclarationSystemController {

    private final IEnterpriseDeclarationService enterpriseDeclarationService;

    @GetMapping("firstReviewList")
    @PreAuthorize("hasAuthority('enterpriseDeclaration:firstReviewList')")
    public EpsResponse enterpriseDeclarationFirstReviewList(QueryRequest request, EnterpriseDeclaration enterpriseDeclaration) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(
                this.enterpriseDeclarationService.findEnterpriseDeclarations(request, enterpriseDeclaration));
        return new EpsResponse().data(dataTable);
    }

    @GetMapping("secondReviewList")
    @PreAuthorize("hasAuthority('enterpriseDeclaration:secondReviewList')")
    public EpsResponse enterpriseDeclarationSecondReviewList(QueryRequest request, EnterpriseDeclaration enterpriseDeclaration) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(
                this.enterpriseDeclarationService.findEnterpriseDeclarations(request, enterpriseDeclaration));
        return new EpsResponse().data(dataTable);
    }

    @GetMapping("{id}")
    @PreAuthorize("hasAuthority('enterpriseDeclaration:get')")
    public EpsResponse queryEnterpriseDeclarationInfo(@NotNull @PathVariable Long id) {
        EnterpriseDeclarationResponse enterpriseDeclaration = this.
                enterpriseDeclarationService.queryEnterpriseDeclaration(id);
        return new EpsResponse().data(enterpriseDeclaration);
    }

    @PostMapping(value = "/firstReview")
    @PreAuthorize("hasAuthority('enterpriseDeclaration:firstReview')")
    @ControllerEndpoint(operation = "企业申报初审", exceptionMessage = "企业申报初审失败")
    public void firstReviewEnterpriseDeclaration(@RequestBody EnterpriseDeclarationReview review) throws EpsException {
        try {
            this.enterpriseDeclarationService.secondReviewEnterpriseDeclaration(review);
        } catch (EpsException e) {
            throw new EpsException(e.getMessage());
        } catch (Exception e) {
            String message = "企业申报信息审核失败，请联系管理员。";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PostMapping(value = "/secondReview")
//    @PreAuthorize("hasAuthority('enterpriseDeclaration:secondReview')")
    @ControllerEndpoint(operation = "企业申报复审", exceptionMessage = "企业申报复审失败")
    public void secondReviewEnterpriseDeclaration(@RequestBody EnterpriseDeclarationReview review) throws EpsException {
        try {
            this.enterpriseDeclarationService.secondReviewEnterpriseDeclaration(review);
        } catch (EpsException e) {
            throw new EpsException(e.getMessage());
        } catch (Exception e) {
            String message = "企业申报信息审核失败，请联系管理员。";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}
