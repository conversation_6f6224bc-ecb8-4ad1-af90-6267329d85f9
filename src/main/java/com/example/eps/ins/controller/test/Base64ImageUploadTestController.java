package com.example.eps.ins.controller.test;

import com.example.eps.ins.common.utils.Base64ImageUploadUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Base64 图片上传测试控制器
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/test/base64-upload")
@Slf4j
@RequiredArgsConstructor
public class Base64ImageUploadTestController {

    private final Base64ImageUploadUtil base64ImageUploadUtil;

    /**
     * 测试单个 base64 图片上传
     */
    @PostMapping("/single")
    public Map<String, Object> testSingleUpload(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();

        try {
            String base64Data = request.get("base64Data");
            String prefix = request.getOrDefault("prefix", "test_image");

            log.info("开始测试 base64 图片上传，前缀: {}", prefix);

            // 检查 base64Data 是否为空
            if (base64Data == null || base64Data.trim().isEmpty()) {
                response.put("code", 400);
                response.put("msg", "base64Data 不能为空");
                return response;
            }

            // 验证 base64 数据
            boolean isValid = base64ImageUploadUtil.validateBase64Image(base64Data);
            log.info("Base64 数据验证结果: {}", isValid);

            if (!isValid) {
                response.put("code", 400);
                response.put("msg", "无效的 base64 图片数据");
                return response;
            }

            // 获取数据大小
            long dataSize = base64ImageUploadUtil.getBase64DataSize(base64Data);
            log.info("Base64 数据大小: {} 字节", dataSize);

            // 上传图片
            String fileUrl = base64ImageUploadUtil.uploadBase64Image(base64Data, prefix);

            // 判断是否上传成功
            boolean isUploaded = !fileUrl.equals(base64Data);

            // 构建响应数据 (Java 8 兼容)
            Map<String, Object> data = new HashMap<>();
            data.put("originalSize", dataSize);
            data.put("fileUrl", fileUrl);
            data.put("isUploaded", isUploaded);

            if (isUploaded) {
                response.put("code", 200);
                response.put("msg", "上传成功");
            } else {
                response.put("code", 500);
                response.put("msg", "上传失败，返回原始数据");
            }
            response.put("data", data);

        } catch (Exception e) {
            log.error("测试 base64 图片上传失败", e);
            response.put("code", 500);
            response.put("msg", "上传失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * 测试批量 base64 图片上传
     */
    @PostMapping("/batch")
    public Map<String, Object> testBatchUpload(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            String[] base64DataArray = ((java.util.List<String>) request.get("base64DataArray"))
                    .toArray(new String[0]);
            String prefix = (String) request.getOrDefault("prefix", "test_batch");
            
            log.info("开始测试批量 base64 图片上传，数量: {}, 前缀: {}", base64DataArray.length, prefix);
            
            // 批量上传
            String[] fileUrls = base64ImageUploadUtil.uploadBase64Images(base64DataArray, prefix);
            
            // 统计结果
            int successCount = 0;
            for (int i = 0; i < fileUrls.length; i++) {
                if (!fileUrls[i].equals(base64DataArray[i])) {
                    successCount++;
                }
            }
            
            // 构建响应数据 (Java 8 兼容)
            Map<String, Object> data = new HashMap<>();
            data.put("totalCount", base64DataArray.length);
            data.put("successCount", successCount);
            data.put("failedCount", base64DataArray.length - successCount);
            data.put("fileUrls", fileUrls);

            response.put("code", 200);
            response.put("msg", "批量上传完成");
            response.put("data", data);
            
        } catch (Exception e) {
            log.error("测试批量 base64 图片上传失败", e);
            response.put("code", 500);
            response.put("msg", "批量上传失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 验证 base64 图片数据
     */
    @PostMapping("/validate")
    public Map<String, Object> validateBase64Image(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String base64Data = request.get("base64Data");
            
            boolean isValid = base64ImageUploadUtil.validateBase64Image(base64Data);
            long dataSize = base64ImageUploadUtil.getBase64DataSize(base64Data);
            
            // 构建响应数据 (Java 8 兼容)
            Map<String, Object> data = new HashMap<>();
            data.put("isValid", isValid);
            data.put("dataSize", dataSize);
            data.put("dataSizeKB", dataSize / 1024.0);
            data.put("dataSizeMB", dataSize / (1024.0 * 1024.0));

            response.put("code", 200);
            response.put("msg", "验证完成");
            response.put("data", data);
            
        } catch (Exception e) {
            log.error("验证 base64 图片数据失败", e);
            response.put("code", 500);
            response.put("msg", "验证失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 生成测试用的 base64 数据（小图片）
     */
    @GetMapping("/generate-test-data")
    public Map<String, Object> generateTestData() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 生成一个简单的 1x1 像素的 PNG 图片的 base64 数据
            String testBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==";
            
            // 构建响应数据 (Java 8 兼容)
            Map<String, Object> data = new HashMap<>();
            data.put("testBase64", testBase64);
            data.put("description", "1x1 像素的透明 PNG 图片");
            data.put("usage", "可以用这个数据测试 base64 图片上传功能");

            response.put("code", 200);
            response.put("msg", "测试数据生成成功");
            response.put("data", data);
            
        } catch (Exception e) {
            log.error("生成测试数据失败", e);
            response.put("code", 500);
            response.put("msg", "生成失败: " + e.getMessage());
        }
        
        return response;
    }

    /**
     * 获取上传统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getUploadStats() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // TODO: 可以添加上传统计功能
            // 构建响应数据 (Java 8 兼容)
            Map<String, Object> data = new HashMap<>();
            data.put("message", "统计功能待实现");
            data.put("timestamp", System.currentTimeMillis());

            response.put("code", 200);
            response.put("msg", "统计信息获取成功");
            response.put("data", data);
            
        } catch (Exception e) {
            log.error("获取上传统计信息失败", e);
            response.put("code", 500);
            response.put("msg", "获取失败: " + e.getMessage());
        }
        
        return response;
    }
}
