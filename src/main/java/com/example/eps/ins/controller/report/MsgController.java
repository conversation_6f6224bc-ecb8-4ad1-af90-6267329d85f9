package com.example.eps.ins.controller.report;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.config.WebSocket;
import com.example.eps.ins.common.dto.report.SubMessage;
import com.example.eps.ins.service.MsgService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023/1/17 10:44
 * @Version: 1.0
 */
@RestController
@RequestMapping("/report/region")
public class MsgController {

    @Resource
    WebSocket webSocket;
    @Resource
    MsgService userService;
    @PostMapping("user")
    public void getUser(@RequestBody SubMessage message){
        userService.sendMessage(message,webSocket);
    }
    @PostMapping("list")
    public EpsResponse getList(@RequestBody SubMessage message){
        return new EpsResponse().data(userService.getList(message));
    }
}
