package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.SpecialCertificate;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.SpecialCertificateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/9 10:38
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/specialCertificate")
@RequiredArgsConstructor
public class SpecialCertificateSystemController {


    private final SpecialCertificateService specialCertificateService;

    @GetMapping("list")
    public EpsResponse finiteSpecialCertificateList(QueryRequest request, SpecialCertificate specialCertificate) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.specialCertificateService.findSpecialCertificates(request,
                specialCertificate));
        return new EpsResponse().data(dataTable);
    }

    @GetMapping("{id}")
    public EpsResponse querySpecialCertificate(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(specialCertificateService.findSpecialCertificate(id));
    }
}
