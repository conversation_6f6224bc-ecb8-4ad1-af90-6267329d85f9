package com.example.eps.ins.controller.safety;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.eps.ins.common.entity.safety.SafetyMonitorEvent;
import com.example.eps.ins.service.SafetyMonitorEventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 安全监控事件查询控制器
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@RestController
@RequestMapping("/safety/monitor/event")
@Slf4j
@RequiredArgsConstructor
public class SafetyMonitorEventController {

    private final SafetyMonitorEventService safetyMonitorEventService;

    /**
     * 分页查询安全监控事件
     */
    @GetMapping("/page")
    public Map<String, Object> getEventPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer eventType,
            @RequestParam(required = false) Integer haveAlarm,
            @RequestParam(required = false) String deviceSerial,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        try {
            Page<SafetyMonitorEvent> page = new Page<>(current, size);
            QueryWrapper<SafetyMonitorEvent> queryWrapper = new QueryWrapper<>();
            
            // 构建查询条件
            if (eventType != null) {
                queryWrapper.eq("event_type", eventType);
            }
            if (haveAlarm != null) {
                queryWrapper.eq("have_alarm", haveAlarm);
            }
            if (deviceSerial != null && !deviceSerial.trim().isEmpty()) {
                queryWrapper.like("device_serial", deviceSerial);
            }
            if (startTime != null && endTime != null) {
                queryWrapper.between("create_time", startTime, endTime);
            }
            
            queryWrapper.orderByDesc("create_time");
            
            IPage<SafetyMonitorEvent> result = safetyMonitorEventService.page(page, queryWrapper);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", result);
            
            return response;
            
        } catch (Exception e) {
            log.error("分页查询安全监控事件失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 根据ID查询事件详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getEventById(@PathVariable Long id) {
        try {
            SafetyMonitorEvent event = safetyMonitorEventService.getById(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", event);
            
            return response;
            
        } catch (Exception e) {
            log.error("查询事件详情失败: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 查询告警事件
     */
    @GetMapping("/alarms")
    public Map<String, Object> getAlarmEvents(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<SafetyMonitorEvent> events = safetyMonitorEventService.getRecentAlarmEvents(limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", events);
            
            return response;
            
        } catch (Exception e) {
            log.error("查询告警事件失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 统计事件数量按事件类型
     */
    @GetMapping("/statistics/by-type")
    public Map<String, Object> getEventStatisticsByType(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        try {
            List<Map<String, Object>> statistics = safetyMonitorEventService.countEventsByType(startTime, endTime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("msg", "统计成功");
            response.put("data", statistics);
            
            return response;
            
        } catch (Exception e) {
            log.error("统计事件数量失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", "统计失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 统计告警事件数量按设备
     */
    @GetMapping("/statistics/by-device")
    public Map<String, Object> getAlarmStatisticsByDevice(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        try {
            List<Map<String, Object>> statistics = safetyMonitorEventService.countAlarmEventsByDevice(startTime, endTime);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("msg", "统计成功");
            response.put("data", statistics);
            
            return response;
            
        } catch (Exception e) {
            log.error("统计告警事件数量失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", "统计失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 处理事件
     */
    @PostMapping("/{id}/process")
    public Map<String, Object> processEvent(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            String processResult = request.get("processResult");
            boolean success = safetyMonitorEventService.processEvent(id, processResult);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", success ? 200 : 500);
            response.put("msg", success ? "处理成功" : "处理失败");
            
            return response;
            
        } catch (Exception e) {
            log.error("处理事件失败: {}", id, e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", "处理失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 批量处理事件
     */
    @PostMapping("/batch-process")
    public Map<String, Object> batchProcessEvents(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> eventIds = (List<Long>) request.get("eventIds");
            String processResult = (String) request.get("processResult");
            
            boolean success = safetyMonitorEventService.batchProcessEvents(eventIds, processResult);
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", success ? 200 : 500);
            response.put("msg", success ? "批量处理成功" : "批量处理失败");
            
            return response;
            
        } catch (Exception e) {
            log.error("批量处理事件失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", "批量处理失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 查询未处理的事件
     */
    @GetMapping("/unprocessed")
    public Map<String, Object> getUnprocessedEvents() {
        try {
            List<SafetyMonitorEvent> events = safetyMonitorEventService.getUnprocessedEvents();
            
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", events);
            
            return response;
            
        } catch (Exception e) {
            log.error("查询未处理事件失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("code", 500);
            response.put("msg", "查询失败: " + e.getMessage());
            return response;
        }
    }
}
