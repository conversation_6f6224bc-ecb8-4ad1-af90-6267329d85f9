package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EnterpriseUser;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IEnterpriseUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 *  Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:30
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/enterpriseUser")
@RequiredArgsConstructor
public class EnterpriseUserSystemController {

    private final IEnterpriseUserService enterpriseUserService;

    @GetMapping
    @PreAuthorize("hasAuthority('enterpriseUser:list')")
    public EpsResponse getAllEnterpriseUsers(EnterpriseUser enterpriseUser) {
        return new EpsResponse().data(enterpriseUserService.findEnterpriseUsers(enterpriseUser));
    }

    @GetMapping("list")
    @PreAuthorize("hasAuthority('enterpriseUser:list')")
    public EpsResponse enterpriseUserList(QueryRequest request, EnterpriseUser enterpriseUser) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseUserService.findEnterpriseUsers(request, enterpriseUser));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping
    @PreAuthorize("hasAuthority('enterpriseUser:add')")
    public void addEnterpriseUser(@Valid EnterpriseUser enterpriseUser) throws EpsException {
        try {
            this.enterpriseUserService.createEnterpriseUser(enterpriseUser);
        } catch (Exception e) {
            String message = "新增EnterpriseUser失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping
    @PreAuthorize("hasAuthority('enterpriseUser:delete')")
    public void deleteEnterpriseUser(EnterpriseUser enterpriseUser) throws EpsException {
        try {
            this.enterpriseUserService.deleteEnterpriseUser(enterpriseUser);
        } catch (Exception e) {
            String message = "删除EnterpriseUser失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
    @PreAuthorize("hasAuthority('enterpriseUser:update')")
    public void updateEnterpriseUser(EnterpriseUser enterpriseUser) throws EpsException {
        try {
            this.enterpriseUserService.updateEnterpriseUser(enterpriseUser);
        } catch (Exception e) {
            String message = "修改EnterpriseUser失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}
