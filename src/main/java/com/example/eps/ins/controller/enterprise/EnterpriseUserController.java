package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.bean.ForgetMobile;
import com.example.eps.ins.common.bean.ForgetName;
import com.example.eps.ins.common.bean.ForgetPassword;
import com.example.eps.ins.common.entity.CreatedResponse;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EnterpriseUser;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.exception.ValidateCodeException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.validator.ValidationGroups;
import com.example.eps.ins.common.annotation.ControllerEndpoint;
import com.example.eps.ins.service.IEnterpriseUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:30
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/enterpriseUser")
@RequiredArgsConstructor
public class EnterpriseUserController {
    private final IEnterpriseUserService enterpriseUserService;

    @GetMapping
    public EpsResponse getAllEnterpriseUsers(@RequestBody EnterpriseUser enterpriseUser) {
        return new EpsResponse().data(enterpriseUserService.findEnterpriseUsers(enterpriseUser));
    }

    @GetMapping("list")
    @ControllerEndpoint(operation = "分页查询企业用户信息", exceptionMessage = "查询企业用户信息失败")
    public EpsResponse enterpriseUserList(QueryRequest request, EnterpriseUser enterpriseUser) {
        Map<String, Object> dataTable = EpsUtil.
                getDataTable(this.enterpriseUserService.findEnterpriseUsers(request, enterpriseUser));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping
    @ControllerEndpoint(operation = "创建企业用户信息", exceptionMessage = "创建企业用户信息失败")
    public ResponseEntity addEnterpriseUser(
            @Validated({ValidationGroups.Create.class}) @RequestBody EnterpriseUser enterpriseUser) throws ValidateCodeException {
        CreatedResponse response = new CreatedResponse();
        if (ObjectUtils.isEmpty(ObjectUtils.isEmpty(enterpriseUser.getSms()))){
            return new ResponseEntity(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
        boolean coedValidated = this.enterpriseUserService.validatedCode(enterpriseUser.getMobile(),
                enterpriseUser.getSms());
        if (coedValidated) {
            Long createdId = this.enterpriseUserService.createEnterpriseUser(enterpriseUser);
            response.setId(createdId);
            return new ResponseEntity(response, HttpStatus.OK);
        }else {
            return new ResponseEntity(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping(value = {"/check/enterpriseName/{enterpriseName}/mobile{mobile}",
            "/check/enterpriseName/{enterpriseName}", "/check/mobile/{mobile}"})
    @ControllerEndpoint(operation = "验证企业信息申报信息详情是否重复", exceptionMessage = "验证失败")
    public boolean checkEnterpriseUser(@PathVariable(required = false) String enterpriseName,
                                       @PathVariable(required = false) String mobile) throws ValidateCodeException {
        return this.enterpriseUserService.checkEnterpriseUser(enterpriseName, mobile);
    }

    @PutMapping
    @ControllerEndpoint(operation = "修改企业用户信息", exceptionMessage = "修改企业用户信息失败")
    public void updateEnterpriseUser(
            @Validated({ValidationGroups.Update.class}) @RequestBody EnterpriseUser enterpriseUser) {
        this.enterpriseUserService.updateEnterpriseUser(enterpriseUser);
    }

    @PostMapping("/updateMobile")
    public void updateMobile(@RequestBody ForgetMobile forgetMobile) {
        this.enterpriseUserService.updateMobile(forgetMobile);
    }

    @PostMapping("/updateName")
    public void updateName(@RequestBody ForgetName forgetName) {
        this.enterpriseUserService.updateName(forgetName);
    }

    @PutMapping("forgetPassword")
    @ControllerEndpoint(operation = "忘记密码", exceptionMessage = "忘记密码")
    public ResponseEntity<EpsResponse> forgetPassword(@RequestBody ForgetPassword forgetPassword)
            throws EpsException {
        try {

            if (!EpsUtil.checkMobile(forgetPassword.getMobile())) {
                throw new EpsException("手机号不符合");
            }

            boolean coedValidated = this.enterpriseUserService.validatedCode(forgetPassword.getMobile(),
                    forgetPassword.getSmsCode());
            if (!coedValidated) {
                throw new EpsException("验证码错误");
            }

            EnterpriseUser enterpriseUser = new EnterpriseUser();
            enterpriseUser.setMobile(forgetPassword.getMobile());
            List<EnterpriseUser> users = this.enterpriseUserService.findEnterpriseUsers(enterpriseUser);
            if (users == null || users.size() < 1) {
                throw new EpsException("手机号未注册");
            }

            if (!forgetPassword.getPassword().equals(forgetPassword.getConfirmPassword())){
                throw new EpsException("两次输入的密码不一致");
            }
            EnterpriseUser mobileUser = users.get(0);
            mobileUser.setPassword(new BCryptPasswordEncoder().encode(forgetPassword.getPassword()));
            this.enterpriseUserService.updateEnterpriseUser(mobileUser);
            return ResponseEntity.ok().body(new EpsResponse().message("重置密码成功"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }

  public static void main(String[] args) {
    //
//      String pass="B123456";
//      String password="$2a$10$FS/cKW6smQtT0qHjJ1Mo.O3xJrdfDhWf213o69XO332BTO6mCZPl.";
//      PasswordEncoder encoder = new BCryptPasswordEncoder();
//      String encode = encoder.encode(pass);
//      System.out.println(encode);
//      boolean matches = encoder.matches(pass, password);
//      System.out.println(matches);
      List<Integer> list=new ArrayList<>();
      list.add(1);
      list.add(2);
      list.add(3);
        System.out.println(list.toString());
  }
}
