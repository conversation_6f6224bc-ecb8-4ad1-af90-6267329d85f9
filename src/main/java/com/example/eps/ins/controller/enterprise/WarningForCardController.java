package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.TDictionaries;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.WarningForCardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/11/10 13:55
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/warningForCard")
@RequiredArgsConstructor
public class WarningForCardController {

    private final WarningForCardService warningForCardService;

    @GetMapping("list")
    public EpsResponse specialPeopleList(QueryRequest request, WarningForCard warningForCard) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.warningForCardService.findSpecialPeoples(request,
                warningForCard));
        return new EpsResponse().data(dataTable);
    }
    @PostMapping("getCode")
    public EpsResponse getCode() {
        List<TDictionaries> dataTable = this.warningForCardService.getCode();
        return new EpsResponse().data(dataTable);
    }
    @PostMapping("update")
    public void update(@RequestBody WarningForCard warningForCard) {
        try {
            this.warningForCardService.updateWar(warningForCard);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }
}
