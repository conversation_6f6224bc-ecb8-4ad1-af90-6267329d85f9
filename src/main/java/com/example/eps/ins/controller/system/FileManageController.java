package com.example.eps.ins.controller.system;

import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.example.eps.ins.common.dto.report.model.OssVo;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.service.FdfsClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@RequiredArgsConstructor
@RequestMapping("/system/sys/fileManage")
@Slf4j
public class FileManageController {
    @Value("${fdfs.maxFileSize}")
    private String maxFileSize;

    @Value("${oss.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.roleArn}")
    private String roleArn;

    @Value("${oss.region}")
    private String region;

    @Value("${oss.bucket}")
    private String bucket;
    @Value("${oss.endpoint}")
    private String endpoint;

    private final FdfsClientService fdfsClientService;

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件路径
     */
    @PostMapping(value = "upload")
    @ResponseBody
    public EpsResponse uploadFile(MultipartFile file) throws IOException {
        return new EpsResponse().data(fdfsClientService.uploadFile(file));
    }

    /**
     * 上传文件
     *
     * @param file 文件
     * @return 文件路径
     */
    @PostMapping(value = "ossUpload")
    @ResponseBody
    public EpsResponse ossUploadFile(MultipartFile file) throws IOException {
        return new EpsResponse().data(fdfsClientService.ossUploadFile(file));
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
    @DeleteMapping(value = "delete")
    @ResponseBody
    public void deleteFile(String filePath) {
        fdfsClientService.deleteFile(filePath);
    }

    /**
     * 获取阿里OSS文件上传SecurityToken
     *
     */
    @GetMapping(value = "getToken")
    @ResponseBody
    public EpsResponse getToken() {
        String roleSessionName = "session-name";// 自定义即可
        // 定制你的policy
        String policy ="{\n" +
                "    \"Statement\": [\n" +
                "        {\n" +
                "            \"Action\": \"oss:*\",\n" +
                "            \"Effect\": \"Allow\",\n" +
                "            \"Resource\": \"*\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"Version\": \"1\"\n" +
                "}";
        OssVo ossVo=new OssVo();

        ossVo.setBucket(bucket);
        ossVo.setRegion(region);
        try {
            AssumeRoleResponse response = OssVo.assumeRole(accessKeyId, accessKeySecret, roleArn, roleSessionName,
                    policy);
            ossVo.setSecurityToken(response.getCredentials().getSecurityToken());
            ossVo.setAccessKeyId(response.getCredentials().getAccessKeyId());
            ossVo.setAccessKeySecret(response.getCredentials().getAccessKeySecret());
        } catch (ClientException e) {
            throw new EpsException("Error message: " + e.getErrMsg());
        }

        return  new EpsResponse().data(ossVo);
    }

}
