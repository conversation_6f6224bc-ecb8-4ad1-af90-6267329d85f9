package com.example.eps.ins.controller.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.device.SubDevice;
import com.example.eps.ins.service.ISubDeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 子设备管理控制器
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@RestController
@RequestMapping("/subDevice")
@RequiredArgsConstructor
public class SubDeviceController {

    private final ISubDeviceService subDeviceService;

    /**
     * 分页查询子设备列表
     *
     * @param request 分页请求
     * @param subDevice 查询条件
     * @return 子设备分页列表
     */
    @GetMapping("/list")
    public EpsResponse getSubDeviceList(QueryRequest request, SubDevice subDevice) {
        log.info("开始查询子设备列表，查询条件: {}", subDevice);

        try {
            IPage<SubDevice> subDevicePage = subDeviceService.findSubDevices(request, subDevice);

            return new EpsResponse()
                    .put("success", true)
                    .put("total", subDevicePage.getTotal())
                    .put("current", subDevicePage.getCurrent())
                    .put("size", subDevicePage.getSize())
                    .data(subDevicePage.getRecords())
                    .message("子设备列表查询成功");

        } catch (Exception e) {
            log.error("查询子设备列表失败", e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询子设备列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据父设备ID查询子设备列表
     *
     * @param parentDeviceId 父设备ID
     * @return 子设备列表
     */
    @GetMapping("/listByParent/{parentDeviceId}")
    public EpsResponse getSubDevicesByParentId(@PathVariable Long parentDeviceId) {
        log.info("开始查询父设备ID: {} 的子设备列表", parentDeviceId);

        try {
            List<SubDevice> subDevices = subDeviceService.findByParentDeviceId(parentDeviceId);

            return new EpsResponse()
                    .put("success", true)
                    .put("count", subDevices.size())
                    .data(subDevices)
                    .message("子设备列表查询成功");

        } catch (Exception e) {
            log.error("查询父设备ID: {} 的子设备列表失败", parentDeviceId, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询子设备列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据父设备ID和状态查询子设备列表
     *
     * @param parentDeviceId 父设备ID
     * @param status 设备状态
     * @return 子设备列表
     */
    @GetMapping("/listByParentAndStatus/{parentDeviceId}/{status}")
    public EpsResponse getSubDevicesByParentIdAndStatus(@PathVariable Long parentDeviceId, @PathVariable String status) {
        log.info("开始查询父设备ID: {} 状态: {} 的子设备列表", parentDeviceId, status);

        try {
            List<SubDevice> subDevices = subDeviceService.findByParentDeviceIdAndStatus(parentDeviceId, status);

            return new EpsResponse()
                    .put("success", true)
                    .put("count", subDevices.size())
                    .put("parentDeviceId", parentDeviceId)
                    .put("status", status)
                    .data(subDevices)
                    .message("子设备列表查询成功");

        } catch (Exception e) {
            log.error("查询父设备ID: {} 状态: {} 的子设备列表失败", parentDeviceId, status, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询子设备列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询子设备详情
     *
     * @param id 子设备ID
     * @return 子设备详情
     */
    @GetMapping("/{id}")
    public EpsResponse getSubDeviceById(@PathVariable Long id) {
        log.info("开始查询子设备详情，ID: {}", id);

        try {
            SubDevice subDevice = subDeviceService.getById(id);

            if (subDevice != null) {
                return new EpsResponse()
                        .put("success", true)
                        .data(subDevice)
                        .message("子设备详情查询成功");
            } else {
                return new EpsResponse()
                        .put("success", false)
                        .message("子设备不存在");
            }

        } catch (Exception e) {
            log.error("查询子设备详情失败，ID: {}", id, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询子设备详情失败: " + e.getMessage());
        }
    }

    /**
     * 更新子设备同步状态
     *
     * @param id 子设备ID
     * @param syncStatus 同步状态
     * @return 更新结果
     */
    @PutMapping("/{id}/syncStatus/{syncStatus}")
    public EpsResponse updateSyncStatus(@PathVariable Long id, @PathVariable Integer syncStatus) {
        log.info("开始更新子设备同步状态，ID: {}, 同步状态: {}", id, syncStatus);

        try {
            boolean result = subDeviceService.updateSyncStatus(id, syncStatus);

            if (result) {
                return new EpsResponse()
                        .put("success", true)
                        .message("子设备同步状态更新成功");
            } else {
                return new EpsResponse()
                        .put("success", false)
                        .message("子设备同步状态更新失败");
            }

        } catch (Exception e) {
            log.error("更新子设备同步状态失败，ID: {}", id, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("更新子设备同步状态失败: " + e.getMessage());
        }
    }

    /**
     * 根据同步状态查询子设备列表
     *
     * @param syncStatus 同步状态
     * @return 子设备列表
     */
    @GetMapping("/listBySyncStatus/{syncStatus}")
    public EpsResponse getSubDevicesBySyncStatus(@PathVariable Integer syncStatus) {
        log.info("开始查询同步状态: {} 的子设备列表", syncStatus);

        try {
            List<SubDevice> subDevices = subDeviceService.findBySyncStatus(syncStatus);

            return new EpsResponse()
                    .put("success", true)
                    .put("count", subDevices.size())
                    .put("syncStatus", syncStatus)
                    .data(subDevices)
                    .message("子设备列表查询成功");

        } catch (Exception e) {
            log.error("查询同步状态: {} 的子设备列表失败", syncStatus, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询子设备列表失败: " + e.getMessage());
        }
    }
}
