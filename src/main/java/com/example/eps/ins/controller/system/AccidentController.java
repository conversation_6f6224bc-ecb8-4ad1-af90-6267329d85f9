package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.annotation.ControllerEndpoint;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.Accident;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IAccidentService;
import com.example.eps.ins.service.IRegionService;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.wuwenze.poi.ExcelKit;
import com.wuwenze.poi.handler.ExcelReadHandler;
import com.wuwenze.poi.pojo.ExcelErrorField;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 事故统计表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-27 14:28:15
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/accident")
@RequiredArgsConstructor
public class AccidentController {
    private static final String TEMPLATE_EXCEL_NAME = "事故统计查询模板";
    private static final String EXCEL_TYPE = ".xlsx";
    private final IAccidentService accidentService;
    private final IRegionService regionService;
    private static final Map<String, String> DEPT_MAPPING = new HashMap<String, String>() {{
        put("长寿区应急管理局", "长寿区（长寿经开区）应急管理局");
        put("开县应急管理局", "开州区应急管理局");
        put("石柱土家族自治县应急管理局", "石柱县应急管理局");
        put("秀山土家族苗族自治县应急管理局", "秀山县应急管理局");
        put("酉阳土家族苗族自治县应急管理局", "酉阳县应急管理局");
        put("彭水苗族土家族自治县应急管理局", "彭水县应急管理局");
    }};

    @GetMapping("countryList")
    @PreAuthorize("hasAuthority('accident:list')")
    @ControllerEndpoint(operation = "查询事故发生县", exceptionMessage = "查询事故发生县失败")
    public EpsResponse accidentCountryList() {
        return new EpsResponse().data(this.accidentService.findAccidentCountryList());
    }

    @GetMapping("list")
    @PreAuthorize("hasAuthority('accident:list')")
    @ControllerEndpoint(operation = "分页查询事故统计", exceptionMessage = "分页查询事故统计失败")
    public EpsResponse accidentList(QueryRequest request, Accident accident) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.accidentService.findAccidents(request, accident));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping
//    @PreAuthorize("hasAuthority('accident:add')")
    public void addAccident(@Valid Accident accident) throws EpsException {
        try {
            this.accidentService.createAccident(accident);
        } catch (Exception e) {
            String message = "新增Accident失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('accident:delete')")
    public void deleteAccident(@PathVariable("id") Integer id) throws EpsException {
        try {
            Accident accident = new Accident();
            accident.setAccidentId(id);
            this.accidentService.deleteAccident(accident);
        } catch (Exception e) {
            String message = "删除失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
//    @PreAuthorize("hasAuthority('accident:update')")
    public void updateAccident(Accident accident) throws EpsException {
        try {
            this.accidentService.updateAccident(accident);
        } catch (Exception e) {
            String message = "修改Accident失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PostMapping("import")
    @PreAuthorize("hasAuthority('accident:import')")
    @ControllerEndpoint(operation = "导入事故统计Excel数据", exceptionMessage = "导入事故统计Excel数据失败")
    public EpsResponse importExcel(MultipartFile file) throws IOException, EpsException {
        if (file.isEmpty()) {
            throw new EpsException("导入数据为空");
        }
        String filename = file.getOriginalFilename();
        if (!StringUtils.endsWith(filename, EXCEL_TYPE)) {
            throw new EpsException(String.format("只支持%s类型文件导入", EXCEL_TYPE));
        }

        final List<Accident> accidentArray = Lists.newArrayList();
        final List<Map<String, Object>> error = Lists.newArrayList();
        final List<String> repeatAccidentCodeArray = Lists.newArrayList();
        final List<String> emptyAccidentCodeArray = Lists.newArrayList();
        final List<String> unknownPushUnitArray = Lists.newArrayList();
        final Map<Long, String> pushUnitMap = regionService.getDeptMap();

        ExcelKit.$Import(Accident.class).readXlsx(file.getInputStream(), new ExcelReadHandler<Accident>() {
            @Override
            public void onSuccess(int sheet, int row, Accident accident) {
                if (row != 1) {
                    String pushUnit = accident.getPushUnit();
                    if (DEPT_MAPPING.containsKey(pushUnit)) {
                        accident.setPushUnit(DEPT_MAPPING.get(pushUnit));
                    }

                    if (StringUtils.isEmpty(accident.getAccidentCode())) {
                        emptyAccidentCodeArray.add((String.valueOf(row - 1)));
                    }

                    if (!pushUnitMap.values().contains(accident.getPushUnit()) && !unknownPushUnitArray.contains(accident.getPushUnit())) {
                        unknownPushUnitArray.add(accident.getPushUnit());
                    }

                    if (pushUnitMap.values().contains(accident.getPushUnit())) {
                        accident.setRegionId(EpsUtil.getMapKey(pushUnitMap, accident.getPushUnit()));
                    }

                    accidentArray.add(accident);
                    Accident queryEqAccidentCode = new Accident();
                    queryEqAccidentCode.setAccidentCode(accident.getAccidentCode());
                    if (accidentService.findAccidents(queryEqAccidentCode).size() > 0) {
                        repeatAccidentCodeArray.add(accident.getAccidentCode());
                    }
                }
            }

            @Override
            public void onError(int sheet, int row, List<ExcelErrorField> errorFields) {
                error.add(ImmutableMap.of("row", row, "errorFields", errorFields));
            }
        });

        if (emptyAccidentCodeArray.size() > 0) {
            throw new EpsException(String.format("excel中第%s条数据事故编码为空", Joiner.on(",").join(emptyAccidentCodeArray)));
        }

        if (repeatAccidentCodeArray.size() > 0) {
            throw new EpsException(String.format("存在重复事故编码:%s", Joiner.on(",").join(repeatAccidentCodeArray)));
        }

        if (unknownPushUnitArray.size() > 0) {
            throw new EpsException(String.format("存在报送单位不匹配:%s", Joiner.on(",").join(unknownPushUnitArray)));
        }

        for (Accident accident : accidentArray) {
            accidentService.save(accident);
        }
        return new EpsResponse().message(String.format("成功导入%s条数据", accidentArray.size()));
    }

    @GetMapping("downloadTemplate")
    @PreAuthorize("hasAuthority('accident:downloadTemplate')")
    @ControllerEndpoint(operation = "下载事故统计Excel数据模板", exceptionMessage = "下载事故统计Excel数据模板失败")
    public void downloadTemplate(HttpServletResponse response) throws IOException, EpsException {
        InputStream inputStream = this.getClass().getResourceAsStream(String.format("/template/%s%s", TEMPLATE_EXCEL_NAME, EXCEL_TYPE));
        response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.addHeader("charset", "utf-8");
        response.addHeader("Pragma", "no-cache");
        String encodeName = URLEncoder.encode(String.format("%s%s", TEMPLATE_EXCEL_NAME, EXCEL_TYPE), StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);


        ServletOutputStream servletOutputStream = response.getOutputStream();
        IOUtils.copy(inputStream, servletOutputStream);
        response.flushBuffer();
    }
}
