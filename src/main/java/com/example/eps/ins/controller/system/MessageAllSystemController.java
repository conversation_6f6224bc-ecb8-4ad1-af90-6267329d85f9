package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.dto.report.model.MessageAllVo;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.system.MessageAll;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.MessageAllService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Description: <消息通知表前端控制器>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/message/all")
@RequiredArgsConstructor
public class MessageAllSystemController {

    private final MessageAllService messageAllService;

    @PostMapping("list")
    public EpsResponse messageList(@RequestBody MessageAllVo dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.messageAllService.findMessages(dto));
        return new EpsResponse().data(dataTable);
    }


    @PostMapping("add")
    public void addMessage(@RequestBody MessageAll dto) throws EpsException {
        try {
            this.messageAllService.createMessage(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }

    @PostMapping("delete")
    public void deleteMessage(@RequestBody MessageAllVo dto) throws EpsException {
        try {
            this.messageAllService.deleteMessage(dto);
        } catch (Exception e) {
            String message = "删除Message失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    @PostMapping("detail")
    public EpsResponse detail(@RequestBody MessageAllVo dto) {
        return new EpsResponse().data(messageAllService.detail(dto.getId()));
    }
}