package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.dto.enterprise.EmergencydrillVO;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Emergencydrill;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IEmergencyDrillService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

/** 企业应急演练维护 @Author: <PERSON><PERSON>yin @Date: Created in 2022/10/11 9:46 @Version: 1.0 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/emergencyDrill")
@RequiredArgsConstructor
public class EmergencyDrillSystemController {
  private final IEmergencyDrillService emergencyDrillService;

  @GetMapping("list")
  public EpsResponse emergencyDrillList(QueryRequest request, Emergencydrill emergencyPlan) {
    Map<String, Object> dataTable =
        EpsUtil.getDataTable(
            this.emergencyDrillService.findEmergencyDrills(request, emergencyPlan));
    return new EpsResponse().data(dataTable);
  }

  @PostMapping("add")
  public void addEmergencyDrill(@Valid EmergencydrillVO dto) throws EpsException {
    try {
      this.emergencyDrillService.createEmergencyDrill(dto);
    } catch (Exception e) {
      String message;
      if (!ObjectUtils.isEmpty(dto.getId())) {
        message = "编辑应急演练失败";
      } else {
        message = "新增应急演练失败";
      }
      log.error(message, e);
      throw new EpsException(e.getMessage());
    }
  }

  @DeleteMapping("{id}")
  public void deleteEmergencyDrill(@NotNull @PathVariable Long id) throws EpsException {
    try {
      this.emergencyDrillService.deleteEmergencyDrill(id);
    } catch (Exception e) {
      String message = "删除应急演练失败";
      log.error(message, e);
      throw new EpsException(message);
    }
  }

  //    @PutMapping("update")
  //    public void updateMessage(EmergencyPlanVo dto) throws EpsException {
  //        try {
  //            this.emergencyPlanService.updateEmergencyPlan(dto);
  //        } catch (Exception e) {
  //            String message = "修改应急预案失败";
  //            log.error(message, e);
  //            throw new EpsException(message);
  //        }
  //    }
  /**
   * 删除文件
   *
   * @param filePath 文件路径
   */
  @DeleteMapping(value = "delete")
  @ResponseBody
  public void deleteFile(Long id,String filePath) {
    try {
      this.emergencyDrillService.deleteFile(id,filePath);
    } catch (Exception e) {
      String message = "删除文件失败";
      log.error(message, e);
      throw new EpsException(message);
    }
  }
  @GetMapping("{id}")
  public EpsResponse queryEmergencyDrill(@NotNull @PathVariable Long id) {
    return new EpsResponse().data(emergencyDrillService.findEmergencyDrill(id));
  }
}
