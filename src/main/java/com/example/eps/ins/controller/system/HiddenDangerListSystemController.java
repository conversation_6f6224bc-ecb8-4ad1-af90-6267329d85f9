package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.HiddenDangerList;
import com.example.eps.ins.common.entity.enterprise.HiddenDangerListInspect;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.HiddenDangerListService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 企业隐患排查制度维护
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 9:46
 * @Version: 1.0 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/hiddenDangerList")
@RequiredArgsConstructor
public class HiddenDangerListSystemController {
    private final HiddenDangerListService hiddenDangerListService;

    @GetMapping("list")
    public EpsResponse hiddenDangerList(QueryRequest request, HiddenDangerList hiddenDangerList) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.hiddenDangerListService.findHiddenDangerList(request,
                hiddenDangerList));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping("add")
    public void addHiddenDanger(@Valid HiddenDangerList dto) throws EpsException {
        try {
            this.hiddenDangerListService.createHiddenDangerList(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }
    @DeleteMapping("{id}")
    public void deleteHiddenDanger(@NotNull @PathVariable Long id) throws EpsException {
        try {
            this.hiddenDangerListService.deleteHiddenDangerList(id);
        } catch (Exception e) {
            String message = "删除应急预案失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
    @DeleteMapping(value = "delete")
    @ResponseBody
    public void deleteFile(Long id,String filePath) {
        try {
            this.hiddenDangerListService.deleteFile(id,filePath);
        } catch (Exception e) {
            String message = "删除文件失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    @GetMapping("{id}")
    public EpsResponse queryHiddenDangerList(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(hiddenDangerListService.findHiddenDangerList(id));
    }

    /**
     * 隐患处理详情
     * @param id
     * @return
     */
    @GetMapping("/inspect")
    public EpsResponse queryHiddenDangerListInspect(@NotNull Long id) {
        return new EpsResponse().data(hiddenDangerListService.findHiddenDangerListInspect(id));
    }

    /**
     * 隐患处理
     * @param dto
     * @throws EpsException
     */
    @PostMapping("/addInspect")
    public void addInspect(@Valid HiddenDangerListInspect dto) throws EpsException {
        try {
            this.hiddenDangerListService.addInspect(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }

    /**
     * 删除隐患整改文件
     *
     * @param filePath 文件路径
     */
    @DeleteMapping(value = "deleteInspect")
    @ResponseBody
    public void deleteInspectFile(Long id,String filePath) {
        try {
            this.hiddenDangerListService.deleteInspectFile(id,filePath);
        } catch (Exception e) {
            String message = "删除文件失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}
