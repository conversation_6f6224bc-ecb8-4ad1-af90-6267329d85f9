package com.example.eps.ins.controller.report;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.dto.report.req.CompanyView;
import com.example.eps.ins.common.dto.report.req.RiskListReq;
import com.example.eps.ins.service.NumberUnitsService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "dateAnalysis",description = "数据分析统计")
@Slf4j
@RestController
@RequestMapping("/report/unit")
@RequiredArgsConstructor
public class NumberUnitsController {

    private final NumberUnitsService numberUnitsService;

    /**
     * 获取所有地址
     * @return
     */
    @PostMapping("getRegion")
    public EpsResponse getRegion(){
        return new EpsResponse().data(numberUnitsService.getRegion());
    }
    /**
     * 单位总览
     * @param companyView
     * @return
     */
    @PostMapping("getView")
    public EpsResponse companyView(@RequestBody CompanyView companyView){
        return new EpsResponse().data(numberUnitsService.companyView(companyView));
    }

    /**
     * 行业分布
     * @param companyView
     * @return
     */
    @PostMapping("getIndustry")
    public EpsResponse getIndustry(@RequestBody CompanyView companyView){
        return new EpsResponse().data(numberUnitsService.getIndustry(companyView));
    }

    /**
     * 三类重点企业分布
     * @param companyView
     * @return
     */
    @PostMapping("getThreeStress")
    public EpsResponse getThreeStress(@RequestBody CompanyView companyView){
        return new EpsResponse().data(numberUnitsService.getThreeStress(companyView));
    }

    /**
     * 风险情况统计
     * @param riskListReq
     * @return
     */
    @PostMapping("riskList")
    public EpsResponse riskList(@RequestBody RiskListReq riskListReq){
        return new EpsResponse().data(numberUnitsService.riskList(riskListReq));
    }
}
