package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IRegionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 地区表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:39
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/region")
@RequiredArgsConstructor
public class RegionController {

    private final IRegionService regionService;

    @GetMapping("all")
    public EpsResponse getAllRegions() {
        return new EpsResponse().data(regionService.getAllRegions());
    }

    @GetMapping("list")
    public EpsResponse regionList(QueryRequest request, Region region) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.regionService.findRegions(request, region));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping
    public void addRegion(@Valid Region region) throws EpsException {
        try {
            this.regionService.createRegion(region);
        } catch (Exception e) {
            String message = "新增Region失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping
    public void deleteRegion(Region region) throws EpsException {
        try {
            this.regionService.deleteRegion(region);
        } catch (Exception e) {
            String message = "删除Region失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
    public void updateRegion(Region region) throws EpsException {
        try {
            this.regionService.updateRegion(region);
        } catch (Exception e) {
            String message = "修改Region失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}
