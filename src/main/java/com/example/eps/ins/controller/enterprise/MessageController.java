package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.Message;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.MessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/31 16:57
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/message")
@RequiredArgsConstructor
public class MessageController{

    private final MessageService messageService;

    @GetMapping
    public EpsResponse messageList(QueryRequest request, Message dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.messageService.findMessages(request, dto));
        return new EpsResponse().data(dataTable);
    }

    @GetMapping("{id}")
    public EpsResponse queryMessage(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(messageService.findMessage(id));

    }

    @GetMapping("noSee")
    public EpsResponse queryNoSee() {
        return new EpsResponse().data(messageService.findNoSee());

    }

    @GetMapping("early")
    public EpsResponse earlyWarningByUser() {
        return new EpsResponse().data(this.messageService.earlyWarningByUser());
    }

    @GetMapping("warning")
    public EpsResponse warningByUser() {
        return new EpsResponse().data(this.messageService.warningByUser());
    }
}
