package com.example.eps.ins.controller.report;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.dto.report.req.AreaReq;
import com.example.eps.ins.common.dto.report.req.CompanyView;
import com.example.eps.ins.service.AccidentReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/report/accident")
@RequiredArgsConstructor
public class AccidentReportController {
    private final AccidentReportService accidentReportService;

    /**
     *事故数据总览
     */
    @PostMapping("all")
    public EpsResponse getAll(@RequestBody CompanyView companyView){
        return new EpsResponse().data(accidentReportService.getAll(companyView.getRegionId(),companyView.getYear()));
    }

    /**
     *行业事故统计
     */
    @PostMapping("industry")
    public EpsResponse getIndustry(@RequestBody CompanyView companyView){
        return new EpsResponse().data(accidentReportService.getIndustry(companyView));
    }

    /**
     *事故类型排序
     */
    @PostMapping("sort")
    public EpsResponse getSort(@RequestBody CompanyView companyView){
        return new EpsResponse().data(accidentReportService.getSort(companyView));
    }

    /**
     * 区县事故统计
     */
    @PostMapping("area")
    public EpsResponse getArea(@RequestBody AreaReq areaReq){
        return new EpsResponse().data(accidentReportService.getArea(areaReq));
    }
}
