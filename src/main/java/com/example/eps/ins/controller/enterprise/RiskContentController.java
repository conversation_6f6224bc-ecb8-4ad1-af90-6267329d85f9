package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.dto.report.model.RiskContentVo;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.enterprise.RiskContent;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.RiskContentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/6/10 11:18
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/risk/content")
@RequiredArgsConstructor
public class RiskContentController {

    private final RiskContentService riskContentService;

    @PostMapping("getContent")
    public EpsResponse messageList(@RequestBody RiskContentVo dto) {
        List<RiskContent> list = this.riskContentService.getList(dto);
        return new EpsResponse().data(list);
    }

    @PostMapping("add")
    public void addTrainEducation(@RequestBody RiskContentVo dto) throws EpsException {
        try {
            this.riskContentService.add(dto);
        } catch (Exception e) {
            String message = "新增重大隐患失败";
            log.error(message, e);
            throw new EpsException(e.getMessage());
        }
    }
    @PostMapping("getList")
    public EpsResponse getList(@RequestBody RiskContentVo dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.riskContentService.getAllList(dto));
        return new EpsResponse().data(dataTable);
    }
    @PostMapping("delete")
    public void deleteTrainEducation(@RequestBody RiskContentVo dto) throws EpsException {
        try {
            this.riskContentService.delete(dto.getId());
        } catch (Exception e) {
            String message = "删除重大隐患失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}
