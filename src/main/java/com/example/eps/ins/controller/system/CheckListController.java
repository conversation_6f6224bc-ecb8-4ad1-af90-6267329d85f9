//package com.example.eps.ins.controller.system;
//
//import com.example.eps.ins.common.bean.CheckListPageRequest;
//import com.example.eps.ins.common.bean.CheckListRequest;
//import com.example.eps.ins.common.entity.EpsResponse;
//import com.example.eps.ins.common.exception.EpsException;
//import com.example.eps.ins.common.utils.EpsUtil;
//import com.example.eps.ins.common.annotation.ControllerEndpoint;
//import com.example.eps.ins.service.ICheckListService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import javax.validation.constraints.NotNull;
//import java.util.Map;
//
///**
//* @Description: <检查清单表前端控制器>
//* @Author: utopia
//* @CreateDate: 2021-11-09
//*/
//@Slf4j
//@Validated
//@RestController
//@RequestMapping("/system/checkList")
//@RequiredArgsConstructor
//public class CheckListController {
//
//    private final ICheckListService checkListServiceImpl;
//
//    @GetMapping
//    @PreAuthorize("hasAuthority('checkList:view')")
//    public EpsResponse checkListList(CheckListPageRequest dto) {
//        Map<String, Object> dataTable = EpsUtil.getDataTable(this.checkListServiceImpl.listByPage(dto));
//        return new EpsResponse().data(dataTable);
//    }
//
//    @GetMapping("illegalTypeList")
//    @PreAuthorize("hasAuthority('checkList:view')")
//    public EpsResponse illegalTypeList(CheckListPageRequest dto) {
//        return new EpsResponse().data(this.checkListServiceImpl.findCheckListIllegal(dto));
//    }
//    @GetMapping("list")
//    @PreAuthorize("hasAuthority('checkList:view')")
//    public EpsResponse list(CheckListPageRequest dto) {
//        return new EpsResponse().data(this.checkListServiceImpl.findCheckLists(dto));
//    }
//
//    @GetMapping("{id}")
//    @PreAuthorize("hasAuthority('checkList:get')")
//    public EpsResponse queryCheckList(@NotNull @PathVariable Long id) {
//        return new EpsResponse().data(checkListServiceImpl.findCheckList(id));
//    }
//
//    @PostMapping
//    @PreAuthorize("hasAuthority('checkList:add')")
//    @ControllerEndpoint(operation = "新增违法清单", exceptionMessage = "新增违法清单失败")
//    public void addCheckList(@RequestBody @Valid CheckListRequest dto) throws EpsException {
//        this.checkListServiceImpl.createCheckList(dto);
//    }
//
//
//    @PostMapping("{checkListId}/enable")
//    @PreAuthorize("hasAuthority('checkList:update')")
//    @ControllerEndpoint(operation = "启用/禁用违法清单", exceptionMessage = "启用/禁用违法清单失败")
//    public void updateStatus(@NotNull @PathVariable(value = "checkListId") Long checkListId) throws EpsException {
//        this.checkListServiceImpl.updateStatus(checkListId);
//    }
//
//    @PutMapping
//    @PreAuthorize("hasAuthority('checkList:update')")
//    @ControllerEndpoint(operation = "修改违法清单", exceptionMessage = "修改违法清单失败")
//    public void updateCheckList(@RequestBody CheckListRequest dto) throws EpsException {
//        this.checkListServiceImpl.updateCheckList(dto);
//    }
//}