package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.bean.EnterpriseDeclarationRequest;
import com.example.eps.ins.common.bean.EnterpriseDeclarationResponse;
import com.example.eps.ins.common.entity.CreatedResponse;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.service.IEnterpriseDeclarationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_SAVE;
import static com.example.eps.ins.common.constant.EnterpriseDeclarationConstant.ENTERPRISE_DECLARATION_STATUS_WAIT_REVIEW;

/**
 * 企业信息申报表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/enterpriseDeclaration")
@RequiredArgsConstructor
public class EnterpriseDeclarationController {

    private final IEnterpriseDeclarationService enterpriseDeclarationService;

    @GetMapping("/currentUser")
    public EpsResponse currentUserEnterpriseDeclaration() {
        EnterpriseDeclarationResponse enterpriseDeclaration = this.
                enterpriseDeclarationService.currentUserEnterpriseDeclaration();
        return new EpsResponse().data(enterpriseDeclaration);
    }

    @PostMapping("save")
    public EpsResponse saveEnterpriseDeclaration(@RequestBody EnterpriseDeclarationRequest enterpriseDeclarationRequest) throws EpsException {
        enterpriseDeclarationRequest.setStatus(ENTERPRISE_DECLARATION_STATUS_SAVE);
        Long id = this.enterpriseDeclarationService.saveEnterpriseDeclaration(enterpriseDeclarationRequest);
        CreatedResponse response = new CreatedResponse();
        response.setId(id);
        return new EpsResponse().data(response);
    }

    @PostMapping
    public EpsResponse addEnterpriseDeclaration(@RequestBody @Valid EnterpriseDeclarationRequest enterpriseDeclarationRequest) throws EpsException {
        enterpriseDeclarationRequest.setStatus(ENTERPRISE_DECLARATION_STATUS_WAIT_REVIEW);
        Long id = this.enterpriseDeclarationService.createEnterpriseDeclaration(enterpriseDeclarationRequest);
        CreatedResponse response = new CreatedResponse();
        response.setId(id);
        return new EpsResponse().data(response);
    }
}
