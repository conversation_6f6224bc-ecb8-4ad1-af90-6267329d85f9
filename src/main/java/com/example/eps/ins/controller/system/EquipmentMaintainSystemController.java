package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.EquipmentMaintain;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.EquipmentMaintainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 *
 * 企业设施设备维护保养记录
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 9:46
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/equipmentMaintain")
@RequiredArgsConstructor
public class EquipmentMaintainSystemController {
    private final EquipmentMaintainService emergencyPlanService;

    @GetMapping("list")
    public EpsResponse equipmentMaintainList(QueryRequest request, EquipmentMaintain emergencyPlan) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.emergencyPlanService.findEquipmentMaintains(request,
                emergencyPlan));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping("add")
    public void addEquipmentMaintain(@Valid EquipmentMaintain dto) throws EpsException {
        try {
            this.emergencyPlanService.createEquipmentMaintain(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }
    @DeleteMapping("{id}")
    public void deleteEquipmentMaintain(@NotNull @PathVariable Long id) throws EpsException {
        try {
            this.emergencyPlanService.deleteEquipmentMaintain(id);
        } catch (Exception e) {
            String message = "删除企业设施设备维护保养记录失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
    @DeleteMapping(value = "delete")
    @ResponseBody
    public void deleteFile(Long id,String filePath) {
        try {
            this.emergencyPlanService.deleteFile(id,filePath);
        } catch (Exception e) {
            String message = "删除文件失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    @GetMapping("{id}")
    public EpsResponse queryEmergencyPlan(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(emergencyPlanService.findEquipmentMaintain(id));
    }
}
