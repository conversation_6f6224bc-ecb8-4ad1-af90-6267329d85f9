package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.service.IIndustryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 行业表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:40
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/industry")
@RequiredArgsConstructor
public class IndustrySystemController {

    private final IIndustryService industryService;

    @GetMapping
    public EpsResponse getAllIndustries() {
        return new EpsResponse().data(industryService.findIndustries());
    }
}
