package com.example.eps.ins.controller.device;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.eps.ins.common.dto.device.DeviceBindingRequest;
import com.example.eps.ins.common.dto.device.DeviceBindingResponse;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.device.EnterpriseDeviceBinding;
import com.example.eps.ins.service.IEnterpriseDeviceBindingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 企业设备绑定管理控制器
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@RestController
@RequestMapping("/enterpriseDeviceBinding")
@RequiredArgsConstructor
@Validated
public class EnterpriseDeviceBindingController {

    private final IEnterpriseDeviceBindingService bindingService;

    /**
     * 绑定设备到企业
     *
     * @param request 绑定请求
     * @return 绑定结果
     */
    @PostMapping("/bind")
    public EpsResponse bindDevice(@Valid @RequestBody DeviceBindingRequest request) {
        log.info("开始绑定设备到企业，请求参数: {}", request);

        try {
            // TODO: 从当前登录用户获取创建人ID，这里暂时使用固定值
            Long creatorId = 1L; // 实际应该从SecurityContext或Session中获取

            EnterpriseDeviceBinding binding = bindingService.bindDevice(request, creatorId);

            return new EpsResponse()
                    .put("success", true)
                    .put("bindingId", binding.getId())
                    .data(binding)
                    .message("设备绑定成功");

        } catch (Exception e) {
            log.error("绑定设备到企业失败", e);
            return new EpsResponse()
                    .put("success", false)
                    .message("设备绑定失败: " + e.getMessage());
        }
    }

    /**
     * 解绑设备
     *
     * @param bindingId 绑定关系ID
     * @return 解绑结果
     */
    @PutMapping("/unbind/{bindingId}")
    public EpsResponse unbindDevice(@PathVariable Long bindingId) {
        log.info("开始解绑设备，绑定ID: {}", bindingId);

        try {
            boolean result = bindingService.unbindDevice(bindingId);

            if (result) {
                return new EpsResponse()
                        .put("success", true)
                        .message("设备解绑成功");
            } else {
                return new EpsResponse()
                        .put("success", false)
                        .message("设备解绑失败");
            }

        } catch (Exception e) {
            log.error("解绑设备失败，绑定ID: {}", bindingId, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("设备解绑失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询绑定关系列表（带详细信息）
     *
     * @param request 分页请求
     * @param binding 查询条件
     * @return 绑定关系分页列表
     */
    @GetMapping("/list")
    public EpsResponse getBindingList(QueryRequest request, EnterpriseDeviceBinding binding) {
        log.info("开始查询绑定关系列表，查询条件: {}", binding);

        try {
            IPage<DeviceBindingResponse> bindingPage = bindingService.findBindingsWithDetails(request, binding);

            return new EpsResponse()
                    .put("success", true)
                    .put("total", bindingPage.getTotal())
                    .put("current", bindingPage.getCurrent())
                    .put("size", bindingPage.getSize())
                    .data(bindingPage.getRecords())
                    .message("绑定关系列表查询成功");

        } catch (Exception e) {
            log.error("查询绑定关系列表失败", e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询绑定关系列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据企业ID查询绑定的设备列表
     *
     * @param enterpriseId 企业ID
     * @return 绑定设备列表
     */
    @GetMapping("/listByEnterprise/{enterpriseId}")
    public EpsResponse getBindingsByEnterpriseId(@PathVariable Long enterpriseId) {
        log.info("开始查询企业ID: {} 的绑定设备列表", enterpriseId);

        try {
            List<EnterpriseDeviceBinding> bindings = bindingService.findByEnterpriseId(enterpriseId);

            return new EpsResponse()
                    .put("success", true)
                    .put("count", bindings.size())
                    .put("enterpriseId", enterpriseId)
                    .data(bindings)
                    .message("企业绑定设备列表查询成功");

        } catch (Exception e) {
            log.error("查询企业ID: {} 的绑定设备列表失败", enterpriseId, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询企业绑定设备列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据企业ID和绑定状态查询设备列表
     *
     * @param enterpriseId 企业ID
     * @param bindingStatus 绑定状态
     * @return 设备列表
     */
    @GetMapping("/listByEnterpriseAndStatus/{enterpriseId}/{bindingStatus}")
    public EpsResponse getBindingsByEnterpriseIdAndStatus(@PathVariable Long enterpriseId, @PathVariable Integer bindingStatus) {
        log.info("开始查询企业ID: {} 绑定状态: {} 的设备列表", enterpriseId, bindingStatus);

        try {
            List<EnterpriseDeviceBinding> bindings = bindingService.findByEnterpriseIdAndStatus(enterpriseId, bindingStatus);

            String statusDesc = getBindingStatusDesc(bindingStatus);

            return new EpsResponse()
                    .put("success", true)
                    .put("count", bindings.size())
                    .put("enterpriseId", enterpriseId)
                    .put("bindingStatus", bindingStatus)
                    .put("bindingStatusDesc", statusDesc)
                    .data(bindings)
                    .message("企业设备列表查询成功");

        } catch (Exception e) {
            log.error("查询企业ID: {} 绑定状态: {} 的设备列表失败", enterpriseId, bindingStatus, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询企业设备列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据外部设备ID查询绑定关系列表
     *
     * @param externalDeviceId 外部设备ID
     * @return 绑定关系列表
     */
    @GetMapping("/listByExternalDevice/{externalDeviceId}")
    public EpsResponse getBindingsByExternalDeviceId(@PathVariable Long externalDeviceId) {
        log.info("开始查询外部设备ID: {} 的绑定关系列表", externalDeviceId);

        try {
            List<EnterpriseDeviceBinding> bindings = bindingService.findByExternalDeviceId(externalDeviceId);

            return new EpsResponse()
                    .put("success", true)
                    .put("count", bindings.size())
                    .put("externalDeviceId", externalDeviceId)
                    .data(bindings)
                    .message("设备绑定关系列表查询成功");

        } catch (Exception e) {
            log.error("查询外部设备ID: {} 的绑定关系列表失败", externalDeviceId, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询设备绑定关系列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据绑定关系ID查询详情
     *
     * @param id 绑定关系ID
     * @return 绑定关系详情
     */
    @GetMapping("/{id}")
    public EpsResponse getBindingById(@PathVariable Long id) {
        log.info("开始查询绑定关系详情，ID: {}", id);

        try {
            EnterpriseDeviceBinding binding = bindingService.getById(id);

            if (binding != null) {
                return new EpsResponse()
                        .put("success", true)
                        .data(binding)
                        .message("绑定关系详情查询成功");
            } else {
                return new EpsResponse()
                        .put("success", false)
                        .message("绑定关系不存在");
            }

        } catch (Exception e) {
            log.error("查询绑定关系详情失败，ID: {}", id, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("查询绑定关系详情失败: " + e.getMessage());
        }
    }

    /**
     * 统计企业绑定设备数量
     *
     * @param enterpriseId 企业ID
     * @return 绑定设备数量
     */
    @GetMapping("/count/{enterpriseId}")
    public EpsResponse countByEnterpriseId(@PathVariable Long enterpriseId) {
        log.info("开始统计企业ID: {} 的绑定设备数量", enterpriseId);

        try {
            int count = bindingService.countByEnterpriseId(enterpriseId);

            return new EpsResponse()
                    .put("success", true)
                    .put("enterpriseId", enterpriseId)
                    .put("count", count)
                    .message("企业绑定设备数量统计成功");

        } catch (Exception e) {
            log.error("统计企业ID: {} 的绑定设备数量失败", enterpriseId, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("统计企业绑定设备数量失败: " + e.getMessage());
        }
    }

    /**
     * 更新绑定状态
     *
     * @param id 绑定关系ID
     * @param bindingStatus 绑定状态
     * @return 更新结果
     */
    @PutMapping("/{id}/status/{bindingStatus}")
    public EpsResponse updateBindingStatus(@PathVariable Long id, @PathVariable Integer bindingStatus) {
        log.info("开始更新绑定状态，ID: {}, 绑定状态: {}", id, bindingStatus);

        try {
            boolean result = bindingService.updateBindingStatus(id, bindingStatus);

            if (result) {
                return new EpsResponse()
                        .put("success", true)
                        .message("绑定状态更新成功");
            } else {
                return new EpsResponse()
                        .put("success", false)
                        .message("绑定状态更新失败");
            }

        } catch (Exception e) {
            log.error("更新绑定状态失败，ID: {}", id, e);
            return new EpsResponse()
                    .put("success", false)
                    .message("更新绑定状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取绑定状态描述
     */
    private String getBindingStatusDesc(Integer bindingStatus) {
        switch (bindingStatus) {
            case 0: return "未绑定";
            case 1: return "已绑定";
            case 2: return "已解绑";
            default: return "未知状态";
        }
    }
}
