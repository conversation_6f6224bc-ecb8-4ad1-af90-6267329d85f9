package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.service.FdfsClientService;
import com.example.eps.ins.service.IAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/enterprise/attachment")
@Slf4j
public class AttachmentController {
    @Autowired
    IAttachmentService attachmentService;
    @Autowired
    FdfsClientService fdfsClientService;

//    @GetMapping("/{filePath}")
//    public void download(@PathVariable String filePath, HttpServletResponse resp) throws IOException {
//        File file = attachmentService.download(filePath);
//        resp.setCharacterEncoding("utf-8");
//        resp.setHeader("content-type", "application/octet-stream");
//        if(file.getName().endsWith(".jpg")){
//            resp.setContentType(MediaType.IMAGE_PNG_VALUE);
//        }else{
//            resp.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
//        }
//
//        resp.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode(file.getName(), "utf-8"));
//        PrintWriter os = null;
//        Reader is = null;
//        try {
//            os = resp.getWriter();
//            is = new BufferedReader(new FileReader(file));
//            int len=0;
//            char[] buffer = new char[200];
//            while((len=is.read(buffer))!=-1){
//                os.print(new String(buffer,0,len));
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }finally {
//            if(is != null){
//                is.close();
//            }
//            if(os != null){
//                os.close();
//            }
//        }
//
//    }

    @GetMapping()
    public ResponseEntity<byte[]> download(@RequestParam String filePath) {
        return new ResponseEntity<>(fdfsClientService.downloadFile(filePath), HttpStatus.CREATED);
    }
}
