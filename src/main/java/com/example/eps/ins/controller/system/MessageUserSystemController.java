package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IMessageUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * @Description: <消息用户关联表前端控制器>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/MessageUser")
@RequiredArgsConstructor
public class MessageUserSystemController {

    private final IMessageUserService iMessageUserService;

    @GetMapping("list")
    public EpsResponse MessageUserList(QueryRequest request, MessageUser dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.iMessageUserService.findMessageUsers(request, dto));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping
    public void addMessageUser(@Valid MessageUser dto) throws EpsException {
        try {
            this.iMessageUserService.createMessageUser(dto);
        } catch (Exception e) {
            String message = "新增MessageUser失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping
    public void deleteMessageUser(MessageUser dto) throws EpsException {
        try {
            this.iMessageUserService.deleteMessageUser(dto);
        } catch (Exception e) {
            String message = "删除MessageUser失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
    public void updateMessageUser(MessageUser dto) throws EpsException {
        try {
            this.iMessageUserService.updateMessageUser(dto);
        } catch (Exception e) {
            String message = "修改MessageUser失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}