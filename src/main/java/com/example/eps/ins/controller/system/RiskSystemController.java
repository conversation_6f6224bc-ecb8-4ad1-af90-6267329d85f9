package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.Risk;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IRiskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 风险类型表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/risk")
@RequiredArgsConstructor
public class RiskSystemController {

    private final IRiskService riskService;

    @GetMapping
    public EpsResponse getAllRisks(@RequestParam(defaultValue = "0")Long parentId, @RequestParam(defaultValue = "true")Boolean queryAll) {
        return new EpsResponse().data(riskService.findRisksSystem(parentId,queryAll));
    }

    @GetMapping("list")
    @PreAuthorize("hasAuthority('risk:list')")
    public EpsResponse riskList(QueryRequest request, Risk risk) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.riskService.findRisks(request, risk));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping
    @PreAuthorize("hasAuthority('risk:add')")
    public void addRisk(@Valid Risk risk) throws EpsException {
        try {
            this.riskService.createRisk(risk);
        } catch (Exception e) {
            String message = "新增Risk失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping
    @PreAuthorize("hasAuthority('risk:delete')")
    public void deleteRisk(Risk risk) throws EpsException {
        try {
            this.riskService.deleteRisk(risk);
        } catch (Exception e) {
            String message = "删除Risk失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
    @PreAuthorize("hasAuthority('risk:update')")
    public void updateRisk(Risk risk) throws EpsException {
        try {
            this.riskService.updateRisk(risk);
        } catch (Exception e) {
            String message = "修改Risk失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}
