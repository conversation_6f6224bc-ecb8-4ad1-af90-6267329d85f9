package com.example.eps.ins.controller.device;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.eps.ins.common.dto.device.DeviceApiResponse;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.device.Device;
import com.example.eps.ins.common.utils.HttpClientUtil;
import com.example.eps.ins.common.utils.Response;
import com.example.eps.ins.service.IDeviceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Slf4j
@RestController
@RequestMapping("/device")
@RequiredArgsConstructor
public class DeviceController {

    private final IDeviceService deviceService;

    private static final String DEVICE_API_URL = "http://**************:8080/api/v3/openapi/devices";
    private static final String APP_KEY = "7490fc43-411c-4d3c-9c10-d2ce774f45cc";

    /**
     * 查询设备列表并自动入库
     *
     * @param pageIndex 页面大小 (可选)
     * @param pageSize 当前页 (可选)
     * @param autoSync 是否自动同步到数据库 (可选，默认true)
     * @return 设备列表响应
     */
    @GetMapping("/list")
    public EpsResponse getDeviceList(
            @RequestParam(value = "pageIndex", required = false) Integer pageIndex,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "autoSync", required = false, defaultValue = "true") Boolean autoSync) {

        log.info("开始调用设备列表API，pageIndex: {}, pageSize: {}, autoSync: {}", pageIndex, pageSize, autoSync);

        try {
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            if (pageIndex != null) {
                params.put("pageIndex", pageIndex.toString());
            }
            if (pageSize != null) {
                params.put("pageSize", pageSize.toString());
            }

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("appKey", APP_KEY);
            headers.put("Content-Type", "application/json");

            // 发送HTTP GET请求
            Response response = HttpClientUtil.doGet(DEVICE_API_URL, params, headers);

            // 打印返回值
            log.info("设备列表API响应状态码: {}", response.getStatusCode());
            log.info("设备列表API响应内容: {}", response.getResult());

            // 检查响应状态
            if (response.getStatusCode() == 200) {
                try {
                    // 解析JSON响应
                    DeviceApiResponse deviceApiResponse = JSON.parseObject(response.getResult(), DeviceApiResponse.class);

                    // 构建基础响应
                    EpsResponse epsResponse = new EpsResponse()
                            .put("success", true)
                            .put("statusCode", response.getStatusCode())
                            .put("apiStatus", deviceApiResponse.getStatus())
                            .put("pageInfo", createPageInfoMap(
                                    deviceApiResponse.getContent().getPageIndex(),
                                    deviceApiResponse.getContent().getPageSize(),
                                    deviceApiResponse.getContent().getTotal()
                            ))
                            .data(deviceApiResponse.getContent().getResult());

                    // 如果启用自动同步，则将设备信息同步到数据库
                    if (autoSync && deviceApiResponse.getContent() != null &&
                        deviceApiResponse.getContent().getResult() != null &&
                        !deviceApiResponse.getContent().getResult().isEmpty()) {

                        try {
                            log.info("开始自动同步设备信息到数据库，设备数量: {}",
                                    deviceApiResponse.getContent().getResult().size());

                            boolean syncResult = deviceService.syncDevices(deviceApiResponse.getContent().getResult());

                            if (syncResult) {
                                epsResponse.put("syncStatus", "success")
                                          .put("syncCount", deviceApiResponse.getContent().getResult().size())
                                          .message("设备列表查询成功，已自动同步到数据库");
                                log.info("设备信息自动同步成功，同步数量: {}",
                                        deviceApiResponse.getContent().getResult().size());
                            } else {
                                epsResponse.put("syncStatus", "failed")
                                          .put("syncCount", 0)
                                          .message("设备列表查询成功，但自动同步到数据库失败");
                                log.warn("设备信息自动同步失败");
                            }
                        } catch (Exception syncException) {
                            log.error("设备信息自动同步异常", syncException);
                            epsResponse.put("syncStatus", "error")
                                      .put("syncError", syncException.getMessage())
                                      .put("syncCount", 0)
                                      .message("设备列表查询成功，但自动同步过程中发生异常");
                        }
                    } else {
                        epsResponse.put("syncStatus", autoSync ? "no_data" : "disabled")
                                  .message(autoSync ? "设备列表查询成功，无数据需要同步" : "设备列表查询成功");
                    }

                    return epsResponse;

                } catch (Exception parseException) {
                    log.error("解析设备API响应失败", parseException);
                    return new EpsResponse()
                            .put("success", true)
                            .put("statusCode", response.getStatusCode())
                            .put("syncStatus", "parse_error")
                            .data(response.getResult())
                            .message("设备列表查询成功，但解析响应失败");
                }
            } else {
                return new EpsResponse()
                        .put("success", false)
                        .put("statusCode", response.getStatusCode())
                        .put("syncStatus", "api_error")
                        .data(response.getResult())
                        .message("设备列表查询失败");
            }

        } catch (Exception e) {
            log.error("调用设备列表API异常", e);
            return new EpsResponse()
                    .put("success", false)
                    .put("syncStatus", "exception")
                    .put("error", e.getMessage())
                    .message("设备列表查询异常");
        }
    }
    /**
     * 查询设备列表并自动入库
     *
     * @param pageIndex 页面大小 (可选)
     * @param pageSize 当前页 (可选)
     * @param autoSync 是否自动同步到数据库 (可选，默认true)
     * @return 设备列表响应
     */
    @GetMapping("/listById")
    public EpsResponse getDeviceById(
            @RequestParam(value = "pageIndex", required = false) Integer pageIndex,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "autoSync", required = false, defaultValue = "true") Boolean autoSync) {

        log.info("开始调用设备列表API，pageIndex: {}, pageSize: {}, autoSync: {}", pageIndex, pageSize, autoSync);

        try {
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            if (pageIndex != null) {
                params.put("pageIndex", pageIndex.toString());
            }
            if (pageSize != null) {
                params.put("pageSize", pageSize.toString());
            }

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("appKey", APP_KEY);
            headers.put("Content-Type", "application/json");

            // 发送HTTP GET请求
            Response response = HttpClientUtil.doGet(DEVICE_API_URL+"/1940708612932972546", params, headers);

            // 打印返回值
            log.info("设备列表API响应状态码: {}", response.getStatusCode());
            log.info("设备列表API响应内容: {}", response.getResult());

            // 检查响应状态
            if (response.getStatusCode() == 200) {
                try {
                    // 解析JSON响应
                    DeviceApiResponse deviceApiResponse = JSON.parseObject(response.getResult(), DeviceApiResponse.class);

                    // 构建基础响应
                    EpsResponse epsResponse = new EpsResponse()
                            .put("success", true)
                            .put("statusCode", response.getStatusCode())
                            .put("apiStatus", deviceApiResponse.getStatus())
                            .put("pageInfo", createPageInfoMap(
                                    deviceApiResponse.getContent().getPageIndex(),
                                    deviceApiResponse.getContent().getPageSize(),
                                    deviceApiResponse.getContent().getTotal()
                            ))
                            .data(deviceApiResponse.getContent().getResult());

                    // 如果启用自动同步，则将设备信息同步到数据库
                    if (autoSync && deviceApiResponse.getContent() != null &&
                            deviceApiResponse.getContent().getResult() != null &&
                            !deviceApiResponse.getContent().getResult().isEmpty()) {

                        try {
                            log.info("开始自动同步设备信息到数据库，设备数量: {}",
                                    deviceApiResponse.getContent().getResult().size());

                            boolean syncResult = deviceService.syncDevices(deviceApiResponse.getContent().getResult());

                            if (syncResult) {
                                epsResponse.put("syncStatus", "success")
                                        .put("syncCount", deviceApiResponse.getContent().getResult().size())
                                        .message("设备列表查询成功，已自动同步到数据库");
                                log.info("设备信息自动同步成功，同步数量: {}",
                                        deviceApiResponse.getContent().getResult().size());
                            } else {
                                epsResponse.put("syncStatus", "failed")
                                        .put("syncCount", 0)
                                        .message("设备列表查询成功，但自动同步到数据库失败");
                                log.warn("设备信息自动同步失败");
                            }
                        } catch (Exception syncException) {
                            log.error("设备信息自动同步异常", syncException);
                            epsResponse.put("syncStatus", "error")
                                    .put("syncError", syncException.getMessage())
                                    .put("syncCount", 0)
                                    .message("设备列表查询成功，但自动同步过程中发生异常");
                        }
                    } else {
                        epsResponse.put("syncStatus", autoSync ? "no_data" : "disabled")
                                .message(autoSync ? "设备列表查询成功，无数据需要同步" : "设备列表查询成功");
                    }

                    return epsResponse;

                } catch (Exception parseException) {
                    log.error("解析设备API响应失败", parseException);
                    return new EpsResponse()
                            .put("success", true)
                            .put("statusCode", response.getStatusCode())
                            .put("syncStatus", "parse_error")
                            .data(response.getResult())
                            .message("设备列表查询成功，但解析响应失败");
                }
            } else {
                return new EpsResponse()
                        .put("success", false)
                        .put("statusCode", response.getStatusCode())
                        .put("syncStatus", "api_error")
                        .data(response.getResult())
                        .message("设备列表查询失败");
            }

        } catch (Exception e) {
            log.error("调用设备列表API异常", e);
            return new EpsResponse()
                    .put("success", false)
                    .put("syncStatus", "exception")
                    .put("error", e.getMessage())
                    .message("设备列表查询异常");
        }
    }

    /**
     * 测试设备API调用（简化版本，直接返回原始响应）
     *
     * @return 原始API响应
     */
    @GetMapping("/test")
    public EpsResponse testDeviceApi() {
        log.info("开始测试设备API调用");

        try {
            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("appKey", APP_KEY);
            headers.put("Content-Type", "application/json");

            // 发送HTTP GET请求（不带分页参数）
            Response response = HttpClientUtil.doGet(DEVICE_API_URL, null, headers);

            // 打印返回值
            log.info("=== 设备API测试响应 ===");
            log.info("状态码: {}", response.getStatusCode());
            log.info("响应内容: {}", response.getResult());
            log.info("========================");

            return new EpsResponse()
                    .put("success", response.getStatusCode() == 200)
                    .put("statusCode", response.getStatusCode())
                    .put("rawResponse", response.getResult())
                    .message("设备API测试完成");

        } catch (Exception e) {
            log.error("测试设备API调用异常", e);
            return new EpsResponse()
                    .put("success", false)
                    .put("error", e.getMessage())
                    .message("设备API测试异常");
        }
    }

    /**
     * 创建分页信息Map（兼容Java 8）
     *
     * @param pageIndex 页码
     * @param pageSize 页大小
     * @param total 总数
     * @return 分页信息Map
     */
    private Map<String, Object> createPageInfoMap(Integer pageIndex, Integer pageSize, Integer total) {
        Map<String, Object> pageInfo = new HashMap<>();
        pageInfo.put("pageIndex", pageIndex);
        pageInfo.put("pageSize", pageSize);
        pageInfo.put("total", total);
        return pageInfo;
    }

    /**
     * 同步设备信息到数据库
     *
     * @param pageIndex 页面大小 (可选)
     * @param pageSize 当前页 (可选)
     * @return 同步结果
     */
    @PostMapping("/sync")
    public EpsResponse syncDevicesToDatabase(
            @RequestParam(value = "pageIndex", required = false) Integer pageIndex,
            @RequestParam(value = "pageSize", required = false) Integer pageSize) {

        log.info("开始同步设备信息到数据库，pageIndex: {}, pageSize: {}", pageIndex, pageSize);

        try {
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            if (pageIndex != null) {
                params.put("pageIndex", pageIndex.toString());
            }
            if (pageSize != null) {
                params.put("pageSize", pageSize.toString());
            }

            // 构建请求头
            Map<String, String> headers = new HashMap<>();
            headers.put("appKey", APP_KEY);
            headers.put("Content-Type", "application/json");

            // 发送HTTP GET请求
            Response response = HttpClientUtil.doGet(DEVICE_API_URL, params, headers);

            // 打印返回值
            log.info("设备API响应状态码: {}", response.getStatusCode());
            log.info("设备API响应内容: {}", response.getResult());

            if (response.getStatusCode() == 200) {
                try {
                    // 解析JSON响应
                    DeviceApiResponse deviceApiResponse = JSON.parseObject(response.getResult(), DeviceApiResponse.class);

                    // 同步设备信息到数据库
                    boolean syncResult = deviceService.syncDevices(deviceApiResponse.getContent().getResult());

                    if (syncResult) {
                        return new EpsResponse()
                                .put("success", true)
                                .put("syncCount", deviceApiResponse.getContent().getResult().size())
                                .put("pageInfo", createPageInfoMap(
                                        deviceApiResponse.getContent().getPageIndex(),
                                        deviceApiResponse.getContent().getPageSize(),
                                        deviceApiResponse.getContent().getTotal()
                                ))
                                .data(deviceApiResponse.getContent().getResult())
                                .message("设备信息同步成功");
                    } else {
                        return new EpsResponse()
                                .put("success", false)
                                .message("设备信息同步失败");
                    }

                } catch (Exception parseException) {
                    log.error("解析设备API响应失败", parseException);
                    return new EpsResponse()
                            .put("success", false)
                            .put("error", parseException.getMessage())
                            .message("解析设备API响应失败");
                }
            } else {
                return new EpsResponse()
                        .put("success", false)
                        .put("statusCode", response.getStatusCode())
                        .data(response.getResult())
                        .message("获取设备信息失败");
            }

        } catch (Exception e) {
            log.error("同步设备信息异常", e);
            return new EpsResponse()
                    .put("success", false)
                    .put("error", e.getMessage())
                    .message("同步设备信息异常");
        }
    }

    /**
     * 批量同步所有设备信息到数据库
     *
     * @param maxPages 最大同步页数 (可选，默认10页，防止数据量过大)
     * @param pageSize 每页大小 (可选，默认100)
     * @return 同步结果
     */
    @PostMapping("/sync-all")
    public EpsResponse syncAllDevicesToDatabase(
            @RequestParam(value = "maxPages", required = false, defaultValue = "10") Integer maxPages,
            @RequestParam(value = "pageSize", required = false, defaultValue = "100") Integer pageSize) {

        log.info("开始批量同步所有设备信息到数据库，最大页数: {}, 每页大小: {}", maxPages, pageSize);

        int totalSyncCount = 0;
        int successPages = 0;
        int failedPages = 0;
        StringBuilder errorMessages = new StringBuilder();

        try {
            for (int pageIndex = 1; pageIndex <= maxPages; pageIndex++) {
                log.info("正在同步第 {} 页设备信息", pageIndex);

                try {
                    // 构建请求参数
                    Map<String, String> params = new HashMap<>();
                    params.put("pageIndex", String.valueOf(pageIndex));
                    params.put("pageSize", pageSize.toString());

                    // 构建请求头
                    Map<String, String> headers = new HashMap<>();
                    headers.put("appKey", APP_KEY);
                    headers.put("Content-Type", "application/json");

                    // 发送HTTP GET请求
                    Response response = HttpClientUtil.doGet(DEVICE_API_URL, params, headers);

                    if (response.getStatusCode() == 200) {
                        DeviceApiResponse deviceApiResponse = JSON.parseObject(response.getResult(), DeviceApiResponse.class);

                        if (deviceApiResponse.getContent() != null &&
                            deviceApiResponse.getContent().getResult() != null &&
                            !deviceApiResponse.getContent().getResult().isEmpty()) {

                            // 同步当前页的设备信息
                            boolean syncResult = deviceService.syncDevices(deviceApiResponse.getContent().getResult());

                            if (syncResult) {
                                totalSyncCount += deviceApiResponse.getContent().getResult().size();
                                successPages++;
                                log.info("第 {} 页设备信息同步成功，同步数量: {}", pageIndex,
                                        deviceApiResponse.getContent().getResult().size());
                            } else {
                                failedPages++;
                                errorMessages.append("第").append(pageIndex).append("页同步失败; ");
                                log.warn("第 {} 页设备信息同步失败", pageIndex);
                            }

                            // 如果当前页数据量小于页大小，说明已经是最后一页
                            if (deviceApiResponse.getContent().getResult().size() < pageSize) {
                                log.info("已到达最后一页，总共同步 {} 页", pageIndex);
                                break;
                            }
                        } else {
                            log.info("第 {} 页无设备数据，停止同步", pageIndex);
                            break;
                        }
                    } else {
                        failedPages++;
                        errorMessages.append("第").append(pageIndex).append("页API调用失败(状态码:")
                                   .append(response.getStatusCode()).append("); ");
                        log.warn("第 {} 页API调用失败，状态码: {}", pageIndex, response.getStatusCode());
                    }

                    // 添加延迟，避免API调用过于频繁
                    Thread.sleep(100);

                } catch (Exception pageException) {
                    failedPages++;
                    errorMessages.append("第").append(pageIndex).append("页异常:")
                               .append(pageException.getMessage()).append("; ");
                    log.error("第 {} 页同步异常", pageIndex, pageException);
                }
            }

            // 构建响应结果
            boolean overallSuccess = failedPages == 0;
            String message = String.format("批量同步完成，成功页数: %d，失败页数: %d，总同步设备数: %d",
                                         successPages, failedPages, totalSyncCount);

            EpsResponse response = new EpsResponse()
                    .put("success", overallSuccess)
                    .put("totalSyncCount", totalSyncCount)
                    .put("successPages", successPages)
                    .put("failedPages", failedPages)
                    .put("maxPages", maxPages)
                    .put("pageSize", pageSize)
                    .message(message);

            if (failedPages > 0) {
                response.put("errors", errorMessages.toString());
            }

            return response;

        } catch (Exception e) {
            log.error("批量同步设备信息异常", e);
            return new EpsResponse()
                    .put("success", false)
                    .put("totalSyncCount", totalSyncCount)
                    .put("successPages", successPages)
                    .put("failedPages", failedPages)
                    .put("error", e.getMessage())
                    .message("批量同步设备信息异常");
        }
    }

    /**
     * 查询本地数据库中的设备列表
     *
     * @param request 分页请求
     * @param device 查询条件
     * @return 设备列表
     */
    @GetMapping("/local")
    public EpsResponse getLocalDevices(QueryRequest request, Device device) {
        log.info("查询本地设备列表，页码: {}, 页大小: {}", request.getPageNum(), request.getPageSize());

        try {
            IPage<Device> devicePage = deviceService.findDevices(request, device);

            return new EpsResponse()
                    .put("success", true)
                    .put("total", devicePage.getTotal())
                    .put("pages", devicePage.getPages())
                    .put("current", devicePage.getCurrent())
                    .put("size", devicePage.getSize())
                    .data(devicePage.getRecords())
                    .message("查询本地设备列表成功");

        } catch (Exception e) {
            log.error("查询本地设备列表异常", e);
            return new EpsResponse()
                    .put("success", false)
                    .put("error", e.getMessage())
                    .message("查询本地设备列表异常");
        }
    }

    /**
     * 获取设备同步状态统计
     *
     * @return 同步状态统计信息
     */
    @GetMapping("/sync-status")
    public EpsResponse getDeviceSyncStatus() {
        log.info("查询设备同步状态统计");

        try {
            // 查询各种同步状态的设备数量
            List<Device> syncedDevices = deviceService.findBySyncStatus(1); // 已同步
            List<Device> unsyncedDevices = deviceService.findBySyncStatus(0); // 未同步
            List<Device> errorDevices = deviceService.findBySyncStatus(-1); // 同步失败

            // 查询总设备数量
            long totalDevices = deviceService.count();

            // 构建统计信息
            Map<String, Object> statusMap = new HashMap<>();
            statusMap.put("totalDevices", totalDevices);
            statusMap.put("syncedCount", syncedDevices.size());
            statusMap.put("unsyncedCount", unsyncedDevices.size());
            statusMap.put("errorCount", errorDevices.size());
            statusMap.put("syncRate", totalDevices > 0 ?
                String.format("%.2f%%", (double) syncedDevices.size() / totalDevices * 100) : "0.00%");

            return new EpsResponse()
                    .put("success", true)
                    .data(statusMap)
                    .message("查询设备同步状态统计成功");

        } catch (Exception e) {
            log.error("查询设备同步状态统计异常", e);
            return new EpsResponse()
                    .put("success", false)
                    .put("error", e.getMessage())
                    .message("查询设备同步状态统计异常");
        }
    }

    /**
     * 重新同步指定状态的设备
     *
     * @param syncStatus 要重新同步的设备状态 (0: 未同步, -1: 同步失败)
     * @return 重新同步结果
     */
    @PostMapping("/resync")
    public EpsResponse resyncDevicesByStatus(
            @RequestParam(value = "syncStatus", defaultValue = "0") Integer syncStatus) {

        log.info("开始重新同步状态为 {} 的设备", syncStatus);

        try {
            List<Device> devicesToResync = deviceService.findBySyncStatus(syncStatus);

            if (devicesToResync.isEmpty()) {
                return new EpsResponse()
                        .put("success", true)
                        .put("resyncCount", 0)
                        .message("没有需要重新同步的设备");
            }

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            for (Device device : devicesToResync) {
                try {
                    // 更新同步状态为成功
                    boolean updateResult = deviceService.updateSyncStatus(device.getId(), 1);
                    if (updateResult) {
                        successCount++;
                        log.info("设备 {} 重新同步成功", device.getDeviceName());
                    } else {
                        failCount++;
                        errorMessages.append("设备").append(device.getDeviceName()).append("更新失败; ");
                    }
                } catch (Exception e) {
                    failCount++;
                    errorMessages.append("设备").append(device.getDeviceName())
                               .append("异常:").append(e.getMessage()).append("; ");
                    log.error("设备 {} 重新同步异常", device.getDeviceName(), e);
                }
            }

            String message = String.format("重新同步完成，成功: %d，失败: %d", successCount, failCount);
            EpsResponse response = new EpsResponse()
                    .put("success", failCount == 0)
                    .put("totalCount", devicesToResync.size())
                    .put("successCount", successCount)
                    .put("failCount", failCount)
                    .message(message);

            if (failCount > 0) {
                response.put("errors", errorMessages.toString());
            }

            return response;

        } catch (Exception e) {
            log.error("重新同步设备异常", e);
            return new EpsResponse()
                    .put("success", false)
                    .put("error", e.getMessage())
                    .message("重新同步设备异常");
        }
    }
}
