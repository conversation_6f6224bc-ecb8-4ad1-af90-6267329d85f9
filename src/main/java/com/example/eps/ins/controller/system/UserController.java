package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.bean.UpdatePassword;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.constant.StringConstant;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.entity.system.LoginLog;
import com.example.eps.ins.common.entity.system.Role;
import com.example.eps.ins.common.entity.system.SystemUser;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.annotation.ControllerEndpoint;
import com.example.eps.ins.common.service.*;
import com.example.eps.ins.service.*;
import com.wuwenze.poi.ExcelKit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/user")
public class UserController {

    private final IUserService userService;
    private final IUserDataPermissionService userDataPermissionService;
    private final ILoginLogService loginLogService;
    private final PasswordEncoder passwordEncoder;
    private final IRegionService regionService;
    private final IRoleService roleService;

    @GetMapping("success")
    public void loginSuccess(HttpServletRequest request) {
        String currentUsername = EpsUtil.getCurrentUsername();
        // update last login time
        this.userService.updateLoginTime(currentUsername);
        // save login log
        LoginLog loginLog = new LoginLog();
        loginLog.setUsername(currentUsername);
        loginLog.setSystemBrowserInfo(request.getHeader("user-agent"));
        this.loginLogService.saveLoginLog(loginLog);
    }

    @GetMapping("index")
    public EpsResponse index() {
        Map<String, Object> data = new HashMap<>(5);
        // 获取系统访问记录
        Long totalVisitCount = loginLogService.findTotalVisitCount();
        data.put("totalVisitCount", totalVisitCount);
        Long todayVisitCount = loginLogService.findTodayVisitCount();
        data.put("todayVisitCount", todayVisitCount);
        Long todayIp = loginLogService.findTodayIp();
        data.put("todayIp", todayIp);
        // 获取近期系统访问记录
        List<Map<String, Object>> lastTenVisitCount = loginLogService.findLastTenDaysVisitCount(null);
        data.put("lastTenVisitCount", lastTenVisitCount);
        SystemUser param = new SystemUser();
        param.setUsername(EpsUtil.getCurrentUsername());
        List<Map<String, Object>> lastTenUserVisitCount = loginLogService.findLastTenDaysVisitCount(param);
        data.put("lastTenUserVisitCount", lastTenUserVisitCount);
        return new EpsResponse().data(data);
    }

    @GetMapping
    @PreAuthorize("hasAuthority('user:view')")
    @ControllerEndpoint(operation = "查询用户列表", exceptionMessage = "查询用户列表失败")
    public ResponseEntity<EpsResponse> userList(QueryRequest queryRequest, SystemUser user) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(userService.findUserDetailList(user, queryRequest));
        return ResponseEntity.ok().body(new EpsResponse().data(dataTable));
    }

    @GetMapping("{id}")
    public ResponseEntity<EpsResponse> getUser(@NotNull @PathVariable Long id) {
        return ResponseEntity.ok().body(new EpsResponse().data(userService.findUser(id)));
    }

    @GetMapping("check/{username}")
    public boolean checkUserName(@NotBlank(message = "{required}") @PathVariable String username) {
        return this.userService.findByName(username) == null;
    }

    @PostMapping
    @PreAuthorize("hasAuthority('user:add')")
    @ControllerEndpoint(operation = "新增用户", exceptionMessage = "新增用户失败")
    public ResponseEntity<EpsResponse> addUser(@Valid @RequestBody SystemUser user) {
        String userName = user.getUsername();
        if (StringUtils.isEmpty(userName)) {
            throw new EpsException("账号名不能为空");
        }
        SystemUser userSearchByUserName = userService.findByName(userName);
        if (userSearchByUserName != null) {
            throw new EpsException("该账号已注册");
        }
        validate(user);
        SystemUser newUser = new SystemUser();
        newUser.setUsername(userName);
        newUser.setNickName(user.getNickName());
        newUser.setMobile(user.getMobile());
        newUser.setIdNo(user.getIdNo());
        newUser.setRegionId(user.getRegionId());
        newUser.setRoleIds(user.getRoleIds());
        newUser.setPassword(new BCryptPasswordEncoder().encode(user.DEFAULT_PWD));
        newUser.setStatus(1);
        newUser.setCreateUserId(EpsUtil.getCurrentUser().getUserId());
        newUser.setModifyUserId(EpsUtil.getCurrentUser().getUserId());
        Date currentDate = new Date();
        newUser.setCreateTime(currentDate);
        newUser.setModifyTime(currentDate);

        userService.createUser(newUser);
        return ResponseEntity.ok().body(new EpsResponse().message("新增用户成功"));
    }

    @PutMapping
    @PreAuthorize("hasAuthority('user:update')")
    @ControllerEndpoint(operation = "修改用户", exceptionMessage = "修改用户失败")
    public void updateUser(@RequestBody SystemUser user) {
        validate(user);
        SystemUser newUser = new SystemUser();
        newUser.setUserId(user.getUserId());
        newUser.setNickName(user.getNickName());
        newUser.setMobile(user.getMobile());
        newUser.setIdNo(user.getIdNo());
        newUser.setRegionId(user.getRegionId());
        newUser.setRoleIds(user.getRoleIds());
        newUser.setModifyUserId(EpsUtil.getCurrentUser().getUserId());
        newUser.setModifyTime(new Date());
        this.userService.updateUser(newUser);
    }

    private void validate(SystemUser user) {
        String mobile = user.getMobile();
        if(StringUtils.isEmpty(mobile) || !EpsUtil.checkMobile(mobile)){
            throw new EpsException("手机号不符合规范");
        }
        Region region = regionService.getById(user.getRegionId());
        if (null == region) {
            throw new EpsException("组织不存在");
        }

        for (Long roleID : user.getRoleIds()) {
            Role role = roleService.getById(roleID);
            if (role == null) {
                throw new EpsException("角色不存在");
            }
        }
    }

    @GetMapping("/{userId}")
    @PreAuthorize("hasAuthority('user:update')")
    public EpsResponse findUserDataPermissions(@NotBlank(message = "{required}") @PathVariable String userId) {
        String dataPermissions = this.userDataPermissionService.findByUserId(userId);
        return new EpsResponse().data(dataPermissions);
    }

    @DeleteMapping("/{userIds}")
    @PreAuthorize("hasAuthority('user:delete')")
    @ControllerEndpoint(operation = "删除用户", exceptionMessage = "删除用户失败")
    public void deleteUsers(@NotBlank(message = "{required}") @PathVariable String userIds) {
        String[] ids = userIds.split(StringConstant.COMMA);
        this.userService.deleteUsers(ids);
    }

    @PutMapping("profile")
    @ControllerEndpoint(exceptionMessage = "修改个人信息失败")
    public void updateProfile(@Valid SystemUser user) throws EpsException {
        this.userService.updateProfile(user);
    }

    @PutMapping("theme")
    public void updateSidebarTheme(@NotBlank(message = "{required}") String theme) {
        userService.updateSidebarTheme(theme);
    }

    @PutMapping("avatar")
    @ControllerEndpoint(exceptionMessage = "修改头像失败")
    public void updateAvatar(@NotBlank(message = "{required}") String avatar) {
        this.userService.updateAvatar(avatar);
    }

    @GetMapping("password/check")
    public boolean checkPassword(@NotBlank(message = "{required}") String password) {
        String currentUsername = EpsUtil.getCurrentUsername();
        SystemUser user = userService.findByName(currentUsername);
        return user != null && passwordEncoder.matches(password, user.getPassword());
    }

    @PutMapping("password")
    @ControllerEndpoint(exceptionMessage = "修改密码失败")
    public void updatePassword(@RequestBody UpdatePassword updatePassword) {
        if (!EpsUtil.checkPassword(updatePassword.getNewPassword())) {
            throw new EpsException("新密码不符合要求");
        }
        String currentUsername = EpsUtil.getCurrentUsername();
        SystemUser user = userService.findByName(currentUsername);
        if (!passwordEncoder.matches(updatePassword.getOldPassword(), user.getPassword())) {
            throw new EpsException("旧密码错误");
        }
        userService.updatePassword(updatePassword.getNewPassword());
    }

    @PutMapping("password/reset/{userID}")
    @PreAuthorize("hasAuthority('user:reset')")
    @ControllerEndpoint(operation = "重置用户密码", exceptionMessage = "重置用户密码失败")
    public ResponseEntity<EpsResponse> resetPassword(@PathVariable("userID") Integer userID) {
        Integer[] userIDs = {userID};
        this.userService.resetPassword(userIDs);

        return ResponseEntity.ok().body(new EpsResponse().message("重置用户密码成功,密码为:" + SystemUser.DEFAULT_PWD));
    }

    @PostMapping("excel")
    @PreAuthorize("hasAuthority('user:export')")
    @ControllerEndpoint(operation = "导出用户数据", exceptionMessage = "导出Excel失败")
    public void export(QueryRequest queryRequest, SystemUser user, HttpServletResponse response) {
        List<SystemUser> users = this.userService.findUserDetailList(user, queryRequest).getRecords();
        ExcelKit.$Export(SystemUser.class, response).downXlsx(users, false);
    }
}
