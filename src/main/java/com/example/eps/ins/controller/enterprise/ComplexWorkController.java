package com.example.eps.ins.controller.enterprise;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.enterprise.ComplexWork;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.ComplexWorkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/9 10:38
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/enterprise/complexWork")
@RequiredArgsConstructor
public class ComplexWorkController {


    private final ComplexWorkService complexWorkService;

    @GetMapping("list")
    public EpsResponse complexWorkList(QueryRequest request, ComplexWork outsourceWork) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.complexWorkService.findComplexWork(request,
                outsourceWork));
        return new EpsResponse().data(dataTable);
    }

    @PostMapping("add")
    public void addComplexWork(ComplexWork dto) throws EpsException {
        try {
            this.complexWorkService.createComplexWork(dto);
        } catch (Exception e) {
//            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }
    @DeleteMapping("{id}")
    public void deleteComplexWork(@NotNull @PathVariable Long id) throws EpsException {
        try {
            this.complexWorkService.deleteComplexWork(id);
        } catch (Exception e) {
            String message = "删除失败";
//            log.error(message, e);
            throw new EpsException(message);
        }
    }
    /**
     * 删除文件
     *
     * @param filePath 文件路径
     */
    @DeleteMapping(value = "delete")
    @ResponseBody
    public void deleteFile(Long id,String filePath) {
        try {
            this.complexWorkService.deleteFile(id,filePath);
        } catch (Exception e) {
            String message = "删除文件失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
    @GetMapping("{id}")
    public EpsResponse queryComplexWork(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(complexWorkService.findComplexWork(id));
    }
}
