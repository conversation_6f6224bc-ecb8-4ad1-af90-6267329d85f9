package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.annotation.ControllerEndpoint;
import com.example.eps.ins.common.bean.EnterpriseDeclarationRequest;
import com.example.eps.ins.common.entity.CreatedResponse;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.model.EnterpriseVo;
import com.example.eps.ins.common.model.ReportVo;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.common.utils.PoiUtils;
import com.example.eps.ins.service.IEnterpriseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 企业信息表 Controller
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:42
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/enterprise")
@RequiredArgsConstructor
public class EnterpriseSystemController {

    private static final String TEMPLATE_EXCEL_NAME = "企业信息导入模板";
    private static final String EXCEL_TYPE = ".xlsx";
    private final IEnterpriseService enterpriseService;

    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('enterprise:get')")
    public EpsResponse getEnterprise(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(enterpriseService.findEnterprise(id));
    }

    @PostMapping("list")
    @PreAuthorize("hasAuthority('enterprise:list')")
    public EpsResponse enterpriseListSystem(@RequestBody EnterpriseVo enterprise) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseService
                .findEnterprises(enterprise));
        return new EpsResponse().data(dataTable);
    }
    @PostMapping("getReport")
    public EpsResponse getRoport() {
        List<ReportVo> dataTable = this.enterpriseService
                .getReport();
        return new EpsResponse().data(dataTable);
    }
    @PostMapping("hiddenList")
    public EpsResponse hiddenList(@RequestBody EnterpriseVo enterprise) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.enterpriseService.hiddenList(
                enterprise));
        return new EpsResponse().data(dataTable);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('enterprise:update')")
    @ControllerEndpoint(operation = "修改企业信息", exceptionMessage = "修改企业信息失败")
    public void updateEnterprise(@NotNull @PathVariable Long id, @RequestBody EnterpriseDeclarationRequest enterprise) {
        enterpriseService.updateEnterprise(id,enterprise);
    }

    @PutMapping("updateAddress/{id}")
    @PreAuthorize("hasAuthority('enterprise:update')")
    @ControllerEndpoint(operation = "修改企业经营地址", exceptionMessage = "修改企业经营地址失败")
    public void updateEnterpriseAddress(@NotNull @PathVariable Long id, @RequestBody Enterprise enterprise) {
        enterpriseService.updateEnterpriseAddress(id, enterprise);
    }


    @PostMapping
    @PreAuthorize("hasAuthority('enterprise:add')")
    @ControllerEndpoint(operation = "新增企业信息", exceptionMessage = "新增企业信息失败")
    public EpsResponse addEnterprise(@Valid @RequestBody EnterpriseDeclarationRequest enterprise) throws EpsException {
        Long id = enterpriseService.createEnterprise(enterprise);
        CreatedResponse response = new CreatedResponse();
        response.setId(id);
        return new EpsResponse().data(response);
    }

    @PostMapping("import")
    @PreAuthorize("hasAuthority('enterprise:import')")
    @ControllerEndpoint(operation = "导入企业信息", exceptionMessage = "导入企业信息失败")
    public EpsResponse importEnterprises(MultipartFile file) throws IOException, EpsException {
        return new EpsResponse().message(String.format("成功导入%s条数据",
                enterpriseService.importEnterprises(file)));
    }

    @GetMapping("excel")
    public void export(EnterpriseVo enterprise, HttpServletResponse response, HttpServletRequest request) {
        List<Enterprise> enterprises = this.enterpriseService.listEnterprises(enterprise);
        List<String[]> records = enterpriseService.conver(enterprises);
        String[] tableHeaderArr = {
                "企业名称","社信代码","行业类别","单位性质","所属区县",
                "所属镇街","从业人数","法人代表/主要负责人","法人电话","安全负责人",
                "安全负责人电话","专职安全员人数","兼职安全员人数","注册地址","生产经营状态","安全生产标准化是否达标",
                "对标达标等级","对标达标时间","证书编号",
                "专业达标时间","岗位达标时间","应急预案是否具备","应急预案是否评审","应急预案是否备案",
                "规上规下企业","安全保险"};
        //输出csv流文件，提供给浏览器下载
        try {
            File tempFile = PoiUtils.createTempFile(records, tableHeaderArr);
            PoiUtils.outCsvStream(response, tempFile);
            //删除临时文件
            PoiUtils.deleteFile(tempFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }



    @GetMapping("downloadTemplate")
    @PreAuthorize("hasAuthority('enterprise:downloadTemplate')")
    @ControllerEndpoint(operation = "下载企业信息导入模板", exceptionMessage = "下载企业信息导入模板失败")
    public void downloadTemplate(HttpServletResponse response) throws IOException, EpsException {
        InputStream inputStream = this.getClass().getResourceAsStream(String.format("/template/%s%s", TEMPLATE_EXCEL_NAME, EXCEL_TYPE));
        response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.addHeader("charset", "utf-8");
        response.addHeader("Pragma", "no-cache");
        String encodeName = URLEncoder.encode(String.format("%s%s", TEMPLATE_EXCEL_NAME, EXCEL_TYPE), StandardCharsets.UTF_8.toString());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);
        ServletOutputStream servletOutputStream = response.getOutputStream();
        IOUtils.copy(inputStream, servletOutputStream);
        response.flushBuffer();
    }

    @GetMapping
    @PreAuthorize("hasAuthority('enterprise:list')")
    public EpsResponse getAllEnterprises(Enterprise enterprise) {
        return new EpsResponse().data(enterpriseService.findEnterprises(enterprise));
    }

    @DeleteMapping
    @PreAuthorize("hasAuthority('enterprise:delete')")
    public void deleteEnterprise(Enterprise enterprise) throws EpsException {
        try {
            this.enterpriseService.deleteEnterprise(enterprise);
        } catch (Exception e) {
            String message = "删除Enterprise失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}
