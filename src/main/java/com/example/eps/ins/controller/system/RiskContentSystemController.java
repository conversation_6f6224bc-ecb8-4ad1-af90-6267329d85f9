package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.dto.report.model.RiskContentVo;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.RiskContentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/6/10 11:18
 * @Version: 1.0
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/risk/content")
@RequiredArgsConstructor
public class RiskContentSystemController {

    private final RiskContentService riskContentService;

    @PostMapping("getContent")
    public EpsResponse messageList(@RequestBody RiskContentVo dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.riskContentService.getSystemList(dto));
        return new EpsResponse().data(dataTable);
    }
    @PostMapping("getList")
    public EpsResponse getList(@RequestBody RiskContentVo dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.riskContentService.getAllList(dto));
        return new EpsResponse().data(dataTable);
    }
    @PostMapping("enterpriseList")
    public EpsResponse enterpriseList(@RequestBody RiskContentVo dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.riskContentService.enterpriseList(
                dto));
        return new EpsResponse().data(dataTable);
    }
}
