package com.example.eps.ins.controller.system;

import com.example.eps.ins.common.bean.MessageRequest;
import com.example.eps.ins.common.dto.report.model.MessageVo;
import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.entity.QueryRequest;
import com.example.eps.ins.common.entity.system.Message;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.utils.EpsUtil;
import com.example.eps.ins.service.IMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * @Description: <消息通知表前端控制器>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/system/message")
@RequiredArgsConstructor
public class MessageSystemController {

    private final IMessageService iMessageService;

    @GetMapping("currentUser/list")
    public EpsResponse messageListByUser(MessageRequest request) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.iMessageService.findMessagesByCurrentUser(request));
        return new EpsResponse().data(dataTable);
    }

    @GetMapping
    public EpsResponse messageList(QueryRequest request, Message dto) {
        Map<String, Object> dataTable = EpsUtil.getDataTable(this.iMessageService.findMessages(request, dto));
        return new EpsResponse().data(dataTable);
    }

    @GetMapping("early")
    public EpsResponse earlyWarningByUser() {
        return new EpsResponse().data(this.iMessageService.earlyWarningByUser());
    }

    @GetMapping("warning")
    public EpsResponse warningByUser() {
        return new EpsResponse().data(this.iMessageService.warningByUser());
    }


    @GetMapping("{id}")
    public EpsResponse queryMessage(@NotNull @PathVariable Long id) {
        return new EpsResponse().data(iMessageService.findMessage(id));
    }

    @GetMapping("noSee")
    public EpsResponse queryNoSee(QueryRequest request, MessageUser messageUser) {
        return new EpsResponse().data(iMessageService.findNoSee(request,messageUser));

    }

    @GetMapping("currentUser")
    public EpsResponse queryMessageByCurrentUser() {
        return new EpsResponse().data(iMessageService.findMessageByCurrentUser());
    }

    @PostMapping
    public void addMessage(@Valid MessageVo dto) throws EpsException {
        try {
            this.iMessageService.createMessage(dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new EpsException(e.getMessage());
        }
    }

    @DeleteMapping("{id}")
    public void deleteMessage(@NotNull @PathVariable Long id) throws EpsException {
        try {
            this.iMessageService.deleteMessage(id);
        } catch (Exception e) {
            String message = "删除Message失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @DeleteMapping("delete")
    public void deleteMessageFile(Long id,String filePath) throws EpsException {
        try {
            this.iMessageService.deleteFile(id,filePath);
        } catch (Exception e) {
            String message = "删除Message附件失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }

    @PutMapping
    public void updateMessage(Message dto) throws EpsException {
        try {
            this.iMessageService.updateMessage(dto);
        } catch (Exception e) {
            String message = "修改Message失败";
            log.error(message, e);
            throw new EpsException(message);
        }
    }
}