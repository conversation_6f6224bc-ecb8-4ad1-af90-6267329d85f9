package com.example.eps.ins.controller.report;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.dto.report.req.BehaviorReq;
import com.example.eps.ins.common.dto.report.req.EnforcementReq;
import com.example.eps.ins.service.BehaviorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: zhanghongyin @Date: Created in 2021/12/1 16:59 @Description: 执法计划controller @Version:
 * 1.0
 */
@RestController
@Slf4j
@RequestMapping("/report/behavior")
@RequiredArgsConstructor
public class BehaviorController {
  private final BehaviorService behaviorPlanService;

  /**
   * 违法数据年度排序
   * @param behaviorReq 参数
   * @return 装有年度排序的集合
   */
  @PostMapping("sort")
  public EpsResponse getSort(@RequestBody BehaviorReq behaviorReq) {
    return new EpsResponse().data(behaviorPlanService.getSort(behaviorReq));
  }

  /**
   * 获取年度执法数据总览
   * @param behaviorReq 请求参数
   * @return 年度执法数据集合
   */
  @PostMapping("all")
  public EpsResponse getAll(@RequestBody EnforcementReq behaviorReq) {
    return new EpsResponse().data(behaviorPlanService.getAll(behaviorReq));
  }

  /**
   * 年度执法数据统计
   * @param behaviorReq 请求参数
   * @return 集合
   */
  @PostMapping("list")
  public EpsResponse getEnforcementList(@RequestBody EnforcementReq behaviorReq) {
    return new EpsResponse().data(behaviorPlanService.getEnforcementList(behaviorReq));
  }
}
