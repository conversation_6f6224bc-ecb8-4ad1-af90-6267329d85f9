package com.example.eps.ins.common.dto.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 设备信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class DeviceInfo {
    
    /**
     * 设备ID
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 设备名称
     */
    @JsonProperty("deviceName")
    private String deviceName;
    
    /**
     * 设备序列号
     */
    @JsonProperty("serial")
    private String serial;
    
    /**
     * 设备状态
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 设备类型
     */
    @JsonProperty("deviceType")
    private String deviceType;
    
    /**
     * 设备IP地址
     */
    @JsonProperty("ip")
    private String ip;

    /**
     * 子设备列表
     */
    @JsonProperty("subDevices")
    private List<SubDeviceInfo> subDevices;
}
