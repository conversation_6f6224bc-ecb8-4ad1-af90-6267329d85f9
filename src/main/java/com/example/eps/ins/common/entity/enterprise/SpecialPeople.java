package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
@TableName("t_special_people")
public class SpecialPeople {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 姓名
     */
    @TableField("special_name")
    private String specialName;
    /**
     * 岗位
     */
    @TableField("special_post")
    private String specialPost;
    /**
     * 证书类别
     */
    @TableField("special_certificate")
    private String specialCertificate;
    /**
     * 资格证号
     */
    @TableField("special_qualifications")
    private String specialQualifications;

    /**
     * 颁证机关
     */
    @TableField("special_office")
    private String specialOffice;
    /**
     * 证书到期时间
     */
    @TableField("special_expiration_time")
    private String specialExpirationTime;
    /**
     * 身份证号
     */
    @TableField("special_id_card")
    private String specialIdCard;
    /**
     * 新建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;

    @TableField(exist = false)
    private String enterpriseName;

    @TableField(exist = false)
    private List<String> created;

}
