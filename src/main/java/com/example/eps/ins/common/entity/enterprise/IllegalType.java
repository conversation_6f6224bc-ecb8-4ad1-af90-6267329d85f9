package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 风险类型 Entity
 *
 * <AUTHOR>
 * @date 2022-01-24 15:39:12
 */
@Data
@TableName("t_illegal_type")
public class IllegalType {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型名称;
     */
    @TableField("name")
    private String name;

    /**
     * 父ID;
     */
    @TableField("parent_id")
    private Long parentId;

}