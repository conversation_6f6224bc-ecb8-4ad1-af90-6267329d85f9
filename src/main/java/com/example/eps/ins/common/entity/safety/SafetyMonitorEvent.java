package com.example.eps.ins.common.entity.safety;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 安全监控事件表 Entity
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@TableName("t_safety_monitor_event")
public class SafetyMonitorEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 外部事件ID（来自第三方系统）
     */
    @TableField("external_event_id")
    private String externalEventId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private Long deviceId;

    /**
     * 设备序列号
     */
    @TableField("device_serial")
    private String deviceSerial;

    /**
     * 设备IP地址
     */
    @TableField("device_ip")
    private String deviceIp;

    /**
     * 设备类型
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 设备状态
     */
    @TableField("device_status")
    private String deviceStatus;

    /**
     * 摄像头ID
     */
    @TableField("camera_id")
    private String cameraId;

    /**
     * 摄像头名称
     */
    @TableField("camera_name")
    private String cameraName;

    /**
     * 事件类型（121=安全帽检测, 122=反光衣检测, 123=人员入侵等）
     */
    @TableField("event_type")
    private Integer eventType;

    /**
     * 事件类型名称
     */
    @TableField("event_type_name")
    private String eventTypeName;

    /**
     * 事件源
     */
    @TableField("event_source")
    private Integer eventSource;

    /**
     * 置信度
     */
    @TableField("confidence")
    private Double confidence;

    /**
     * 检测分数
     */
    @TableField("detection_score")
    private Double detectionScore;

    /**
     * 是否有告警（0=无, 1=有）
     */
    @TableField("have_alarm")
    private Integer haveAlarm;

    /**
     * 告警次数
     */
    @TableField("alarm_times")
    private Integer alarmTimes;

    /**
     * 检测对象名称
     */
    @TableField("object_name")
    private String objectName;

    /**
     * 边界框坐标（JSON格式存储）
     */
    @TableField("bounding_box")
    private String boundingBox;

    /**
     * 图片URL
     */
    @TableField("img_url")
    private String imgUrl;

    /**
     * 大图URL
     */
    @TableField("big_url")
    private String bigUrl;

    /**
     * 图片宽度
     */
    @TableField("img_width")
    private Integer imgWidth;

    /**
     * 图片高度
     */
    @TableField("img_height")
    private Integer imgHeight;

    /**
     * 事件开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 事件结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 事件标记时间
     */
    @TableField("mark_time")
    private Date markTime;

    /**
     * 原始JSON数据
     */
    @TableField("raw_json_data")
    private String rawJsonData;

    /**
     * 处理状态（0=未处理, 1=已处理, 2=处理失败）
     */
    @TableField("process_status")
    private Integer processStatus;

    /**
     * 处理时间
     */
    @TableField("process_time")
    private Date processTime;

    /**
     * 处理结果
     */
    @TableField("process_result")
    private String processResult;

    /**
     * 告警状态（0=未告警, 1=已告警）
     */
    @TableField("alarm_status")
    private Integer alarmStatus;

    /**
     * 告警时间
     */
    @TableField("alarm_time")
    private Date alarmTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
