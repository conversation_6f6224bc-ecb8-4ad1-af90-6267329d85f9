package com.example.eps.ins.common.dto.enterprise;

import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/10/11 16:56
 * @Version: 1.0
 */
@Data
public class EmergencyPlanVo {
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 6516800943817041235L;
    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 应急预案名称
     */
    @NonNull
    private String emergencyPlanName;
    /**
     * 摘要
     */
    @NotNull
    private String digest;
    /**
     * 应急预案文件
     */
    private String emergencyPlanFile;
    /**
     * 上传时间
     */
    private Date createTime;
    /**
     * 1、综合预案  2、专项预案 3、现场处置方案
     */
    private Integer planType;

    /**
     * 所属地区
     */
    private Long regionId;


    private String enterpriseName;

    private List<String> created;
}
