package com.example.eps.ins.common.dto.report.model;

import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
public class TrainEducationVo  extends QueryRequest {
    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 培训主题
     */
    private String trainTheme;
    /**
     * 培训内容
     */
    private String trainContent;
    /**
     * 培训图片
     */
    private String trainImage;
    /**
     * 培训文档
     */
    private String trainFile;

    /**
     * 培训时间
     */
    private String trainDate;

    /**
     * 新建人
     */
    private Long creatorId;

    /**
     * 新建时间
     */
    private Date createTime;
    /**
     * 所属地区
     */
    private Long regionId;

    private String enterpriseName;

    private List<String> created;
}
