package com.example.eps.ins.common.dto.device;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 设备绑定请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class DeviceBindingRequest {
    
    /**
     * 企业ID
     */
    @NotNull(message = "企业ID不能为空")
    private Long enterpriseId;
    
    /**
     * 外部设备ID
     */
    @NotNull(message = "外部设备ID不能为空")
    private Long externalDeviceId;
    
    /**
     * 设备名称（用户输入的自定义名称）
     */
    @NotBlank(message = "设备名称不能为空")
    private String deviceName;
    
    /**
     * 设备型号
     */
    @NotBlank(message = "设备型号不能为空")
    private String deviceModel;
    
    /**
     * 创建时间（用户输入，不使用搜索框）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 备注
     */
    private String remark;
}
