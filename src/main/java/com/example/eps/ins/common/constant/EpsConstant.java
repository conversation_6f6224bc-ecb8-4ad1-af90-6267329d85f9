package com.example.eps.ins.common.constant;

/**
 * EPS系统常量类
 *
 * <AUTHOR>
 */
public interface EpsConstant {

    /**
     * 排序规则：降序
     */
    String ORDER_DESC = "descending";
    /**
     * 排序规则：升序
     */
    String ORDER_ASC = "ascending";

    /**
     * Gateway请求头TOKEN名称（不要有空格）
     */
    String GATEWAY_TOKEN_HEADER = "GatewayToken";
    /**
     * Gateway请求头TOKEN值
     */
    String GATEWAY_TOKEN_VALUE = "eps:gateway:123456";

    /**
     * 允许下载的文件类型，根据需求自己添加（小写）
     */
    String[] VALID_FILE_TYPE = {"xlsx", "zip"};

    /**
     * 验证码 key前缀
     */
    String CODE_PREFIX = "eps.captcha.";
    /**
     * 短信验证码 key前缀
     */
    String SMS_CODE_PREFIX = "eps.smsCaptcha.";

    /**
     * 异步线程池名称
     */
    String ASYNC_POOL = "epsAsyncThreadPool";

    /**
     * OAUTH2 令牌类型 https://oauth.net/2/bearer-tokens/
     */
    String OAUTH2_TOKEN_TYPE = "bearer";
    /**
     * Java默认临时目录
     */
    String JAVA_TEMP_DIR = "java.io.tmpdir";
    /**
     * utf-8
     */
    String UTF8 = "utf-8";
    /**
     * 注册用户角色ID
     */
    Long REGISTER_ROLE_ID = 2L;

    String LOCALHOST = "localhost";
    String LOCALHOST_IP = "127.0.0.1";

    /**
     * 默认验证码
     */
    String DEFAULT_CODE = "9999";

     String EXCEL_VALUE_HAVE = "有";
     String EXCEL_VALUE_YES = "是";
     String DATE_PATTERN = "yyyy-MM-dd";
     String EXCEL_EMPTY = "$EMPTY_CELL$";
     Integer PLAN_OK = 1;// 已建立应急预案
     Integer PLAN_NO = 0;// 未建立应急预案
     Integer HIDDEN_OK = 1; //已建立隐患制度
     Integer HIDDEN_NO = 0;// 未建立隐患制度
     Integer COUNT_ONE = 1;// 未建立隐患制度
     Integer COUNT_ZEOR = 0;// 未建立隐患制度
     Integer SAFETY_BCK_NO = 0;// 未建立安全生产标准化情况
     String TRADE_TYPE = "0";// 非大型商贸
     String SAFETY_MAJOR_POST="0";   //有岗位达标和
     Integer INDUSTRY_ID_ZEOR=0;   //所有行业
     Long NOT_IN=0L;
    String AQDB_GQ="aqdb_gq";
    String AQDB_JJGQ="aqdb_jjgq";
    String AQDB_NO="aqdb_no";
    String AQPX="aqpx";
    String LDLK_CJ="ldlk_cj";
    String SSSB="sssb";
    String TZRN_GQ="tzrn_gq";
    String TZRN_JJGQ="tzrn_jjgq";
    String YHPC="yhpc";
    String YHPC_ZD="yhpc_zd";
    String YJYA_CJ="yjya_cj";
    String YJYL="yjyl";
}
