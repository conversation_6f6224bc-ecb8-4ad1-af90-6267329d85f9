package com.example.eps.ins.common.dto.report.model;

import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
* 行业表 Entity
*
* <AUTHOR>
* @date 2021-10-20 13:49:40
*/
@Data
public class RiskContentVo extends QueryRequest {

    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 描述
     */
    private String contentText;
    /**
     * 描述
     */
    private String enterpriseName;

    /**
     * 创建人
     */
    private Long riskId;

    /**
     * 创建日期
     */
    private Date industryId;

    private String contentTextUser;
    private List<Long> riskIds;

    private Long creatorId;
}