package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
* 行业表 Entity
*
* <AUTHOR>
* @date 2021-10-20 13:49:40
*/
@Data
@TableName("t_risk_content_user")
public class RiskContentUser {

    /**
     * 全局唯一标识符
     */
    @TableField("content_id")
    private Long contentId;

    /**
     * 全局唯一标识符
     */
    @TableField("enterprise_id")
    private Long enterpriseId;

    @TableField("content_text_user")
    private String contentTextUser;

    @TableField("region_id")
    private Long regionId;
    @TableField("create_time")
    private Date createTime;

}