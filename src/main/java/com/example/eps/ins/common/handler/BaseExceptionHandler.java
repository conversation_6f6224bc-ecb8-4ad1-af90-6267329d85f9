package com.example.eps.ins.common.handler;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.constant.StringConstant;
import com.example.eps.ins.common.exception.ApplicationException;
import com.example.eps.ins.common.exception.EpsException;
import com.example.eps.ins.common.exception.FileDownloadException;
import com.example.eps.ins.common.utils.EpsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Path;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
public class BaseExceptionHandler {

    @ExceptionHandler(value = Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public EpsResponse handleException(Exception e) {
        String message = EpsUtil.containChinese(e.getMessage()) ? e.getMessage() : "系统内部异常";
        log.error(message, e);
        return new EpsResponse().message(message);
    }

    @ExceptionHandler(value = EpsException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public EpsResponse handleEpsException(EpsException e) {
        log.error("EPS系统异常", e);
        return new EpsResponse().message(e.getMessage());
    }

    @ExceptionHandler(value = ApplicationException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public EpsResponse handleApplicationException(ApplicationException e) {
        log.error("EPS系统异常", e);
        return new EpsResponse().message(e.getMessage());
    }

    /**
     * 统一处理请求参数校验(实体对象传参)
     *
     * @param e BindException
     * @return EpsResponse
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public EpsResponse handleBindException(BindException e) {
        StringBuilder message = new StringBuilder();
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        for (FieldError error : fieldErrors) {
            message.append(error.getField()).append(error.getDefaultMessage()).append(StringConstant.COMMA);
        }
        message = new StringBuilder(message.substring(0, message.length() - 1));
        log.error(message.toString());
        return new EpsResponse().message(message.toString());
    }

    /**
     * 统一处理请求参数校验(普通传参)
     *
     * @param e ConstraintViolationException
     * @return EpsResponse
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public EpsResponse handleConstraintViolationException(ConstraintViolationException e) {
        StringBuilder message = new StringBuilder();
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            Path path = violation.getPropertyPath();
            String[] pathArr = StringUtils.splitByWholeSeparatorPreserveAllTokens(path.toString(), ".");
            message.append(pathArr[1]).append(violation.getMessage()).append(StringConstant.COMMA);
        }
        message = new StringBuilder(message.substring(0, message.length() - 1));
        log.error(message.toString());
        return new EpsResponse().message(message.toString());
    }

    /**
     * 统一处理请求参数校验(json)
     *
     * @param e ConstraintViolationException
     * @return EpsResponse
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public EpsResponse handlerMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        StringBuilder message = new StringBuilder();
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            message.append(error.getField()).append(error.getDefaultMessage()).append(StringConstant.COMMA);
        }
        message = new StringBuilder(message.substring(0, message.length() - 1));
        log.error(message.toString());
        return new EpsResponse().message(message.toString());
    }

    @ExceptionHandler(value = FileDownloadException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public void handleFileDownloadException(FileDownloadException e) {
        log.error("FileDownloadException", e);
    }

    @ExceptionHandler(value = AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public EpsResponse handleAccessDeniedException() {
        return new EpsResponse().message("没有权限访问该资源");
    }

    @ExceptionHandler(value = HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public EpsResponse handleHttpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException e) {
        String message = "该方法不支持" + StringUtils.substringBetween(e.getMessage(), "'", "'") + "媒体类型";
        log.error(message);
        return new EpsResponse().message(message);
    }

    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public EpsResponse handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        String message = "该方法不支持" + StringUtils.substringBetween(e.getMessage(), "'", "'") + "请求方法";
        log.error(message);
        return new EpsResponse().message(message);
    }

    /**
     * 处理客户端连接中断异常
     */
    @ExceptionHandler(value = ClientAbortException.class)
    @ResponseStatus(HttpStatus.REQUEST_TIMEOUT)
    public EpsResponse handleClientAbortException(ClientAbortException e, HttpServletRequest request) {
        String requestUri = request != null ? request.getRequestURI() : "unknown";
        String method = request != null ? request.getMethod() : "unknown";

        // 记录警告日志，不记录为错误，因为这通常是客户端主动断开连接
        log.warn("客户端连接中断: {} {}, 原因: {}", method, requestUri, e.getMessage());

        return new EpsResponse().message("客户端连接中断");
    }

    /**
     * 处理Socket超时异常
     */
    @ExceptionHandler(value = SocketTimeoutException.class)
    @ResponseStatus(HttpStatus.REQUEST_TIMEOUT)
    public EpsResponse handleSocketTimeoutException(SocketTimeoutException e, HttpServletRequest request) {
        String requestUri = request != null ? request.getRequestURI() : "unknown";
        String method = request != null ? request.getMethod() : "unknown";

        log.error("Socket连接超时: {} {}, 原因: {}", method, requestUri, e.getMessage());

        return new EpsResponse().message("请求超时，请稍后重试");
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(value = MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.PAYLOAD_TOO_LARGE)
    public EpsResponse handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
        String requestUri = request != null ? request.getRequestURI() : "unknown";
        String method = request != null ? request.getMethod() : "unknown";

        log.error("文件上传大小超限: {} {}, 最大允许: {}", method, requestUri, e.getMaxUploadSize());

        long maxSizeMB = e.getMaxUploadSize() / 1024 / 1024;
        return new EpsResponse().message("上传文件过大，最大允许: " + maxSizeMB + "MB");
    }

    /**
     * 处理I/O异常
     */
    @ExceptionHandler(value = IOException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public EpsResponse handleIOException(IOException e, HttpServletRequest request) {
        String requestUri = request != null ? request.getRequestURI() : "unknown";
        String method = request != null ? request.getMethod() : "unknown";

        // 如果是ClientAbortException的根本原因，记录为警告
        if (e.getCause() instanceof ClientAbortException ||
            e.getMessage().contains("ClientAbortException")) {
            log.warn("I/O异常(客户端中断): {} {}, 原因: {}", method, requestUri, e.getMessage());
            return new EpsResponse().message("客户端连接中断");
        }

        log.error("I/O异常: {} {}, 原因: {}", method, requestUri, e.getMessage(), e);
        return new EpsResponse().message("I/O操作异常");
    }

    /**
     * 处理HTTP消息不可读异常（通常由超时引起）
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public EpsResponse handleHttpMessageNotReadableException(
            HttpMessageNotReadableException ex, HttpServletRequest request) {

        String requestUri = request != null ? request.getRequestURI() : "unknown";
        String method = request != null ? request.getMethod() : "unknown";

        // 检查是否是由ClientAbortException引起的
        if (ex.getCause() instanceof ClientAbortException) {
            ClientAbortException clientAbortException = (ClientAbortException) ex.getCause();

            // 检查是否是SocketTimeoutException
            if (clientAbortException.getCause() instanceof SocketTimeoutException) {
                log.warn("客户端请求超时中断: {} {}, 原因: {}", method, requestUri, ex.getMessage());
                return new EpsResponse().message("请求处理超时，请稍后重试");
            }

            log.warn("客户端中断连接: {} {}, 原因: {}", method, requestUri, ex.getMessage());
            return new EpsResponse().message("客户端连接中断");
        }

        // 其他HTTP消息读取异常
        log.error("HTTP消息读取异常: {} {}, 原因: {}", method, requestUri, ex.getMessage());
        return new EpsResponse().message("请求数据格式错误");
    }

}
