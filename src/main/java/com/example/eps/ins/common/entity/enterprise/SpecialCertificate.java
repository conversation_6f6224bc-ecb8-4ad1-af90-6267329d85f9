package com.example.eps.ins.common.entity.enterprise;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("t_special_certificate")
public class SpecialCertificate {

  /**
   * 全局唯一标识符
   */
  @TableId(value = "id",type = IdType.AUTO)
  private Long id;

  /**
   * 特种作业许可证类型：
   * 1-动火作业、2-高处作业、3-吊装作业、4-抽堵盲板作业、
   * 5-设备检修作业、6-动土作业、7-断路作业、8-临时用电作业
   */
  @TableField("special_type")
  private String specialType;

  /**
   * 作业许可证图片
   */
  @TableField("special_file")
  private String specialFile;

  /**
   * 创建时间
   */
  @TableField("create_time")
  private Date createTime;

  /**
   * 地区id
   */
  @TableField("region_id")
  private Long regionId;

  /**
   * 创建人
   */
  @TableField("creator_id")
  private Long creatorId;

  @TableField(exist = false)
  private String enterpriseName;

  @TableField(exist = false)
  private List<String> created;
}
