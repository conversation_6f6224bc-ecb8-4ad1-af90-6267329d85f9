package com.example.eps.ins.common.dto.report;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
// 聊天记录-消息内容
public class SubMessage implements Serializable {
    private static final long serialVersionUID = -8713837118340960775L;
    /**
     * 消息内容
     */
    private String msgText;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 推送方用户名
     */
    private String sendName;
    /**
     * 推送方用户id
     */
    private String sendId;
    /**
     * 发送用户类型（0-管理员  1-企业）
     */
    private Integer userStatus;
    /**
     *  接收方用户id
     */
    private String receiveId;
    /**
     * 消息状态（0-未读  1-已读）
     */
    private Boolean status;
}