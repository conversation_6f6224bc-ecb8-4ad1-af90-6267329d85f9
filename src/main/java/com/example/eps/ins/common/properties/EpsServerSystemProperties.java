package com.example.eps.ins.common.properties;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR>
 */
@Data
@SpringBootConfiguration
@PropertySource(value = {"classpath:eps-server-system.properties"})
@ConfigurationProperties(prefix = "eps.server.system")
public class EpsServerSystemProperties {
    /**
     * 批量插入当批次可插入的最大值
     */
    private Integer batchInsertMaxNum = 1000;
}
