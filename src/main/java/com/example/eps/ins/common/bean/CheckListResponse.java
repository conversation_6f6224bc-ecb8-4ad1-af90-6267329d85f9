package com.example.eps.ins.common.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.example.eps.ins.common.entity.enterprise.Industry;
import com.example.eps.ins.common.entity.enterprise.Risk;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: <检查清单表实体>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Data
public class CheckListResponse implements Serializable {
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 8958082058393344005L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 违法类别;
     */
    @TableField(value = "illegal_type")
    private Integer illegalType;
    /**
     * 违法行为概述
     */
    @TableField(value = "illegal_desc")
    private String illegalDesc;
    /**
     * 相关标准或依据
     */
    @TableField(value = "relevant_standard")
    private String relevantStandard;
    /**
     * 法律依据
     */
    @TableField(value = "legal_basis")
    private String legalBasis;
    /**
     * 法律责任
     */
    @TableField(value = "legal_responsibility")
    private String legalResponsibility;
    /**
     * 连续处罚
     */
    @TableField(value = "continuous_punishment")
    private String continuousPunishment;
    /**
     * 加重情形或并罚情形
     */
    @TableField(value = "aggravating_punishment")
    private String aggravatingPunishment;
    /**
     * 备注
     */
    @TableField(value = "check_desc")
    private String checkDesc;
    /**
     * 是否通用;0-通用;1-不通用
     */
    @TableField(value = "universal")
    private Integer universal;
    /**
     * 三类重点企业，1-钢铁;2-铝加工;3-粉尘;4-非三类重点企业
     */
    @TableField(value = "key_enterprise")
    private List<Long> keyEnterprise;
    /**
     * 国标或行标
     */
    @TableField(value = "national_standard")
    private Integer nationalStandard;
    /**
     * 所属规范性文件
     */
    @TableField(value = "normative_documents")
    private String normativeDocuments;
    /**
     * 状态;0-启用;1-停用
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 创建人
     */
    @TableField(value = "creator_id")
    private Long creatorId;
    /**
     * 创建日期
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 修改人
     */
    @TableField(value = "modify_user_id")
    private Long modifyUserId;
    /**
     * 修改日期
     */
    @TableField(value = "modify_time")
    private Date modifyTime;

    @TableField(exist = false)
    private List<Risk> risks;
    @TableField(exist = false)
    private List<Industry> industries;
}