package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_illegal_behavior")
public class TRptIllegalBehavior {
  /**
   *唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;
  /**
   *违法清单id
   */
  @TableField("check_list_id")
  private long checkListId;
  /**
   *违法行为
   */
  @TableField("behavior")
  private String behavior;
  /**
   *该行为数量
   */
  @TableField("number")
  private int number;
  /**
   *年份
   */
  @TableField("year")
  private int year;
  /**
   *地区名
   */
  @TableField("name")
  private String name;
  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;

}
