package com.example.eps.ins.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: <消息用户关联表实体>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Data
@TableName("t_message_user_see")
public class MessageUserSee implements Serializable{
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 5737768244997356643L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 消息表id
     */
    @TableField(value = "message_id")
    private Long messageId;
    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Long userId;
    /**
     * 用户对消息的状态 0-未读  1-已读
     */
    @TableField(value = "status")
    private String status;
    /**
     * 用户类型1-运营端；2-企业
     */
    @TableField(value = "type")
    private Integer type;
    /**
     * 用户类型1-运营端；2-企业
     */
    @TableField(value = "create_time")
    private String createTime;

    /**
     * 企业名称
     */
    @TableField(value = "enterprise_name")
    private String enterpriseName;
    /**
     * 联系电话
     */
    @TableField(value = "enterprise_phone")
    private String enterprisePhone;
    /**
     * 所在地区
     */
    @TableField(value = "region_id")
    private Long regionId;
    /**
     * 用户类型1-运营端；2-企业
     */
    @TableField(value = "risk_status")
    private Integer riskStatus;
}