package com.example.eps.ins.common.dto.riskPush;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data 
public class OptParam {
    @JsonProperty("FmodConfig") private FmodConfig fmodConfig;
    @JsonProperty("AlarmTimes") private Integer alarmTimes;
    @JsonProperty("HaveAlarm") private Integer haveAlarm;
    @JsonProperty("Compare") private Integer compare;
    @JsonProperty("ObjectName") private String objectName;
}
