package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
* 风险类型表 Entity
*
* <AUTHOR>
* @date 2021-10-20 13:49:41
*/
@Data
@TableName("t_risk")
public class Risk {

    /**
     * 全局唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 风险类型名
     */
    @TableField("name")
    private String name;

    /**
     * 上级ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 风险项编码
     */
    @TableField("code")
    private String code;

    /**
     * 风险项描述
     */
    @TableField("risk_desc")
    private String riskDesc;
}