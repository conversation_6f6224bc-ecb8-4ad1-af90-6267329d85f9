package com.example.eps.ins.common.exception;

import java.util.LinkedList;
import java.util.List;

public class ApplicationException extends RuntimeException {
    /**
     * The serial version UID should be generated by the IDE and can not be changed unless the fields of the class changed.
     */
    private static final long serialVersionUID = -3570297377815700173L;

    protected final transient List<ApplicationError> errors = new LinkedList<>();

    /**
     * The constructor to initialize the application exception with one application error.
     *
     * @param exceptionMessage The error message.
     * @param applicationError The application error model.
     */
    public ApplicationException(String exceptionMessage, ApplicationError applicationError) {
        super(exceptionMessage);
        this.errors.add(applicationError);
    }

    /**
     * The constructor to initialize the application exception with a list of application errors.
     *
     * @param exceptionMessage The error message.
     * @param errors           The application errors.
     */
    public ApplicationException(String exceptionMessage, List<ApplicationError> errors) {
        super(exceptionMessage);
        if (errors != null) {
            this.errors.addAll(errors);
        }
    }

    public List<ApplicationError> getErrors() {
        return errors;
    }

    /**
     * Add an application error to the exception.
     *
     * @param applicationError The application error model.
     */
    public void addApplicationError(ApplicationError applicationError) {
        this.errors.add(applicationError);
    }

    /**
     * Add an array of application errors to the exception.
     *
     * @param applicationErrors The array of errors.
     */
    public void addApplicationErrors(List<ApplicationError> applicationErrors) {
        this.errors.addAll(applicationErrors);
    }
}
