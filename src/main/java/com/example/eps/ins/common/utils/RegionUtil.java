package com.example.eps.ins.common.utils;

import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.example.eps.ins.common.constant.RedisConstant.ALL_REGION;


@Service
public class RegionUtil {
    @Autowired
    RedisService redisService;
    public List<Long> getRegionIds(Long id) {
        List<Region> regions = (List<Region>) redisService.get(ALL_REGION);
        Map<Long, List<Region>> regionMap = getRegionMap(regions);
        List<Long> regionIds = new ArrayList<>();
        regionIds.add(id);
        getChildRegionIds(id, regionIds, regionMap);
        return regionIds;
    }

    /**
     * 获取子地区ID列表
     * @param id
     * @param regionIds
     * @param regionMap
     * @return
     */
    private void getChildRegionIds(Long id, List<Long> regionIds, Map<Long, List<Region>> regionMap) {
        if (regionMap.containsKey(id)) {
            List<Region> regions = regionMap.get(id);
            for (Region region : regions) {
                regionIds.add(region.getId());
                getChildRegionIds(region.getId(), regionIds, regionMap);
            }
        }
    }

    /**
     * 遍历地区列表 存放key为地区ID，value为子地区列表List，供生成地区树使用
     * @param regions
     * @return
     */
    private Map<Long, List<Region>> getRegionMap(List<Region> regions) {
        Map<Long,List<Region>> regionMap = new HashMap();
        for (Region region: regions){
            List<Region> regionList = regionMap.get(region.getParentId());
            if(null == regionList){
                regionList = new ArrayList<>();
            }
            regionList.add(region);
            regionMap.put(region.getParentId(),regionList);
        }
        return regionMap;
    }
}
