package com.example.eps.ins.common.dto.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 子设备信息DTO
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class SubDeviceInfo {
    
    /**
     * 子设备ID
     */
    @JsonProperty("id")
    private Long id;
    
    /**
     * 子设备名称
     */
    @JsonProperty("deviceName")
    private String deviceName;
    
    /**
     * 子设备序列号
     */
    @JsonProperty("serial")
    private String serial;
    
    /**
     * 子设备状态
     */
    @JsonProperty("status")
    private String status;
    
    /**
     * 子设备类型
     */
    @JsonProperty("deviceType")
    private String deviceType;
    
    /**
     * 子设备IP地址
     */
    @JsonProperty("ip")
    private String ip;
}
