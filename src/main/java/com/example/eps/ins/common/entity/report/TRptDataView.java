package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_data_view")
public class TRptDataView {
  /**
   *唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;
  /**
   *已执法完成
   */
  @TableField("complete")
  private int complete;
  /**
   *已排查隐患
   */
  @TableField("yes_check_list")
  private int yesCheckList;
  /**
   *未排查隐患
   */
  @TableField("no_check_list")
  private int noCheckList;
  /**
   *排查隐患总数
   */
  @TableField("check_list_number")
  private int checkListNumber;
  /**
   *罚款金额
   */
  @TableField("fine")
  private BigDecimal fine;
  /**
   *年份
   */
  @TableField("year")
  private int year;
  /**
   *地区
   */
  @TableField("name")
  private String name;
  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;
  /**
   *管理局名称
   */
  @TableField("dept")
  private String dept;
}
