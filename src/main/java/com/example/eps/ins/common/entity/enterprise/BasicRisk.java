package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/11/7 16:18
 * @Version: 1.0
 */
@Data
@TableName("t_basic_risk")
public class BasicRisk {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 企业名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 社信代码
     */
    @TableField("social_code")
    private String socialCode;

    /**
     * 涉燃爆粉尘
     */
    @TableField("risk_i1")
    private Integer riskI1;

    /**
     * 涉高温熔融
     */
    @TableField(value = "risk_i2")
    private Integer riskI2;

    /**
     * 涉冶金煤气
     */
    @TableField(value = "risk_i3")
    private Integer riskI3;

    /**
     * 涉氨制冷
     */
    @TableField("risk_i4")
    private Integer riskI4;

    /**
     * 有限空间
     */
    @TableField("risk_i5")
    private Integer riskI5;

    /**
     * 其他燃爆毒危化品使用
     */
    @TableField("risk_i6")
    private Integer riskI6;

    /**
     * 规上企业
     */
    @TableField("scale_status")
    private Integer scaleStatus;

    /**
     * 安全三同时或现状评价
     */
    @TableField("current_evaluation")
    private Integer currentEvaluation;

    /**
     * 安全生产标准化情况对标达标等级
     */
    @TableField("safety_bck")
    private Integer safetyBck;

    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;

    /**
     * 所属地区上级id
     */
    @TableField("region_parent_id")
    private Long regionParentId;

    /**
     * 所属地区上级id
     */
    @TableField("order_and_card")
    private Integer orderAndCard;

    /**
     * 所属地区上级id
     */
    @TableField("enterprise_id")
    private Long enterpriseId;
}
