package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2022/11/10 10:18
 * @Version: 1.0
 */
@Data
@TableName("t_warning_for_card")
public class WarningForCard {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /**
     * 企业id
     */
    @TableField("enterprise_id")
    private Long enterpriseId;
    /**
     * 企业联系人
     */
    @TableField("mobile")
    private String mobile;
    /**
     * 企业名称
     */
    @TableField("name")
    private String name;
    /**
     * 预计类别
     */
    @TableField("title")
    private String title;
    /**
     * 预警时间
     */
    @TableField("time")
    private String time;
    /**
     * 监管端说明
     */
    @TableField("remarkOne")
    private String remarkOne;
    /**
     * 企业端说明
     */
    @TableField("remarkTwo")
    private String remarkTwo;
    /**
     * 预警类别（1-企业安全标准化达标；2-两单两卡）
     */
    @TableField("type")
    private Integer type;
    /**
     * 镇街id
     */
    @TableField("region_town_id")
    private Long regionTownId;
    /**
     * 区县id
     */
    @TableField("region_county_id")
    private Long regionCountyId;
    /**
     * 预警状态
     */
    @TableField("status")
    private Integer status;
    /**
     * 预警状态
     */
    @TableField("create_id")
    private Long createId;

    /**
     * 类别（0-预警，1警告）
     */
    @TableField("remark_status")
    private Integer remarkStatus;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    /**
     * code
     */
    @TableField("code")
    private String code;
    /**
     * 未读
     */
    @TableField(exist = false)
    private Integer noSee;
    /**
     * 未读
     */
    @TableField(exist = false)
    private String address;
    /**
     * 未读
     */
    @TableField(exist = false)
    private String regionName;

    /**
     * 未读
     */
    @TableField(exist = false)
    private String riskTime;

}
