package com.example.eps.ins.common.dto.report.model;

import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
public class EmergencyPlanVO extends QueryRequest {
    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 应急预案名称
     */
    private String emergencyPlanName;
    /**
     * 应急预案文件
     */
    private String emergencyPlanFile;
    /**
     * 摘要
     */
    private String digest;

    /**
     * 1、综合预案  2、专项预案 3、现场处置方案
     */
    private Integer planType;

    /**
     * 上传时间
     */
    private Date createTime;
    /**
     * 所属地区
     */
    private Long regionId;


    private String enterpriseName;

    private List<String> created;
}
