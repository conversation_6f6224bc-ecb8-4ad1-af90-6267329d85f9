package com.example.eps.ins.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.eps.ins.common.entity.enterprise.Region;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: <消息通知表实体>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Data
@TableName("t_message")
public class Message implements Serializable{
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 6516800943817041235L;

    /**
     * 主键自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 消息类型
     */
    @TableField(value = "type")
    private String type;
    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;
    /**
     * 消息内容
     */
    @TableField(value = "content")
    private String content;
    /**
     * 消息摘要
     */
    @TableField(value = "digest")
    private String digest;
    /**
     * 附件链接列表，json格式
     */
    @TableField(value = "attachment_path")
    private String attachmentPath;
    /**
     * 发布时间
     */
    @TableField(value = "publish_time")
    private Date publishTime;
    /**
     * 发布人id
     */
    @TableField(value = "user_id")
    private Long userId;
    /**
     * 企业查看权限（0 无  1可查看）
     */
    @TableField(value = "message_status")
    private Integer messageStatus;
    /**
     * 创建人所属地区id
     */
    @TableField(value = "create_region_id")
    private Long createRegionId;
    /**
     * 发布人
     */
    @TableField(value = "publish_nick_name")
    private String publishNickName;
    /**
     * 消息状态，是否为删除等状态
     */
    @TableField(value = "status")
    private String status;
    /**
     * 创建人所属地区id
     */
    @TableField(value = "message_risk")
    private String messageRisk;
    /**
     * 创建人所属地区id
     */
    @TableField(value = "message_industry")
    private String messageIndustry;
    /**
     * 创建人所属地区id
     */
    @TableField(exist = false)
    private String seeStatus;

    /**
     * 未读企业数量
     */
    @TableField(exist = false)
    private Integer noSeeNum;

    public static class MessageType{
        /**
         * 公告
         */
        public static final String NOTICE = "notice";
        /**
         * 通知
         */
        public static final String ADVICE = "advice";
        /**
         * 预警
         */
        public static final String WARNING = "warning";


    }

    public static class StatusType{
        /**
         * 未发布
         */
        public static final String NOT_PUBLISH = "1";
        /**
         * 已发布
         */
        public static final String PUBLISH = "2";

        /**
         * 企业查看权限无）
         */
        public static final Integer NOT_MESSAGE_STATUS = 0;
        /**
         * 企业查看权限可查看
         */
        public static final Integer MESSAGE_STATUS = 1;

        /**
         * 消息发布页面
         */
        public static final Integer MASSAGE_RELEASE = 0;
        /**
         * 消息查看页面
         */
        public static final Integer MESSAGE_SEE = 1;

        /**
         * 警告
         */
        public static final Integer WARNING=1;
        /**
         *预警
         */
        public static final Integer EARLY_WARNING=0;
    }
    @TableField(exist = false)
    private List<String> publishTimeSlot;

    @TableField(exist = false)
    private List<Region> regions;

    @TableField(exist = false)
    private List<Long> regionIds;

    /**
     * 区分消息发布、消息查看页面
     * 0 消息发布
     * 1 消息查看
     */
    @TableField(exist = false)
    private Integer pageType;
//    /**
//     * 关联的权限地区ids
//     */
//    private String regionIds;
}