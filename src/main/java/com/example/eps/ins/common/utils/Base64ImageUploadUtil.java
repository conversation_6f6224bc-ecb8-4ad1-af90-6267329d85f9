package com.example.eps.ins.common.utils;

import com.example.eps.ins.service.FdfsClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Base64 图片上传工具类
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class Base64ImageUploadUtil {

    private final FdfsClientService fdfsClientService;

    /**
     * 处理 base64 图片上传
     *
     * @param base64Data base64 图片数据
     * @param prefix     文件名前缀
     * @return 上传后的文件 URL，如果上传失败返回原始数据
     */
    public String uploadBase64Image(String base64Data, String prefix) {
        if (!StringUtils.hasText(base64Data)) {
            return base64Data;
        }

        try {
            // 检查是否是 base64 格式的图片数据
            if (!isBase64Image(base64Data)) {
                log.debug("数据不是 base64 图片格式，跳过上传: {}", prefix);
                return base64Data;
            }

            log.info("检测到 base64 图片数据，开始上传到 FastDFS，前缀: {}", prefix);

            // 解码 base64 数据
            byte[] imageBytes = decodeBase64Image(base64Data);
            if (imageBytes == null || imageBytes.length == 0) {
                log.error("base64 图片解码失败，前缀: {}", prefix);
                return base64Data;
            }

            // 检查图片大小限制（避免超大图片导致超时）
            long imageSizeKB = imageBytes.length / 1024;
            if (imageSizeKB > 10240) { // 10MB 限制
                log.warn("图片过大 {}KB，可能导致上传超时，前缀: {}", imageSizeKB, prefix);
            }

            // 检测图片格式
            String extension = detectImageFormat(imageBytes);

            // 生成文件名
            String fileName = generateImageFileName(prefix, extension);

            // 设置上传超时时间并上传到 FastDFS
            long startTime = System.currentTimeMillis();
            Map<String, Object> uploadResult = fdfsClientService.uploadFile(
                    imageBytes,
                    imageBytes.length,
                    fileName,
                    extension
            );
            long uploadTime = System.currentTimeMillis() - startTime;

            log.info("FastDFS 上传耗时: {}ms, 前缀: {}", uploadTime, prefix);

            if (uploadResult != null && uploadResult.containsKey("fileUrl")) {
                String fileUrl = (String) uploadResult.get("fileUrl");
                log.info("图片上传成功，前缀: {}, 文件URL: {}, 耗时: {}ms", prefix, fileUrl, uploadTime);
                return fileUrl;
            } else {
                log.error("图片上传失败，返回结果为空，前缀: {}", prefix);
            }

        } catch (Exception e) {
            log.error("处理 base64 图片上传异常，前缀: {}", prefix, e);

            // 如果是超时异常，特别记录
            if (e.getCause() instanceof java.net.SocketTimeoutException) {
                log.error("FastDFS 上传超时，前缀: {}", prefix);
            }
        }

        // 如果上传失败，返回原始值
        return base64Data;
    }

    /**
     * 异步处理 base64 图片上传
     *
     * @param base64Data base64 图片数据
     * @param prefix     文件名前缀
     * @return 异步上传结果
     */
    @Async
    public CompletableFuture<String> uploadBase64ImageAsync(String base64Data, String prefix) {
        try {
            String result = uploadBase64Image(base64Data, prefix);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步上传 base64 图片失败，前缀: {}", prefix, e);
            CompletableFuture<String> future = new CompletableFuture<>();
            future.completeExceptionally(e);
            return future;
        }
    }

    /**
     * 批量处理 base64 图片上传
     *
     * @param base64DataArray base64 图片数据数组
     * @param prefix          文件名前缀
     * @return 上传后的文件 URL 数组
     */
    public String[] uploadBase64Images(String[] base64DataArray, String prefix) {
        if (base64DataArray == null || base64DataArray.length == 0) {
            return base64DataArray;
        }

        String[] result = new String[base64DataArray.length];
        for (int i = 0; i < base64DataArray.length; i++) {
            result[i] = uploadBase64Image(base64DataArray[i], prefix + "_" + i);
        }
        return result;
    }

    /**
     * 检查是否是 base64 格式的图片数据
     */
    private boolean isBase64Image(String data) {
        if (!StringUtils.hasText(data)) {
            return false;
        }

        // 检查是否以常见的 base64 图片前缀开头
        if (data.startsWith("data:image/")) {
            return true;
        }

        // 检查是否是纯 base64 数据（长度合理且包含 base64 字符）
        if (data.length() > 100 && data.matches("^[A-Za-z0-9+/]*={0,2}$")) {
            return true;
        }

        return false;
    }

    /**
     * 解码 base64 图片数据
     */
    private byte[] decodeBase64Image(String base64Data) {
        try {
            String actualData = base64Data;

            // 如果包含 data URL 前缀，去除它
            if (base64Data.contains(",")) {
                actualData = base64Data.substring(base64Data.indexOf(",") + 1);
            }

            // 解码 base64 数据
            return Base64.getDecoder().decode(actualData);

        } catch (Exception e) {
            log.error("解码 base64 图片数据失败", e);
            return null;
        }
    }

    /**
     * 检测图片格式
     */
    private String detectImageFormat(byte[] imageBytes) {
        if (imageBytes == null || imageBytes.length < 4) {
            return "jpg"; // 默认格式
        }

        // 检查文件头来确定图片格式
        if (imageBytes[0] == (byte) 0xFF && imageBytes[1] == (byte) 0xD8) {
            return "jpg";
        } else if (imageBytes[0] == (byte) 0x89 && imageBytes[1] == (byte) 0x50 
                && imageBytes[2] == (byte) 0x4E && imageBytes[3] == (byte) 0x47) {
            return "png";
        } else if (imageBytes[0] == (byte) 0x47 && imageBytes[1] == (byte) 0x49 
                && imageBytes[2] == (byte) 0x46) {
            return "gif";
        } else if (imageBytes[0] == (byte) 0x42 && imageBytes[1] == (byte) 0x4D) {
            return "bmp";
        }

        return "jpg"; // 默认格式
    }

    /**
     * 生成图片文件名
     */
    private String generateImageFileName(String prefix, String extension) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss_SSS");
        String timestamp = sdf.format(new Date());
        return String.format("%s_%s.%s", prefix, timestamp, extension);
    }

    /**
     * 获取 base64 数据的大小（字节）
     */
    public long getBase64DataSize(String base64Data) {
        if (!StringUtils.hasText(base64Data)) {
            return 0;
        }

        try {
            byte[] decoded = decodeBase64Image(base64Data);
            return decoded != null ? decoded.length : 0;
        } catch (Exception e) {
            log.warn("计算 base64 数据大小失败", e);
            return 0;
        }
    }

    /**
     * 验证 base64 图片数据是否有效
     */
    public boolean validateBase64Image(String base64Data) {
        if (!isBase64Image(base64Data)) {
            return false;
        }

        byte[] imageBytes = decodeBase64Image(base64Data);
        if (imageBytes == null || imageBytes.length == 0) {
            return false;
        }

        // 检查是否是有效的图片格式
        String format = detectImageFormat(imageBytes);
        return format != null && !format.isEmpty();
    }

    /**
     * 压缩 base64 图片（如果需要）
     * 注意：这里只是一个示例，实际压缩需要使用图片处理库
     */
    public String compressBase64Image(String base64Data, int maxSizeKB) {
        // TODO: 实现图片压缩逻辑
        // 可以使用 Thumbnailator 或其他图片处理库
        return base64Data;
    }
}
