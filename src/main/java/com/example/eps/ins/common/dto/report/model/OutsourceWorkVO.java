package com.example.eps.ins.common.dto.report.model;


import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OutsourceWorkVO  extends QueryRequest {

  /**
   * 全局唯一标识符
   */
  private Long id;

  /**
   * 外委作业类型：
   * 1-有限空间作业、2-建筑作业、3-装修作业、
   * 4-清掏作业、5-设备维护维修作业、6-特种设备、7-其他作业
   */
  private Integer workType;

  /**
   * 外委公司名称
   */
  private String workEnterprise;

  /**
   * 合同或安全协议等附件上传
   */
  private String workFile;
  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 地区id
   */
  private Long regionId;

  /**
   * 创建人
   */
  private Long creatorId;

  private String enterpriseName;

  private List<String> created;
}
