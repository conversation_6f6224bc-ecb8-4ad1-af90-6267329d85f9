package com.example.eps.ins.common.dto.report.model;

import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class MessageVo {
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 6516800943817041235L;

    /**
     * 主键自增id
     */
    private Long id;
    /**
     * 消息类型
     */
    @NonNull
    private String type;
    /**
     * 标题
     */
    @NotNull
    private String title;
    /**
     * 摘要
     */
    @NotNull
    private String digest;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 附件.
     */
    private String attachment;
    /**
     * 发布时间
     */
    private Date publishTime;
    /**
     * 发布人id
     */
    private Long userId;
    /**
     * 发布人
     */
    private String publishNickName;
    /**
     * 消息状态，是否为删除等状态
     */
    private String status;

//    private List<MessageRegion> regionIds;

    /**
     * 关联的权限地区ids
     */
    private List<Long> regionIds;

    /**
     * 企业查看权限（0 无  1可查看）
     */
    private Integer messageStatus;
    /**
     * 可查看四涉一限一使用企业
     */
    private List<Long> riskIds;
    /**
     * 可查看行业
     */
    private List<Long> industryIds;
}
