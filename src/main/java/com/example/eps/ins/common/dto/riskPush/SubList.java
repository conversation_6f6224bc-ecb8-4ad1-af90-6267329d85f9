package com.example.eps.ins.common.dto.riskPush;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SubList {
    @JsonProperty("TrainPlatRaw") private List<TrainPlatRaw> trainPlatRaw;
    @JsonProperty("LocationInfo") private List<LocationInfo> locationInfo;
    @JsonProperty("ThresholdDetail") private Map<String, Object> thresholdDetail;
    @JsonProperty("ImageInfo") private ImageInfo imageInfo;
    @JsonProperty("Config") private String config; // 原始JSON字符串
}
