package com.example.eps.ins.common.entity.system;

import com.example.eps.ins.common.converter.StringTrimConverter;
import com.wuwenze.poi.annotation.Excel;
import com.wuwenze.poi.annotation.ExcelField;
import lombok.Data;

/**
 * 企业信息表 Entity
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:42
 */
@Data
@Excel("企业信息导入")
public class EnterpriseImport {

    /**
     * 企业名称
     */
    @ExcelField(value = "单位名称", readConverter = StringTrimConverter.class)
    private String name;

    /**
     * 社信代码
     */
    @ExcelField(value = "社信代码", readConverter = StringTrimConverter.class)
    private String socialCode;

    /**
     * 行业
     */
    @ExcelField(value = "行业类别", readConverter = StringTrimConverter.class)
    private String industry;

    /**
     * 企业性质
     */
    @ExcelField(value = "单位性质", readConverter = StringTrimConverter.class)
    private String nature;

    /**
     * 地区-区县ID
     */
    @ExcelField(value = "所属区县", readConverter = StringTrimConverter.class)
    private String regionCounty;

    /**
     * 地区-镇街ID
     */
    @ExcelField(value = "所属镇街", readConverter = StringTrimConverter.class)
    private String regionTown;

    /**
     * 从业人数
     */
    @ExcelField(value = "从业人数", readConverter = StringTrimConverter.class)
    private String peopleNumber;

    /**
     * 法定代表人姓名
     */
    @ExcelField(value = "法人代表/主要负责人", readConverter = StringTrimConverter.class)
    private String legalRepresentativeName;

    /**
     * 法定代表人联系电话
     */
    @ExcelField(value = "法人电话", readConverter = StringTrimConverter.class)
    private String legalRepresentativePhone;

    /**
     * 安全负责人姓名
     */
    @ExcelField(value = "安全负责人", readConverter = StringTrimConverter.class)
    private String safetyDirectorName;

    /**
     * 安全负责人电话
     */
    @ExcelField(value = "安全负责人电话", readConverter = StringTrimConverter.class)
    private String safetyDirectorPhone;

    /**
     * 专职安全员人数
     */
    @ExcelField(value = "安全员专职人数")
    private Integer safetyFullNumber;

    /**
     * 兼职安全员人数
     */
    @ExcelField(value = "安全员兼职人数")
    private Integer safetyPartNumber;

    /**
     * 企业地址
     */
    @ExcelField(value = "注册地址", readConverter = StringTrimConverter.class)
    private String address;
    /**
     * 生产经营状态
     */
    @ExcelField(value = "生产经营状态", readConverter = StringTrimConverter.class)
    private String productionStatus;

//    /**
//     * 有无安全三同时或现状评价
//     */
//    @ExcelField(value = "有无安全三同时或现状评价", readConverter = StringTrimConverter.class)
//    private String isCurrentEvaluation;
//
//    /**
//     * 安全三同时或现状评价
//     */
//    @ExcelField(value = "安全三同时或现状评价时间", readConverter = StringTrimConverter.class)
//    private String currentEvaluation;

    /**
     * 是否达标
     */
    @ExcelField(value = "安全生产标准化是否达标", readConverter = StringTrimConverter.class)
    private String isSafety;

    /**
     * 安全生产标准化情况对标达标等级
     */
    @ExcelField(value = "对标达标等级", readConverter = StringTrimConverter.class)
    private String safetyBckLevel;
    /**
     * 安全生产标准化情况对标达标等级
     */
    @ExcelField(value = "证书编号", readConverter = StringTrimConverter.class)
    private String certificateCode;
    /**
     * 安全生产标准化情况对标达标时间
     */
    @ExcelField(value = "对标达标时间", readConverter = StringTrimConverter.class)
    private String safetyBckTime;

    /**
     * 安全生产标准化情况专业达标时间
     */
    @ExcelField(value = "专业达标时间", readConverter = StringTrimConverter.class)
    private String safetyMajorTime;

    /**
     * 安全生产标准化情况岗位达标时间
     */
    @ExcelField(value = "岗位达标时间", readConverter = StringTrimConverter.class)
    private String safetyPostTime;

    /**
     * 是否具备
     */
    @ExcelField(value = "应急预案是否具备", readConverter = StringTrimConverter.class)
    private String isEmergency;

    /**
     * 应急预案
     */
    @ExcelField(value = "应急预案是否评审", readConverter = StringTrimConverter.class)
    private String emergencyPlanReview;

    /**
     * 应急预案
     */
    @ExcelField(value = "应急预案是否备案", readConverter = StringTrimConverter.class)
    private String emergencyPlanRecord;

    /**
     * 工商保险
     */
    @ExcelField(value = "工商保险", readConverter = StringTrimConverter.class)
    private String isIndustrial;

    /**
     * 安全责任保险
     */
    @ExcelField(value = "安全责任保险", readConverter = StringTrimConverter.class)
    private String isLiability;
    /**
     * 是否大型商贸
     */
    @ExcelField(value = "是否大型商贸", readConverter = StringTrimConverter.class)
    private String tradeType;
    /**
     * 两单两卡创建情况
     */
    @ExcelField(value = "两单两卡创建情况", readConverter = StringTrimConverter.class)
    private String orderAndCard;
    /**
     * 规上/规下企业
     */
    @ExcelField(value = "规上/规下企业", readConverter = StringTrimConverter.class)
    private String scaleStatus;
    /**
     * 公司简介
     */
    @ExcelField(value = "公司简介", readConverter = StringTrimConverter.class)
    private String enterpriseIntroduce;

    /**
     * 登录手机号
     */
    @ExcelField(value = "登录手机号", readConverter = StringTrimConverter.class)
    private String loginPhone;
}