package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
@TableName("t_hidden_danger")
public class HiddenDanger {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;
    /**
     * 隐患排查制度名称
     */
    @TableField("hidden_danger_name")
    private String hiddenDangerName;
    /**
     * 隐患排查制度维护文件
     */
    @TableField("hidden_danger_file")
    private String hiddenDangerFile;
    /**
     * 摘要
     */
    @TableField("digest")
    private String digest;

    /**
     * 上传时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;

    @TableField(exist = false)
    private String enterpriseName;

    @TableField(exist = false)
    private List<String> created;
}
