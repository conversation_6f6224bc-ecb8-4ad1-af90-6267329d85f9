package com.example.eps.ins.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.entity.enterprise.EnterpriseRisk;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.example.eps.ins.common.constant.EnterpriseConstant.*;

@Slf4j
public class EnterpriseUtil {
    private static final String RISK_SPLIT_I = "i";

    /**
     * 获取企业风险
     */
    public static List<EnterpriseRisk> generateEnterpriseRisk(long enterpriseId, JSONObject jsonObject) {
        List<EnterpriseRisk> enterpriseRisks = new ArrayList<>();
        if (Objects.nonNull(jsonObject)) {
            for (Map.Entry<String, String> entry : RISK_ENTERPRISE_IDS.entrySet()) {
                String mapKey = entry.getKey();
                String mapValue = entry.getValue();
                JSONObject jsonObject1 = jsonObject.getJSONObject(mapKey);
                if (jsonObject1.getBooleanValue(mapValue)) {
                    long riskId = getRiskId(mapKey);
                    enterpriseRisks.add(new EnterpriseRisk(enterpriseId, riskId, ""));
                    if (RISK_QUERY_ENTERPRISE_IDS.containsKey(mapKey)) {
                        String queryValue = RISK_QUERY_ENTERPRISE_IDS.get(mapKey);
                        if (jsonObject1.toJSONString().indexOf(queryValue) > -1) {
                            long queryRiskId = getRiskId(queryValue);
                            enterpriseRisks.add(new EnterpriseRisk(enterpriseId, queryRiskId, ""));
                        }
                    }
                }
            }
        }
        return enterpriseRisks;
    }

    /**
     * 封装企业风险
     */
    public static void generateEnterpriseRisk(long enterpriseId, Map<String, Object> jsonObject,
                                              List<EnterpriseRisk> enterpriseRisks) {
        for (Map.Entry entry : jsonObject.entrySet()) {
            String key = entry.getKey().toString();
            Object entryValue = entry.getValue();
            try {
                long riskId = getRiskId(key);
                String value = "";
                if (entryValue instanceof ArrayList) {
                    List jsonArray = (ArrayList) entryValue;
                    for (int i = 0; i < jsonArray.size(); i++) {
                        Object object = jsonArray.get(i);
                        if (object instanceof String) {
                            long selectRiskId = getRiskId((String) object);
                            enterpriseRisks.add(new EnterpriseRisk(enterpriseId, selectRiskId, ""));
                        }
                    }
                } else {
                    if ((entryValue instanceof Boolean)) {
                        value = entry.getValue().toString();
                    } else if (entryValue instanceof String) {
                        if (((String) entryValue).startsWith(RISK_SPLIT_I)) {
                            try {
                                long selectRiskId = getRiskId((String) entryValue);
                                enterpriseRisks.add(new EnterpriseRisk(enterpriseId, selectRiskId, ""));
                            } catch (Exception e) {
                                value = entryValue.toString();
                            }
                        } else {
                            value = entryValue.toString();
                        }
                    }
                }
                enterpriseRisks.add(new EnterpriseRisk(enterpriseId, riskId, value));
            } catch (Exception e) {
                log.error("风险类型有误，请排查", e, jsonObject.toString());
            }
        }
    }

    /**
     * 判断重点企业
     *
     * @param enterprise
     * @param enterpriseRisks
     * @return
     */
    public static int getEnterpriseKey(Enterprise enterprise, List<EnterpriseRisk> enterpriseRisks) {
        int keyEnterprise = ENTERPRISE_KEY_OTHER;
        if (Objects.nonNull(enterpriseRisks) && !enterpriseRisks.isEmpty()) {
            boolean isHighTemperature = false;
            boolean isCombustion = false;
            boolean isMetallurgy = false;
            boolean isDeepWell = false;
            for (EnterpriseRisk enterpriseRisk : enterpriseRisks) {
                if (ENTERPRISE_RISK_COMBUSTION == enterpriseRisk.getRiskId()) {
                    isCombustion = true;
                } else if (ENTERPRISE_RISK_HIGH_TEMPERATURE == enterpriseRisk.getRiskId()) {
                    isHighTemperature = true;
                } else if (ENTERPRISE_RISK_METALLURGY == enterpriseRisk.getRiskId()) {
                    isMetallurgy = true;
                } else if (ENTERPRISE_RISK_DEEP_WELL == enterpriseRisk.getRiskId()) {
                    isDeepWell = true;
                }
            }
            if (enterprise.getIndustryId() == ENTERPRISE_INDUSTRY_METALLURGY && isHighTemperature && isMetallurgy) {
                keyEnterprise = ENTERPRISE_KEY_STEEL;
            } else if (enterprise.getIndustryId() == ENTERPRISE_INDUSTRY_COLORED && isHighTemperature && isDeepWell) {
                keyEnterprise = ENTERPRISE_KEY_ALUMINIUM;
            } else if (isCombustion) {
                keyEnterprise = ENTERPRISE_KEY_DUST;
            }
        }
        return keyEnterprise;
    }

    /**
     * 获取风险ID
     */
    private static long getRiskId(String risk) {
        return Long.parseLong((risk).substring(1));
    }

}
