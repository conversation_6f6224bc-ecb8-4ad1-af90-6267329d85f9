package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
@TableName("t_emergency_plan")
public class EmergencyPlan {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;
    /**
     * 应急预案名称
     */
    @TableField("emergency_plan_name")
    private String emergencyPlanName;
    /**
     * 应急预案文件
     */
    @TableField("emergency_plan_file")
    private String emergencyPlanFile;
    /**
     * 摘要
     */
    @TableField("digest")
    private String digest;

    /**
     * 1、综合预案  2、专项预案 3、现场处置方案
     */
    @TableField("plan_type")
    private Integer planType;

    /**
     * 上传时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;


    @TableField(exist = false)
    private String enterpriseName;

    @TableField(exist = false)
    private List<String> created;
}
