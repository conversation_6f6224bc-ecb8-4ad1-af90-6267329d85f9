package com.example.eps.ins.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public final class HttpClientUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpClientUtil.class);
    private static final int ERROR_CODE = 500;
    private static final int SOCKET_TIMEOUT = 1000*60*15;
    private static final int CONNECT_TIMEOUT = 1000*60*5;

    private HttpClientUtil() {

    }
    /**
     * Do get request.
     *
     * @param url The request url.
     * @return The response contain two params statusCode and result，statusCode is http status code，result is response data.
     */
    public static Response doGet(String url) {
        return doGet(url, null);
    }

    /**
     * Do get request.
     *
     * @param url The request url.
     * @param map The request params.
     * @return The response contain two params statusCode and result，statusCode is http status code，result is response data.
     */
    public static Response doGet(String url, Map<String, String> map) {
        return doGet(url, map, null);
    }

    /**
     * Do get request with custom headers.
     *
     * @param url The request url.
     * @param map The request params.
     * @param headers The request headers.
     * @return The response contain two params statusCode and result，statusCode is http status code，result is response data.
     */
    public static Response doGet(String url, Map<String, String> map, Map<String, String> headers) {
        URI uri;
        Response response = new Response();
        try {
            URIBuilder uriBuilder = new URIBuilder(url);
            List<NameValuePair> nameValuePairList = buildRequestParams(map);
            if (!CollectionUtils.isEmpty(nameValuePairList)) {
                uriBuilder.addParameters(nameValuePairList);
            }
            uri = uriBuilder.build();
        } catch (URISyntaxException e) {
            LOGGER.error("Build URL error：", e);
            response.setStatusCode(ERROR_CODE);
            response.setResult("Build URL error");
            return response;
        }
        HttpGet get = new HttpGet(uri);

        // Add custom headers
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                get.setHeader(header.getKey(), header.getValue());
            }
        }

        LOGGER.info(uri.toString());
        return doRequest(get);
    }

    /**
     * Do POST request.
     *
     * @param url The request url.
     * @param map The request params.
     * @return The response contain two params statusCode and result，statusCode is http status code，result is response data.
     */
    public static Response doPost(String url, Map<String, String> map) {
        Response response = new Response();
        List<NameValuePair> nameValuePairs = buildRequestParams(map);
        HttpPost post = new HttpPost(url);
        try {
            post.setEntity(new UrlEncodedFormEntity(nameValuePairs, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Add POST param error：", e);
            response.setStatusCode(ERROR_CODE);
            response.setResult("Add POST param error");
            return response;
        }
        return doRequest(post);
    }

    /**
     * Do POST request.
     *
     * @param url The request url.
     * @param params The request params.
     * @return The response contain two params statusCode and result，statusCode is http status code，result is response data.
     */
    public static Response doPost(String url, String params) {
        Response response = new Response();
        HttpPost post = new HttpPost(url);
        try {
            post.setEntity(new StringEntity(params, "UTF-8"));
        } catch (Exception e) {
            LOGGER.error("Add POST param error：", e);
            response.setStatusCode(ERROR_CODE);
            response.setResult("Add POST param error");
            return response;
        }
        return doRequest(post);
    }

    /**
     * 将map转换成url
     *
     * @param map
     * @return
     */
    public static String getUrlParamsByMap(Map<String, Object> map) {
        if (map == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            sb.append(entry.getKey() + "=" + entry.getValue());
            sb.append("&");
        }
        String params = sb.toString();
        if (params.endsWith("&")) {
            params = StringUtils.substringBeforeLast(params, "&");
        }
        return params;
    }

    /**
     * Build request params.
     *
     * @param map The params list.
     * @return The NameValuePair list.
     */
    private static List<NameValuePair> buildRequestParams(Map<String, String> map) {
        if (map == null) {
            return null;
        }
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            nameValuePairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }
        return nameValuePairs;
    }

    public static Response doPostStream(String url, String params) throws IOException {
        Response response = new Response();
        HttpURLConnection connection = null;
        int responseCode = 200;
        StringBuffer buffer = new StringBuffer();
        try {
            URL postUrl = new URL(url);
            connection = (HttpURLConnection) postUrl.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestMethod("POST");
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);
            connection.setRequestProperty("Content-Type",
                    "application/x-www-form-urlencoded");
            connection.connect();
            DataOutputStream out = new DataOutputStream(
                    connection.getOutputStream());
            out.writeBytes(params);
            out.flush();
            out.close();
            responseCode = connection.getResponseCode();
            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    connection.getInputStream(), "UTF-8"));// 设置编码,否则中文乱码
            String lines;
            while ((lines = reader.readLine()) != null) {
                buffer.append(lines);
            }
        } catch (Exception e) {
            buffer.append(e.getMessage());
        }
        String result = buffer.toString();
        if(!result.contains("\"res_code\":\"0\"")){
            responseCode = 500;
        }
        response.setStatusCode(responseCode);
        response.setResult(result);
        return response;
    }

    /**
     * Do request.
     *
     * @param requestBase The request.
     * @return The tms response.
     */
    private static Response doRequest(HttpRequestBase requestBase) {
        Response tmsResponse = new Response();
        String responseBody;
        RequestConfig requestConfig = RequestConfig.custom().
                setSocketTimeout(SOCKET_TIMEOUT).setConnectTimeout(CONNECT_TIMEOUT).build();
        requestBase.setConfig(requestConfig);
        try (CloseableHttpClient httpClient = HttpClients.createDefault();
             CloseableHttpResponse response = httpClient.execute(requestBase);) {
            int statusCode = response.getStatusLine().getStatusCode();
            HttpEntity httpEntity = response.getEntity();
            responseBody = EntityUtils.toString(httpEntity);
            tmsResponse.setStatusCode(statusCode);
        } catch (IOException e) {
            LOGGER.error("Send HTTP request error, Exception:[{}], URL:[{}]", e, requestBase.getURI());
            tmsResponse.setStatusCode(ERROR_CODE);
            tmsResponse.setResult("Send HTTP request error");
            if (e instanceof SocketTimeoutException){
                tmsResponse.setResult("Send HTTP socket timeout");
            }
            return tmsResponse;
        }
        tmsResponse.setResult(responseBody);
        return tmsResponse;
    }
}
