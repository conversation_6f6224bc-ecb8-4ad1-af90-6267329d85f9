package com.example.eps.ins.common.dto.report.model;

import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
public class EmergencydrillVO extends QueryRequest {
    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 应急演练名称
     */
    private String emergencyDrillName;
    /**
     * 摘要
     */
    private String emergencyDrillDigest;
    /**
     * 应急演练文件
     */
    private String emergencyDrillFile;
    /**
     * 应急演练图片
     */
    private String emergencyDrillImage;
    /**
     * 演练时间
     */
    private String emergencyDrillTime;
    /**
     * 上传时间
     */
    private Date createTime;
    /**
     * 所属地区
     */
    private Long regionId;

    private String enterpriseName;

    private List<String> created;
}
