package com.example.eps.ins.common.dto.riskPush;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CityEventObject {
    // 基础字段（严格匹配JSON大小写）
    @JsonProperty("Confidence") private Double confidence;
    @JsonProperty("Census") private Double census;
    @JsonProperty("Unit") private Integer unit;
    @JsonProperty("Id") private String id;
    @JsonProperty("BigUrl") private String bigUrl;
    @JsonProperty("ImgUrl") private String imgUrl;
    @JsonProperty("CreateTime") private String createTime;
    @JsonProperty("MarkTime") private String markTime;
    @JsonProperty("Serialnumber") private String serialNumber;
    @JsonProperty("CameraId") private String cameraId;
    @JsonProperty("SlaveIP") private String slaveIp;
    @JsonProperty("EventSource") private Integer eventSource;
    @JsonProperty("StartTime") private String startTime;
    @JsonProperty("EndTime") private String endTime;
    @JsonProperty("HaveAlarm") private Integer haveAlarm;
    @JsonProperty("EventType") private Integer eventType;
    
    // 嵌套结构
    @JsonProperty("SubList") private SubList subList;
    @JsonProperty("ExtInfo") private ExtInfo extInfo;
    @JsonProperty("MachineCode") private String machineCode;
    @JsonProperty("DeviceId") private String deviceId;
    @JsonProperty("cameraName") private String cameraName;
}
