package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
@TableName("t_equipment_maintain")
public class EquipmentMaintain {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 设备名称
     */
    @TableField("equipment_name")
    private String equipmentName;
    /**
     * 设备型号
     */
    @TableField("equipment_model")
    private String equipmentModel;
    /**
     * 位置
     */
    @TableField("equipment_address")
    private String equipmentAddress;
    /**
     * 保养维护日期
     */
    @TableField("maintenance_date")
    private String maintenanceDate;

    /**
     * 保养维护内容
     */
    @TableField("maintenance_details")
    private String maintenanceDetails;
    /**
     * 保养维护单位
     */
    @TableField("maintenance_company")
    private String maintenanceCompany;
    /**
     * 保养人员
     */
    @TableField("maintenance_people")
    private String maintenancePeople;
    /**
     * 现场保养图片
     */
    @TableField("maintenance_image")
    private String maintenanceImage;
    /**
     * 现场保养图片
     */
    @TableField("maintenance_file")
    private String maintenanceFile;
    /**
     * 新建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;
    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;
    /**
     * 所属地区
     */
    @TableField("maintenance_type")
    private Integer maintenanceType;

    @TableField(exist = false)
    private String enterpriseName;

    @TableField(exist = false)
    private List<String> created;
}
