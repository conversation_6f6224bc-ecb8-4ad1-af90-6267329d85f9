package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_year_check")
public class TRptYearCheck {
  /**
   *唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;
  /**
   *已完成检查
   */
  @TableField("complete")
  private long complete;
  /**
   *未开始检查
   */
  @TableField("no_check")
  private long noCheck;
  /**
   *年度检查总数
   */
  @TableField("number")
  private long number;
  /**
   *年份
   */
  @TableField("year")
  private int year;
  /**
   *地区名
   */
  @TableField("name")
  private String name;
  /**
   *管理局名
   */
  @TableField("dept")
  private String dept;
  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;

}
