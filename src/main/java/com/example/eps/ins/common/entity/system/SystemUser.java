package com.example.eps.ins.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.eps.ins.common.annotation.IsMobile;
import com.example.eps.ins.common.converter.TimeConverter;
import com.wuwenze.poi.annotation.Excel;
import com.wuwenze.poi.annotation.ExcelField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_user")
@Excel("用户信息表")
public class SystemUser implements Serializable {
    /**
     * 用户状态：有效
     */
    public static final String STATUS_VALID = "1";
    /**
     * 用户状态：锁定
     */
    public static final String STATUS_LOCK = "0";

    /**
     * 默认密码
     */
    public static final String DEFAULT_PWD = "123456";

    private static final long serialVersionUID = -4352868070794165001L;
    /**
     * 用户 ID
     */
    @TableId(value = "USER_ID", type = IdType.AUTO)
    private Long userId;

    /**
     * 用户名
     */
    @TableField("USERNAME")
    @Size(min = 1, max = 30, message = "用户长度为{range}")
    @ExcelField(value = "用户名")
    private String username;

    /**
     * 密码
     */
    @TableField("PASSWORD")
    private String password;

    /**
     * 昵称
     */
    @TableField("NICK_NAME")
    @Size(min = 1, max = 30, message = "姓名长度为{range}")
    private String nickName;

    /**
     * 地区ID
     */
    @TableField("REGION_ID")
    @NotNull(message = "所属组织不能为空")
    private Long regionId;

    /**
     * 联系电话
     */
    @TableField("MOBILE")
    @IsMobile(message = "{mobile}")
    @ExcelField(value = "联系电话")
    private String mobile;

    /**
     * 证件号
     */
    @TableField("ID_NO")
    private String idNo;

    /**
     * 状态 0锁定 1有效
     */
    @TableField("STATUS")
    @ExcelField(value = "状态", writeConverterExp = "0=锁定,1=有效")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @ExcelField(value = "创建时间", writeConverter = TimeConverter.class)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    @ExcelField(value = "修改时间", writeConverter = TimeConverter.class)
    private Date modifyTime;

    /**
     * 最近访问时间
     */
    @TableField("LAST_LOGIN_TIME")
    @ExcelField(value = "最近访问时间", writeConverter = TimeConverter.class)
    private Date lastLoginTime;

    /**
     * 创建者
     */
    @TableField("CREATE_USER_ID")
    private Long createUserId;

    /**
     * 更新者
     */
    @TableField("MODIFY_USER_ID")
    private Long modifyUserId;

    @TableField(exist = false)
    private String roleName;

    @TableField(exist = false)
    private String dept;

    @TableField(exist = false)
    @NotNull(message = "用户角色不能为空")
    private Long[] roleIds;

    @TableField(exist = false)
    private String regionName;
}
