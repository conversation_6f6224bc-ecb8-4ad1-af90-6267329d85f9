package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.*;
import com.github.dreamyoung.mprelation.JoinColumn;
import com.github.dreamyoung.mprelation.OneToOne;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
* 企业信息申报表 Entity
*
* <AUTHOR>
* @date 2021-10-20 13:49:41
*/
@Data
@TableName("t_enterprise_declaration")
public class EnterpriseDeclaration {

    /**
     * 全局唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 行业ID
     */
    @TableField("industry_id")
    private Long industryId;

    /**
     * 地区-区县ID
     */
    @TableField("region_county_id")
    private Long regionCountyId;
    /**
     * 证书编号
     */
    @TableField("certificate_code")
    private String certificateCode;
    /**
     * 地区-镇街ID
     */
    @TableField("region_town_id")
    private Long regionTownId;

    /**
     * 企业性质
     */
    @TableField("nature")
    private String nature;

    /**
     * 从业人数
     */
    @TableField("people_number")
    private String peopleNumber;

    /**
     * 生产经营状态
     */
    @TableField("production_status")
    private String productionStatus;

    /**
     * 安全三同时或现状评价
     */
    @TableField(value = "current_evaluation",updateStrategy = FieldStrategy.IGNORED)
    private String currentEvaluation;
    /**
     * 安全三同时：0-无；1-有
     */
    @TableField(value = "current_evaluation_status")
    private Integer currentEvaluationStatus;
    /**
     * 安全生产标准化 -0：有；1无
     */
    @TableField("safety_bck")
    private Integer safetyBck;
    /**
     * 安全生产标准化情况对标达标等级
     */
    @TableField(value = "safety_bck_level",updateStrategy = FieldStrategy.IGNORED)
    private String safetyBckLevel;

    /**
     * 安全生产标准化情况对标达标时间
     */
    @TableField(value = "safety_bck_time",updateStrategy = FieldStrategy.IGNORED)
    private String safetyBckTime;

    /**
     * 安全生产标准化情况专业达标时间
     */
    @TableField(value = "safety_major_time",updateStrategy = FieldStrategy.IGNORED)
    private String safetyMajorTime;

    /**
     * 安全生产标准化情况岗位达标时间
     */
    @TableField(value = "safety_post_time",updateStrategy = FieldStrategy.IGNORED)
    private String safetyPostTime;

    /**
     * 应急预案
     */
    @TableField("emergency_plan_review")
    private String emergencyPlanReview;

    /**
     * 应急预案
     */
    @TableField("emergency_plan_record")
    private String emergencyPlanRecord;

    /**
     * 安全保险
     */
    @TableField("safety_insurance")
    private String safetyInsurance;

    /**
     *  两单两卡创建情况：0-未创建；1-创建中；2-已创建
     */
    @TableField("order_and_card")
    private Integer orderAndCard;

    /**
     * 企业名称
     */
    @TableField("name")
    private String name;

    /**
     * 企业地址
     */
    @TableField("address")
    private String address;

    /**
     * 社信代码
     */
    @TableField("social_code")
    private String socialCode;

    /**
     * 安全负责人姓名
     */
    @TableField("safety_director_name")
    private String safetyDirectorName;

    /**
     * 安全负责人电话
     */
    @TableField("safety_director_phone")
    private String safetyDirectorPhone;

    /**
     * 专职安全员人数
     */
    @TableField(value = "safety_full_number", updateStrategy = FieldStrategy.IGNORED)
    private Integer safetyFullNumber;

    /**
     * 兼职安全员人数
     */
    @TableField(value = "safety_part_number", updateStrategy = FieldStrategy.IGNORED)
    private Integer safetyPartNumber;

    /**
     * 企业联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 企业联系人电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 法定代表人姓名
     */
    @TableField("legal_representative_name")
    private String legalRepresentativeName;

    /**
     * 法定代表人职务
     */
    @TableField("legal_representative_job")
    private String legalRepresentativeJob;

    /**
     * 法定代表人联系电话
     */
    @TableField("legal_representative_phone")
    private String legalRepresentativePhone;

    /**
     * 初审人
     */
    @TableField("first_reviewer_id")
    private Long firstReviewerId;

    /**
     * 初审时间
     */
    @TableField("first_review_time")
    private Date firstReviewTime;

    /**
     * 初审状态.1-通过;2-拒绝
     */
    @TableField("first_review_status")
    private Integer firstReviewStatus;

    /**
     * 初审意见
     */
    @TableField("first_review_remark")
    private String firstReviewRemark;

    /**
     * 复审人
     */
    @TableField("second_reviewer_id")
    private Long secondReviewerId;

    /**
     * 复审时间
     */
    @TableField("second_review_time")
    private Date secondReviewTime;

    /**
     * 复审状态.;1-通过;2-拒绝
     */
    @TableField("second_review_status")
    private Integer secondReviewStatus;

    /**
     * 复审意见
     */
    @TableField("second_review_remark")
    private String secondReviewRemark;

    /**
     * 企业申报状态.0-提交;1-初审通过;2-初审不通过;3-复审通过;4-复审不通过
     */
    @TableField("status")
    private Integer status;

    /**
     * 申请人手机号
     */
    @TableField("creator_mobile")
    private String creatorMobile;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modify_user_id")
    private Long modifyUserId;

    /**
     * 修改日期
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 风险项
     */
    @TableField("risks")
    private String risks;

    /**
     * 规上规下企业：0-规下；1-规上
     */
    @TableField("scale_status")
    private Integer scaleStatus;
    /**
     * 0-无；1-批发  2-仓储   零售   住宿   餐饮
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 四涉一限一使用
     */
    @TableField("risk_type")
    private String riskType;
    /**
     * 公司简介
     */
    @TableField("enterprise_introduce")
    private String enterpriseIntroduce;

    @TableField(exist = false)
    @OneToOne
    @JoinColumn(name = "creator_id", referencedColumnName = "user_id",table = "t_enterprise_user")
    private EnterpriseUser creator;

    @TableField(exist = false)
    @OneToOne
    @JoinColumn(name = "industry_id", referencedColumnName = "id",table = "t_industry")
    private Industry industry;

    @TableField(exist = false)
    @OneToOne
    @JoinColumn(name = "region_county_id", referencedColumnName = "id",table = "t_region")
    private Region regionCounty;

    @TableField(exist = false)
    @OneToOne
    @JoinColumn(name = "region_town_id", referencedColumnName = "id",table = "t_region")
    private Region regionTown;

    @TableField(exist = false)
    private List<String> publishTimeSlot;
}