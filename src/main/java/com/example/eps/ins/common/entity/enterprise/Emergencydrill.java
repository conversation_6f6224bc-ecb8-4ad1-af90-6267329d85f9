package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
@TableName("t_emergency_drill")
public class Emergencydrill {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;
    /**
     * 应急演练名称
     */
    @TableField("emergency_drill_name")
    private String emergencyDrillName;
    /**
     * 摘要
     */
    @TableField("emergency_drill_digest")
    private String emergencyDrillDigest;
    /**
     * 应急演练文件
     */
    @TableField("emergency_drill_file")
    private String emergencyDrillFile;
    /**
     * 应急演练图片
     */
    @TableField("emergency_drill_image")
    private String emergencyDrillImage;
    /**
     * 演练时间
     */
    @TableField("emergency_drill_time")
    private String emergencyDrillTime;
    /**
     * 上传时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;

    @TableField(exist = false)
    private String enterpriseName;

    @TableField(exist = false)
    private List<String> created;
}
