package com.example.eps.ins.common.dto.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 设备分页内容DTO
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class DevicePageContent {
    
    /**
     * 当前页码
     */
    @JsonProperty("pageIndex")
    private Integer pageIndex;
    
    /**
     * 每页大小
     */
    @JsonProperty("pageSize")
    private Integer pageSize;
    
    /**
     * 总记录数
     */
    @JsonProperty("total")
    private Integer total;
    
    /**
     * 设备列表
     */
    @JsonProperty("result")
    private List<DeviceInfo> result;
}
