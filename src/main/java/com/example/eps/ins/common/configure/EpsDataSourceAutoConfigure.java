package com.example.eps.ins.common.configure;

import com.baomidou.mybatisplus.core.parser.ISqlParser;
import com.baomidou.mybatisplus.extension.parsers.BlockAttackSqlParser;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.example.eps.ins.common.inteceptor.DataPermissionInterceptor;
import com.example.eps.ins.common.inteceptor.IDataPermissionHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
public class EpsDataSourceAutoConfigure {
    @Autowired(required = false)
    IDataPermissionHandle dataPermissionHandle;
    /**
     * 注册数据权限
     */
    @Bean
    @Order(-1)
    public DataPermissionInterceptor dataPermissionInterceptor() {
        DataPermissionInterceptor dataPermissionInterceptor = new DataPermissionInterceptor();
        return dataPermissionInterceptor;
    }

//    @Bean
//    public DefaulDataPermissionHandle defaulDataPermissionHandle(){
//        return new DefaulDataPermissionHandle();
//    }

    /**
     * 注册分页插件
     */
    @Bean
    @Order(-2)
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        List<ISqlParser> sqlParserList = new ArrayList<>();
        sqlParserList.add(new BlockAttackSqlParser());
        paginationInterceptor.setSqlParserList(sqlParserList);
        return paginationInterceptor;
    }
}
