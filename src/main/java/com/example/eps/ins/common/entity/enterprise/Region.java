package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 地区表 Entity
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:39
 */
@Data
@TableName("t_region")
public class Region {

    /**
     * 全局唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 地区名
     */
    @TableField("name")
    private String name;

    /**
     * 上级ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 层级
     */
    @TableField("level")
    private Integer level;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modify_user_id")
    private Long modifyUserId;

    /**
     * 修改日期
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 管理局名称
     */
    @TableField("dept")
    private String dept;

    /**
     * 地区简称
     */
    @TableField("abbreviation")
    private String abbreviation;

    /**
     * 上级地区名
     */
    @TableField(exist = false)
    private String parentDeptName;
}