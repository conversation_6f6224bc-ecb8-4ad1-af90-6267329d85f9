package com.example.eps.ins.common.constant;

import java.util.HashMap;
import java.util.Map;

public class EnterpriseConstant {
    /**
     * 未建立隐患排查制度
     */
    public static final int HIDDEN_STATUS = 0;
    /**
     *未是否建立应急预案
     */
    public static final int PLAN_STATUS = 0;
    /**
     * 1-钢铁
     */
    public static final int ENTERPRISE_KEY_STEEL = 1;
    /**
     * 2-铝加工
     */
    public static final int ENTERPRISE_KEY_ALUMINIUM = 2;

    /**
     * 3-粉尘
     */
    public static final int ENTERPRISE_KEY_DUST = 3;
    /**
     * 4-非三类重点企业
     */
    public static final int ENTERPRISE_KEY_OTHER = 4;

    /**
     * 行业-冶金
     */
    public static final int ENTERPRISE_INDUSTRY_METALLURGY = 1;
    /**
     * 行业-有色
     */
    public static final int ENTERPRISE_INDUSTRY_COLORED = 2;

    /**
     * 风险-涉燃爆粉尘
     */
    public static final int ENTERPRISE_RISK_COMBUSTION = 1;

    /**
     * 风险-涉高温融熔
     */
    public static final int ENTERPRISE_RISK_HIGH_TEMPERATURE = 2;

    /**
     * 风险-涉冶金煤气
     */
    public static final int ENTERPRISE_RISK_METALLURGY = 3;

    /**
     * 风险-深井铸造工艺
     */
    public static final int ENTERPRISE_RISK_DEEP_WELL = 106;

    /*EXCEL*/
    /**
     * 企业
     */
    public static final String NATURE_ENTERPRISE = "1";
    /**
     * 个体工商户
     */
    public static final String NATURE_SELF_EMPLOYED_PERSON = "2";
    /**
     * 100人以上
     */
    public static final String PEOPLE_NUMBER_MORE_THAN_HUNDRED = "1";
    /**
     * 0-30人
     */
    public static final String NPEOPLE_NUMBER_LESS_THAN_HUNDRED = "2";
    /**
     * 30-50人
     */
    public static final String NPEOPLE_NUMBER_THREE_FIVE= "3";
    /**
     * :50-100人
     */
    public static final String NPEOPLE_NUMBER_FIVE_ONE = "4";

    /**
     * 正常营业
     */
    public static final String PRODUCTION_STATUS_NORMAL_BUSINESS = "1";
    /**
     * 放假
     */
    public static final String PRODUCTION_STATUS_HOLIDAY = "2";
    /**
     * 停产整改
     */
    public static final String PRODUCTION_STATUS_STOP_PRODUCTION = "3";
    /**
     * 注销
     */
    public static final String PRODUCTION_STATUS_LOGOUT = "4";
    /**
     * 搬迁
     */
    public static final String PRODUCTION_STATUS_RELOCATION = "5";

    /**
     * 已评审
     */
    public static final String EMERGENCY_PLAN_REVIEW = "1";
    /**
     * 未评审
     */
    public static final String EMERGENCY_PLAN_REVIEW_NO = "2";
    /**
     * 已备案
     */
    public static final String EMERGENCY_PLAN_RECORD = "1";
    /**
     * 未备案
     */
    public static final String EMERGENCY_PLAN_RECORD_NO = "2";

    /**
     * 工商保险
     */
    public static final int SAFETY_INSURANCE = 1;
    /**
     * 安全责任保险
     */
    public static final int SAFETY_INSURANCE_LIABILITY = 2;

    /**
     * 批发
     */
    public static final Integer WHOSLESALE = 1;
    /**
     * 仓储
     */
    public static final Integer KEEP_GRAIN = 2;
    /**
     * 零售
     */
    public static final Integer RETAIL= 3;
    /**
     * 住宿
     */
    public static final Integer STAY = 4;
    /**
     * 餐饮
     */
    public static final Integer RESTAURANT = 5;

    public static Map<String, String> NATURES = new HashMap<>();

    public static Map<String, String> PEOPLE_NUMBERS = new HashMap<>();

    public static Map<String, String> PRODUCTION_STATUS = new HashMap<>();

    public static Map<String, String> EMERGENCYPLAN_REVIEW = new HashMap<>();

    public static Map<String, String> EMERGENCYPLAN_RECORD = new HashMap<>();

    /*EXCEL END*/

    public static final String RISK_CRUX_I = "i";
    /**
     * 保存后续会用来查询的风险
     */
    public static Map<String, String> RISK_ENTERPRISE_IDS = new HashMap<>();

    static {
        RISK_ENTERPRISE_IDS.put(RISK_CRUX_I + "1", RISK_CRUX_I + "7");
        RISK_ENTERPRISE_IDS.put(RISK_CRUX_I + "2", RISK_CRUX_I + "93");
        RISK_ENTERPRISE_IDS.put(RISK_CRUX_I + "3", RISK_CRUX_I + "159");
        RISK_ENTERPRISE_IDS.put(RISK_CRUX_I + "4", RISK_CRUX_I + "178");
        RISK_ENTERPRISE_IDS.put(RISK_CRUX_I + "5", RISK_CRUX_I + "221");
        RISK_ENTERPRISE_IDS.put(RISK_CRUX_I + "6", RISK_CRUX_I + "313");

        //EXCEL
        NATURES.put("企业", NATURE_ENTERPRISE);
        NATURES.put("个体工商户", NATURE_SELF_EMPLOYED_PERSON);
        PEOPLE_NUMBERS.put("100人以上", PEOPLE_NUMBER_MORE_THAN_HUNDRED);
        PEOPLE_NUMBERS.put("30人以下", NPEOPLE_NUMBER_THREE_FIVE);
        PEOPLE_NUMBERS.put("0-30人", NPEOPLE_NUMBER_LESS_THAN_HUNDRED);
        PEOPLE_NUMBERS.put("30-50人", NPEOPLE_NUMBER_FIVE_ONE);
        PRODUCTION_STATUS.put("正常营业",PRODUCTION_STATUS_NORMAL_BUSINESS);
        PRODUCTION_STATUS.put("放假",PRODUCTION_STATUS_HOLIDAY);
        PRODUCTION_STATUS.put("停产整改",PRODUCTION_STATUS_STOP_PRODUCTION);
        PRODUCTION_STATUS.put("注销",PRODUCTION_STATUS_LOGOUT);
        PRODUCTION_STATUS.put("搬迁",PRODUCTION_STATUS_RELOCATION);
        EMERGENCYPLAN_REVIEW.put("是",EMERGENCY_PLAN_REVIEW);
        EMERGENCYPLAN_REVIEW.put("否",EMERGENCY_PLAN_REVIEW_NO);
        EMERGENCYPLAN_RECORD.put("是",EMERGENCY_PLAN_RECORD);
        EMERGENCYPLAN_RECORD.put("否",EMERGENCY_PLAN_RECORD_NO);
    }

    public static Map<String, String> RISK_QUERY_ENTERPRISE_IDS = new HashMap<>();

    static {
        RISK_QUERY_ENTERPRISE_IDS.put(RISK_CRUX_I + "2", RISK_CRUX_I + ENTERPRISE_RISK_DEEP_WELL);
    }


}
