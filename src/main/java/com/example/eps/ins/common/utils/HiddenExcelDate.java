package com.example.eps.ins.common.utils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/11 15:30
 * @Version: 1.0
 */
@Getter
@Setter
@HeadRowHeight(40)
@ContentRowHeight(30)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND)
@EqualsAndHashCode
public class HiddenExcelDate {

    @ColumnWidth(30)
    @ExcelProperty({"企业名称"})
    private String name;

    @ColumnWidth(20)
    @ExcelProperty({"统一社会信用代码"})
    private String socialCode;

    @ColumnWidth(13)
    @ExcelProperty({"登录手机号"})
    private String phone;

    @ColumnWidth(13)
    @ExcelProperty({"所属区县"})
    private String county;

    @ColumnWidth(13)
    @ExcelProperty({"所属镇街"})
    private String town;

    @ColumnWidth(13)
    @ExcelProperty({"行业类别"})
    private String industryName;
}
