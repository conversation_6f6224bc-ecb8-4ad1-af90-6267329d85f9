package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
@TableName("t_present_situation")
public class PresentSituation {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;
    /**
     * 0-安全三同时，1-现状评价
     */
    @TableField("present_type")
    private Integer presentType;
    /**
     * 安全三同时类别
     * 1.安全生产条件和设施综合分析报告/安全预评价报告；
     * 2.安全设施设计报告；
     * 3.安全设施竣工验收报告。
     */
    @TableField("present_model")
    private String presentModel;
    /**
     * 图片
     */
    @TableField("present_image")
    private String presentImage;
    /**
     * 附件
     */
    @TableField("present_file")
    private String presentFile;
    /**
     * 提交时间
     */
    @TableField("present_time")
    private String presentTime;
    /**
     * 上传时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;

    @TableField(exist = false)
    private String enterpriseName;

    @TableField(exist = false)
    private List<String> created;
}
