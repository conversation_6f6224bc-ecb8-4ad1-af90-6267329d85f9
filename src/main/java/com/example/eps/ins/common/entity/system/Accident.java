package com.example.eps.ins.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.example.eps.ins.common.converter.StringTrimConverter;
import com.example.eps.ins.common.converter.StringTrimIntensifyConverter;
import com.wuwenze.poi.annotation.Excel;
import com.wuwenze.poi.annotation.ExcelField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 事故统计表 Entity
 *
 * <AUTHOR>
 * @date 2021-10-27 14:28:15
 */
@Data
@TableName("t_accident")
@Excel("事故统计导入导出")
public class Accident {
    /**
     * 事故id
     */
    @TableId(value = "ACCIDENT_ID", type = IdType.AUTO)
    private Integer accidentId;

    /**
     * 报送状态
     */
    @TableField("REPORT_STATUS")
    @ExcelField(value = "报送状态", readConverter = StringTrimConverter.class)
    private String reportStatus;

    /**
     * 报送类别
     */
    @TableField("REPORT_TYPE")
    @ExcelField(value = "报送类别", readConverter = StringTrimConverter.class)
    private String reportType;

    /**
     * 数据状态
     */
    @TableField("DATA_STATUS")
    @ExcelField(value = "数据状态", readConverter = StringTrimConverter.class)
    private String dataStatus;

    /**
     * 事故发生时间
     */
    @TableField("ACCIDENT_TIME")
    @ExcelField(value = "事故发生时间", dateFormat = "yyyy年MM月dd日  HH时mm分")
    private Date accidentTime;

    /**
     * 事故发生地点
     */
    @TableField("ACCIDENT_PLACE")
    @ExcelField(value = "事故发生地点", readConverter = StringTrimConverter.class)
    private String accidentPlace;

    /**
     * 事故发生单位
     */
    @TableField("ACCIDENT_UNIT")
    @ExcelField(value = "事故发生单位", readConverter = StringTrimConverter.class)
    private String accidentUnit;

    /**
     * 事故等级
     */
    @TableField("ACCIDENT_LEVEL")
    @ExcelField(value = "事故等级", readConverter = StringTrimConverter.class)
    private String accidentLevel;

    /**
     * 事故类型
     */
    @TableField("ACCIDENT_TYPE")
    @ExcelField(value = "事故类型", readConverter = StringTrimConverter.class)
    private String accidentType;

    /**
     * 报送单位
     */
    @TableField("PUSH_UNIT")
    @ExcelField(value = "报送单位", readConverter = StringTrimIntensifyConverter.class)
    private String pushUnit;

    /**
     * 事故编码
     */
    @TableId(value = "ACCIDENT_CODE")
    @ExcelField(value = "事故编码", readConverter = StringTrimConverter.class)
    private String accidentCode;

    /**
     * 死亡人数合计
     */
    @TableField("TOTAL_DEATH_NUM")
    @ExcelField(value = "死亡人数合计")
    private Integer totalDeathNum;

    /**
     * 死亡人数
     */
    @TableField("DEATH_NUM")
    @ExcelField(value = "死亡人数")
    private Integer deathNum;

    /**
     * 受伤人数
     */
    @TableField("INJURED_NUM")
    @ExcelField(value = "受伤人数")
    private Integer injuredNum;

    /**
     * 其中重伤人数
     */
    @TableField("SERIOUSLY_INJURED_NUM")
    @ExcelField(value = "其中重伤人数")
    private Integer seriouslyInjuredNum;

    /**
     * 下落不明人数
     */
    @TableField("MISSING_NUM")
    @ExcelField(value = "下落不明人数")
    private Integer missingNum;

    /**
     * 管理分类
     */
    @TableField("MANAGE_TYPE")
    @ExcelField(value = "管理分类", readConverter = StringTrimConverter.class)
    private String manageType;

    /**
     * 事故标识
     */
    @TableField("ACCIDENT_LOGO")
    @ExcelField(value = "事故标识", readConverter = StringTrimConverter.class)
    private String accidentLogo;

    /**
     * 事故发生省
     */
    @TableField("ACCIDENT_PROVINCE")
    @ExcelField(value = "事故发生省", readConverter = StringTrimConverter.class)
    private String accidentProvince;

    /**
     * 事故发生市
     */
    @TableField("ACCIDENT_CITY")
    @ExcelField(value = "事故发生市", readConverter = StringTrimConverter.class)
    private String accidentCity;

    /**
     * 事故发生县
     */
    @TableField("ACCIDENT_COUNTY")
    @ExcelField(value = "事故发生县", readConverter = StringTrimConverter.class)
    private String accidentCounty;

    /**
     * 报送单位省
     */
    @TableField("PUSH_PROVINCE")
    @ExcelField(value = "报送单位省", readConverter = StringTrimConverter.class)
    private String pushProvince;

    /**
     * 报送单位市
     */
    @TableField("PUSH_CITY")
    @ExcelField(value = "报送单位市", readConverter = StringTrimConverter.class)
    private String pushCity;

    /**
     * 报送单位县
     */
    @TableField("PUSH_COUNTY")
    @ExcelField(value = "报送单位县", readConverter = StringTrimConverter.class)
    private String pushCounty;

    /**
     * 事故概况
     */
    @TableField("ACCIDENT_GENERAL")
    @ExcelField(value = "事故概况", readConverter = StringTrimConverter.class)
    private String accidentGeneral;

    /**
     * 门类
     */
    @TableField("CATEGORY")
    @ExcelField(value = "门类", readConverter = StringTrimConverter.class)
    private String category;

    /**
     * 大类
     */
    @TableField("BIG_CATEGORY")
    @ExcelField(value = "大类", readConverter = StringTrimConverter.class)
    private String bigCategory;

    /**
     * 中类
     */
    @TableField("MIDDLE_CATEGORY")
    @ExcelField(value = "中类", readConverter = StringTrimConverter.class)
    private String middleCategory;

    /**
     * 小类
     */
    @TableField("SMALL_CATEGORY")
    @ExcelField(value = "小类", readConverter = StringTrimConverter.class)
    private String smallCategory;

    /**
     * 单位性质
     */
    @TableField("UNIT_TYPE")
    @ExcelField(value = "单位性质", readConverter = StringTrimConverter.class)
    private String unitType;

    /**
     * 直接经济损失(万元)
     */
    @TableField("DIRECT_LOSS")
    @ExcelField(value = "直接经济损失(万元)")
    private Double directLoss;

    /**
     * 统一社会信用代码
     */
    @TableField("UNIFY_SOCIAL_CREDIT_CODE")
    @ExcelField(value = "统一社会信用代码", readConverter = StringTrimConverter.class)
    private String unifySocialCreditCode;

    /**
     * 登记注册类型
     */
    @TableField("REGISTER_TYPE")
    @ExcelField(value = "登记注册类型", readConverter = StringTrimConverter.class)
    private String registerType;

    /**
     * 起因物
     */
    @TableField("CAUSE")
    @ExcelField(value = "CAUSE", readConverter = StringTrimConverter.class)
    private String cause;

    /**
     * 致害物
     */
    @TableField("DAMAGE")
    @ExcelField(value = "致害物", readConverter = StringTrimConverter.class)
    private String damage;

    /**
     * 事故原因
     */
    @TableField("ACCIDENT_REASON")
    @ExcelField(value = "事故原因", readConverter = StringTrimConverter.class)
    private String accidentReason;

    /**
     * 火灾
     */
    @TableField("FIRE")
    @ExcelField(value = "火灾", readConverter = StringTrimConverter.class)
    private String fire;

    /**
     * 特种设备
     */
    @TableField("SPECIAL_EQUIPMENT")
    @ExcelField(value = "特种设备", readConverter = StringTrimConverter.class)
    private String specialEquipment;

    /**
     * 危险化学品
     */
    @TableField("HAZARDOUS_CHEMICALS")
    @ExcelField(value = "危险化学品", readConverter = StringTrimConverter.class)
    private String hazardousChemicals;

    /**
     * 民用爆炸物品
     */
    @TableField("CIVILIAN_EXPLOSIVES")
    @ExcelField(value = "民用爆炸物品", readConverter = StringTrimConverter.class)
    private String civilianExplosives;

    /**
     * 有限空间
     */
    @TableField("FINITE_SPACE")
    @ExcelField(value = "有限空间", readConverter = StringTrimConverter.class)
    private String finiteSpace;

    /**
     * 不安全行为
     */
    @TableField("UNSAFE_BEHAVIOR")
    @ExcelField(value = "不安全行为", readConverter = StringTrimConverter.class)
    private String unsafeBehavior;

    /**
     * 不安全状态
     */
    @TableField("UNSAFE_STATE")
    @ExcelField(value = "不安全状态", readConverter = StringTrimConverter.class)
    private String unsafeState;

    /**
     * 是否为举报查实事故
     */
    @TableField("ACCIDENT_VERIFY")
    @ExcelField(value = "是否为举报查实事故", readConverter = StringTrimConverter.class)
    private String accidentVerify;

    /**
     * 事故发生单位详细情况
     */
    @TableField("ACCIDENT_DETAIL")
    @ExcelField(value = "事故发生单位详细情况", readConverter = StringTrimConverter.class)
    private String accidentDetail;

    /**
     * 查处情况
     */
    @TableField("INVESTIGATION_SITUATION")
    @ExcelField(value = "查处情况", readConverter = StringTrimConverter.class)
    private String investigationSituation;

    /**
     * 煤矿事故类别
     */
    @TableField("COAL_MINE_ACCIDENT_TYPE")
    @ExcelField(value = "煤矿事故类别", readConverter = StringTrimConverter.class)
    private String coalMineAccidentType;

    /**
     * 煤矿类别
     */
    @TableField("COAL_MINE_TYPE")
    @ExcelField(value = "煤矿类别", readConverter = StringTrimConverter.class)
    private String coalMineType;

    /**
     * 煤矿规模
     */
    @TableField("COAL_MINE_SIZE")
    @ExcelField(value = "煤矿规模", readConverter = StringTrimConverter.class)
    private String coalMineSize;

    /**
     * 持有证件
     */
    @TableField("HOLD_CERTIFICATES")
    @ExcelField(value = "持有证件", readConverter = StringTrimConverter.class)
    private String holdCertificates;

    /**
     * 违法行为
     */
    @TableField("ILLEGAL_ACT")
    @ExcelField(value = "违法行为", readConverter = StringTrimConverter.class)
    private String illegalAct;

    /**
     * 车辆性质
     */
    @TableField("VEHICLE_NATURE")
    @ExcelField(value = "车辆性质", readConverter = StringTrimConverter.class)
    private String vehicleNature;

    /**
     * 道路运输事故类别
     */
    @TableField("ROAD_TRAFFIC_ACCIDENTS_TYPE")
    @ExcelField(value = "道路运输事故类别", readConverter = StringTrimConverter.class)
    private String roadTrafficAccidentsType;

    /**
     * 建筑施工类型
     */
    @TableField("CONSTRUCTION_TYPE")
    @ExcelField(value = "建筑施工类型", readConverter = StringTrimConverter.class)
    private String constructionType;

    /**
     * 是否为央企
     */
    @TableField("CENTRAL_ENTERPRISE")
    @ExcelField(value = "是否为央企", readConverter = StringTrimConverter.class)
    private String centralEnterprise;

    /**
     * 建筑单位
     */
    @TableField("BUILDING_UNIT")
    @ExcelField(value = "建筑单位", readConverter = StringTrimConverter.class)
    private String buildingUnit;

    /**
     * 危险化学品事故环节
     */
    @TableField("HAZARDOUS_CHEMICAL_ACCIDENT_LINK")
    @ExcelField(value = "危险化学品事故环节", readConverter = StringTrimConverter.class)
    private String hazardousChemicalAccidentLink;

    /**
     * 烟花爆竹事故环节
     */
    @TableField("FIREWORKS_ACCIDENT_SECTION")
    @ExcelField(value = "烟花爆竹事故环节", readConverter = StringTrimConverter.class)
    private String fireworksAccidentSection;

    /**
     * 烟花爆竹类别
     */
    @TableField("FIREWORKS_CATEGORY")
    @ExcelField(value = "烟花爆竹类别", readConverter = StringTrimConverter.class)
    private String fireworksCategory;

    /**
     * 是否为央企事故
     */
    @TableField("CENTRAL_ENTERPRISE_ACCIDENT")
    @ExcelField(value = "是否为央企事故", readConverter = StringTrimConverter.class)
    private String centralEnterpriseAccident;

    /**
     * 调查报告
     */
    @TableField("INVESTIGATION_REPORT")
    @ExcelField(value = "调查报告", readConverter = StringTrimConverter.class)
    private String investigationReport;

    /**
     * 渔业船舶事故类型
     */
    @TableField("FISHERY_MARINE_ACCIDENT_TYPE")
    @ExcelField(value = "渔业船舶事故类型", readConverter = StringTrimConverter.class)
    private String fisheryMarineAccidentType;

    /**
     * 水上运输事故类型
     */
    @TableField("WATER_TRANSPORT_ACCIDENT_TYPE")
    @ExcelField(value = "水上运输事故类型", readConverter = StringTrimConverter.class)
    private String waterTransportAccidentType;

    /**
     * 船舶溢油
     */
    @TableField("SHIP_OIL_SPILL")
    @ExcelField(value = "船舶溢油", readConverter = StringTrimConverter.class)
    private String shipOilSpill;

    /**
     * 列车类型
     */
    @TableField("TRAIN_TYPE")
    @ExcelField(value = "列车类型", readConverter = StringTrimConverter.class)
    private String trainType;

    /**
     * 脱轨数量
     */
    @TableField("DERAILMENT_NUM")
    @ExcelField(value = "脱轨数量", readConverter = StringTrimConverter.class)
    private Integer derailmentNum;

    /**
     * 铁路类型
     */
    @TableField("RAILWAY_TYPE")
    @ExcelField(value = "铁路类型", readConverter = StringTrimConverter.class)
    private String railwayType;

    /**
     * 中断铁路行车时间
     */
    @TableField("INTERRUPT_RAILWAY_TRAFFIC_TIME")
    @ExcelField(value = "中断铁路行车时间", readConverter = StringTrimConverter.class)
    private String interruptRailwayTrafficTime;

    /**
     * 建筑门类
     */
    @TableField("BUILDING_CATEGORY")
    @ExcelField(value = "建筑门类", readConverter = StringTrimConverter.class)
    private String buildingCategory;

    /**
     * 建筑大类
     */
    @TableField("BUILDING_BIG_CATEGORY")
    @ExcelField(value = "建筑大类", readConverter = StringTrimConverter.class)
    private String buildingBigCategory;

    /**
     * 建筑中类
     */
    @TableField("BUILDING_MIDDLE_CATEGORY")
    @ExcelField(value = "建筑中类", readConverter = StringTrimConverter.class)
    private String buildingMiddleCategory;

    /**
     * 建筑小类
     */
    @TableField("BUILDING_SMALL_CATEGORY")
    @ExcelField(value = "建筑小类", readConverter = StringTrimConverter.class)
    private String buildingSmallCategory;

    /**
     * 建筑施工事故类型1
     */
    @TableField("CONSTRUCTION_ACCIDENT_TYPE_ONE")
    @ExcelField(value = "建筑施工事故类型1", readConverter = StringTrimConverter.class)
    private String constructionAccidentTypeOne;

    /**
     * 建筑施工事故类型2
     */
    @TableField("CONSTRUCTION_ACCIDENT_TYPE_TWO")
    @ExcelField(value = "建筑施工事故类型2", readConverter = StringTrimConverter.class)
    private String constructionAccidentTypeTwo;

    /**
     * 初次入库日期
     */
    @TableField("FIRST_ENTRY_DATE")
    @ExcelField(value = "初次入库日期", dateFormat = "yyyy-MM-dd")
    private Date firstEntryString;

    /**
     * 初次上报日期
     */
    @TableField("FIRST_REPORT_DATE")
    @ExcelField(value = "初次上报日期", dateFormat = "yyyy-MM-dd")
    private Date firstReportString;

    /**
     * 伤亡人员详细信息
     */
    @TableField("CASUALTY_DETAILS")
    @ExcelField(value = "伤亡人员详细信息", readConverter = StringTrimConverter.class)
    private String casualtyDetails;

    /**
     * 报送单位地区ID
     */
    @TableField(value = "REGION_ID")
    private Long regionId;

    @TableField(exist = false)
    private List<String> accidentCodeArray;

    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date accidentTimeFrom;

    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date accidentTimeTo;


}