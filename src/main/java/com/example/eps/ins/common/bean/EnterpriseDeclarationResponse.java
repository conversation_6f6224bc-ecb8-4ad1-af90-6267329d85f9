package com.example.eps.ins.common.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.example.eps.ins.common.entity.enterprise.EnterpriseUser;
import com.example.eps.ins.common.entity.enterprise.Industry;
import com.example.eps.ins.common.entity.enterprise.Region;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class EnterpriseDeclarationResponse {

    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 行业ID
     */
    @NotNull(message = "所属行业不能为空")
    private Long industryId;
    /**
     * 证书编号
     */
    private String certificateCode;
    /**
     * 地区-区县ID
     */
    @NotNull(message = "所属地区区县不能为空")
    private Long regionCountyId;

    /**
     * 地区-镇街ID
     */
    private Long regionTownId;

    /**
     * 企业性质
     */
    @NotNull(message = "企业性质不能为空")
    private String nature;

    /**
     * 从业人数
     */
    @NotNull(message = "从业人数不能为空")
    private String peopleNumber;

    /**
     * 生产经营状态
     */
    @NotNull(message = "经营状态不能为空")
    private String productionStatus;

    /**
     * 安全三同时或现状评价
     */
    private String currentEvaluation;
    /**
     * 安全生产标准化 -0：有；1无
     */
    @TableField("safety_bck")
    private Integer safetyBck;

    /**
     * 安全生产标准化情况对标达标等级
     */
    private String safetyBckLevel;

    /**
     * 安全生产标准化情况对标达标时间
     */
    private String safetyBckTime;

    /**
     * 安全生产标准化情况专业达标时间
     */
    private String safetyMajorTime;

    /**
     * 安全生产标准化情况岗位达标时间
     */
    private String safetyPostTime;

    /**
     * 应急预案
     */
    private String emergencyPlanReview;

    /**
     * 应急预案
     */
    private String emergencyPlanRecord;

    /**
     * 安全保险
     */
    private List<Integer> safetyInsurance;

    /**
     * 企业名称
     */
    @NotNull(message = "企业名称不能为空")
    private String name;

    /**
     * 企业地址
     */
    @NotNull(message = "企业地址不能为空")
    private String address;

    /**
     * 社信代码
     */
    @NotNull(message = "统一社信代码不能为空")
    private String socialCode;

    /**
     * 安全负责人姓名
     */
    private String safetyDirectorName;

    /**
     * 安全负责人电话
     */
    private String safetyDirectorPhone;

    /**
     * 专职安全员人数
     */
    private Integer safetyFullNumber;

    /**
     * 兼职安全员人数
     */
    private Integer safetyPartNumber;

    /**
     * 企业联系人姓名
     */
    private String contactName;

    /**
     * 企业联系人电话
     */
    private String contactPhone;

    /**
     * 法定代表人姓名
     */
    private String legalRepresentativeName;

    /**
     * 法定代表人职务
     */
    private String legalRepresentativeJob;

    /**
     * 法定代表人联系电话
     */
    private String legalRepresentativePhone;

    /**
     * 初审人
     */
    private Long firstReviewerId;

    /**
     * 初审时间
     */
    private Date firstReviewTime;

    /**
     * 初审状态.1-通过;2-拒绝
     */
    private Integer firstReviewStatus;

    /**
     * 初审意见
     */
    private String firstReviewRemark;

    /**
     * 复审人
     */
    private Long secondReviewerId;

    /**
     * 复审时间
     */
    private Date secondReviewTime;

    /**
     * 复审状态.;1-通过;2-拒绝
     */
    private Integer secondReviewStatus;

    /**
     * 复审意见
     */
    private String secondReviewRemark;

    /**
     * 企业申报状态.
     */
    private Integer status;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Long modifyUserId;

    /**
     * 修改日期
     */
    private Date modifyTime;

    /**
     * 风险项
     */
    private String risks;

    private EnterpriseUser creator;

    private Industry industry;

    private Region regionCounty;

    private Region regionTown;
    /**
     * 规上规下企业：0-规下；1-规上
     */
    private Integer scaleStatus;

    /**
     * 安全三同时：0-无；1-有
     */
    private Integer currentEvaluationStatus;
    /**
     * 两单两卡创建情况：0-未创建；1-创建中；2-已创建
     */
    private Integer orderAndCard;
    /**
     * 0-无；1-批发  2-仓储   零售   住宿   餐饮
     */
    private List<Integer> tradeType;
    /**
     * 公司简介
     */
    private String enterpriseIntroduce;
}
