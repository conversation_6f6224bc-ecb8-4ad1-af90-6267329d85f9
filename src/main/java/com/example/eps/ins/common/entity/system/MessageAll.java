package com.example.eps.ins.common.entity.system;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("t_message_all")
public class MessageAll {
  /**
   * 序列化时候使用
   */
  private static final long serialVersionUID = 6516800943817041235L;
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  /**
   *内容
   */
  @TableField("content_text")
  private String contentText;
  /**
   *行业id
   */
  @TableField("industry_id")
  private String industryId;
  /**
   *创建人
   */
  @TableField("create_id")
  private Long createId;
  @TableField(exist = false)
  private String createName;
  /**
   *创建时间
   */
  @TableField("create_time")
  private Date createTime;
  /**
   *行业id
   */
  @TableField(exist = false)
  private List<String> industryName;
  @TableField(exist = false)
  private Integer status;
}
