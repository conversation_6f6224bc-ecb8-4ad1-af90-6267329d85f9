package com.example.eps.ins.common.dto.riskPush;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class LocationInfo {
    @JsonProperty("LeftTopX") private Integer leftTopX;
    @JsonProperty("LeftTopY") private Integer leftTopY;
    @JsonProperty("RightBtmX") private Integer rightBtmX;
    @JsonProperty("RightBtmY") private Integer rightBtmY;
    // 其他字段...
}
