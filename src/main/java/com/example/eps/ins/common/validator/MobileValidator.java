package com.example.eps.ins.common.validator;

import com.example.eps.ins.common.annotation.IsMobile;
import com.example.eps.ins.common.constant.RegexpConstant;
import com.example.eps.ins.common.utils.EpsUtil;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 校验是否为合法的手机号码
 *
 * <AUTHOR>
 */
public class MobileValidator implements ConstraintValidator<IsMobile, String> {

    @Override
    public void initialize(IsMobile isMobile) {
    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        try {
            if (StringUtils.isBlank(s)) {
                return true;
            } else {
                String regex = RegexpConstant.MOBILE;
                return EpsUtil.match(regex, s);
            }
        } catch (Exception e) {
            return false;
        }
    }
}
