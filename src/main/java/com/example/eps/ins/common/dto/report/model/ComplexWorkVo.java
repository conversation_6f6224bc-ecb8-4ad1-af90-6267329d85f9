package com.example.eps.ins.common.dto.report.model;


import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ComplexWorkVo extends QueryRequest {

  /**
   * 全局唯一标识符
   */
  private Long id;

  /**
   * 复工复产类型：1-机器设备排查;2-集中培训;3-技改
   */
  private String complexType;

  /**
   * 复工复产图片
   */
  private String complexImage;
  /**
   * 复工复产文件
   */
  private String complexFile;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 地区id
   */
  private Long regionId;

  /**
   * 创建人
   */
  private Long creatorId;

  private String enterpriseName;

  private List<String> created;
}
