package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
@TableName("t_hidden_danger_list")
public class HiddenDangerList {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /**
     * 隐患检查周期（0 日 1 周  2 月）
     */
    @TableField("hidden_danger_cycle")
    private Integer hiddenDangerCycle;
    /**
     * 隐患内容
     */
    @TableField("hidden_danger_content")
    private String hiddenDangerContent;
    /**
     * 隐患检查日期
     */
    @TableField("hidden_danger_inspect")
    private String hiddenDangerInspect;
    /**
     * 隐患检查日期日排查结束时间
     */
    @TableField("hidden_danger_end")
    private String hiddenDangerEnd;
    /**
     * 隐患部门
     */
    @TableField("hidden_danger_dept")
    private String hiddenDangerDept;
    /**
     * 隐患整改要求
     */
    @TableField("hidden_danger_revise")
    private String hiddenDangerRevise;
    /**
     * 隐患整改时间
     */
    @TableField("hidden_danger_revise_time")
    private String hiddenDangerReviseTime;
    /**
     * 隐患图片
     */
    @TableField("hidden_danger_image")
    private String hiddenDangerImage;

    /**
     * 隐患图片
     */
    @TableField("hidden_danger_file")
    private String hiddenDangerFile;

    /**
     * 新建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 隐患是否处理（0 未处理  1 处理）
     */
    @TableField("hidden_danger_status")
    private Integer hiddenDangerStatus;
    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;
    /**
     * 开始时间
     */
    @TableField(exist = false)
    private List<String> startDate;
    /**
     * 是否隐患（0：否  1是）
     */
    @TableField("hidden_status")
    private Integer hiddenStatus;
    /**
     * 是否能够修改有无隐患（0 不能  1 能）
      */
    @TableField(exist = false)
    private Integer updateStatus;

    @TableField(exist = false)
    private String enterpriseName;

    @TableField(exist = false)
    private List<String> created;
}
