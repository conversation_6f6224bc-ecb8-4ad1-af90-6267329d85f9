package com.example.eps.ins.common.entity;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
public class EpsResponse extends HashMap<String, Object> {

    private static final long serialVersionUID = -8713837118340960775L;

    public EpsResponse message(String message) {
        this.put("message", message);
        return this;
    }

    public EpsResponse data(Object data) {
        this.put("data", data);
        return this;
    }

    @Override
    public EpsResponse put(String key, Object value) {
        super.put(key, value);
        return this;
    }

    public String getMessage() {
        return String.valueOf(get("message"));
    }

    public Object getData() {
        return get("data");
    }
}
