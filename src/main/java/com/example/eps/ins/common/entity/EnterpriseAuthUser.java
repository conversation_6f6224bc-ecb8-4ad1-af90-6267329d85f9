package com.example.eps.ins.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

@Data
@SuppressWarnings("all")
@EqualsAndHashCode(callSuper = true)
public class EnterpriseAuthUser extends User {
    private Long userId;
    private String mobile;
    public EnterpriseAuthUser(String username, String password, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, authorities);
    }
}
