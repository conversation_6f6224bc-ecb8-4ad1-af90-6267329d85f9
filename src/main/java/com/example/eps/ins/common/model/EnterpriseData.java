package com.example.eps.ins.common.model;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;

import com.wuwenze.poi.annotation.Excel;
import com.wuwenze.poi.annotation.ExcelField;
import lombok.Data;

import java.io.Serializable;


@Data
@Excel("单位数据")
public class EnterpriseData  implements Serializable {
    private static final long serialVersionUID = 921991157363932095L;
    /**
     * 企业名称
     */
    @ExcelField("企业名称")
    private String name;

    /**
     * 社信代码
     */
    @ExcelField("社信代码")
    private String socialCode;

    /**
     * 行业类别
     */
    @ExcelField("行业类别")
    private String industry;

    /**
     * 企业性质
     */
    @ExcelField("单位性质")
    private String nature;

    /**
     * 所属地区
     */
    @ExcelField("所属区县")
    private String regionCounty;

    /**
     * 所属镇街
     */
    @ExcelField("所属镇街")
    private String regionTown;

    /**
     * 从业人数
     */
    @ExcelField("从业人数")
    private String peopleNumber;
    /**
     * 法定代表人姓名
     */
    @ExcelField("法人代表/主要负责人")
    private String legalRepresentativeName;
    /**
     * 法定代表人联系电话
     */
    @ExcelField("法人电话")
    private String legalRepresentativePhone;

    /**
     * 安全负责人姓名
     */
    @ExcelField("安全负责人")
    private String safetyDirectorName;

    /**
     * 安全负责人电话
     */
    @ExcelField("安全负责人电话")
    private String safetyDirectorPhone;

    /**
     * 专职安全员人数
     */
    @ExcelField("专职安全员人数")
    private Integer safetyFullNumber;

    /**
     * 兼职安全员人数
     */
    @ExcelField("兼职安全员人数")
    private Integer safetyPartNumber;

    /**
     * 企业地址
     */
    @ExcelField("注册地址")
    private String address;

    /**
     * 生产经营状态
     */
    @ExcelField("生产经营状态")
    private String productionStatus;
    /**
     * 安全生产标准化是否达标
     */
    @ExcelField("安全生产标准化是否达标")
    private String safetyBck;
    /**
     * 安全生产标准化情况对标达标等级
     */
    @ExcelField("对标达标等级")
    private String safetyBckLevel;
    /**
     * 安全生产标准化情况对标达标时间
     */
    @ExcelField("对标达标时间")
    private String safetyBckTime;
    /**
     * 安全生产标准化情况对标达标时间
     */
    @ExcelField("证书编号")
    private String certificateCode;
    /**
     * 安全生产标准化情况专业达标时间
     */
    @ExcelField("专业达标时间")
    private String safetyMajorTime;

    /**
     * 安全生产标准化情况岗位达标时间
     */
    @ExcelField("岗位达标时间")
    private String safetyPostTime;
    /**
     * 应急预案评审
     */
    @ExcelField("应急预案是否具备")
    private String emergencyPlan;
    /**
     * 应急预案评审
     */
    @ExcelField("应急预案是否评审")
    private String emergencyPlanReview;
    /**
     * 应急预案备案
     */
    @ExcelField("应急预案是否备案")
    private String emergencyPlanRecord;
    /**
     * 规上规下企业：0-规下；1-规上
     */
    @ExcelField("规上规下企业")
    private String scaleStatus;
    /**
     * 安全保险1-工商保险;2-安全责任保险
     */
    @ExcelField("安全保险")
    private String safetyInsurance;

}
