package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_accident_census")
public class TRptAccidentCensus {
  /**
   *唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;
  /**
   *事故总数
   */
  @TableField("accident_num")
  private int accidentNum;
  /**
   *直接经济损失
   */
  @TableField("loss")
  private BigDecimal loss;
  /**
   *受伤人数
   */
  @TableField("injured")
  private int injured;
  /**
   *死亡人数
   */
  @TableField("death")
  private int death;
  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;
  /**
   *地区名
   */
  @TableField("name")
  private String name;
  /**
   *年份
   */
  @TableField("year")
  private int year;
}
