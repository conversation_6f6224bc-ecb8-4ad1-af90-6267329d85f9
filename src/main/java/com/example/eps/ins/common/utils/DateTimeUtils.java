package com.example.eps.ins.common.utils;

import com.example.eps.ins.common.constant.NumberConstant;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @author: zhanghongyin
 * @date: Created in 2021/11/19 14:16
 * @Description:
 * @Version: 1.0
 */
public class DateTimeUtils {
    /**
     * 获取当前年份
     * @return 当前年份的String类型
     */
    public static String getYear(){
        return new SimpleDateFormat("yyyy").format(new Date());
    }

    /**
     * 获取开始时间
     * @return 开始时间的String类型
     */
    public static String getStartTime(Integer year){
        return year.toString()+ NumberConstant.START_TIME;
    }

    /**
     * 获取结束时间
     * @return 结束时间的String类型
     */
    public static String getEndTime(Integer year){
        return year.toString()+NumberConstant.END_TIME;
    }
}
