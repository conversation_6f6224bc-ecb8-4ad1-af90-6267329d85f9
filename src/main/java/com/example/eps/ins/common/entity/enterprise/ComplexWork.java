package com.example.eps.ins.common.entity.enterprise;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("t_complex_work")
public class ComplexWork {

  /**
   * 全局唯一标识符
   */
  @TableId(value = "id",type = IdType.AUTO)
  private Long id;

  /**
   * 复工复产类型：1-机器设备排查;2-集中培训;3-技改
   */
  @TableField("complex_type")
  private String complexType;

  /**
   * 复工复产图片
   */
  @TableField("complex_image")
  private String complexImage;
  /**
   * 复工复产文件
   */
  @TableField("complex_file")
  private String complexFile;

  /**
   * 创建时间
   */
  @TableField("create_time")
  private Date createTime;

  /**
   * 地区id
   */
  @TableField("region_id")
  private Long regionId;

  /**
   * 创建人
   */
  @TableField("creator_id")
  private Long creatorId;

  @TableField(exist = false)
  private String enterpriseName;

  @TableField(exist = false)
  private List<String> created;
}
