package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_industry_scatter")
public class TRptIndustryScatter {
  /**
   *唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;
  /**
   *行业id
   */
  @TableField("industry_id")
  private long industryId;
  /**
   *行业
   */
  @TableField("industry_name")
  private String industryName;
  /**
   *单位数量
   */
  @TableField("unit_num")
  private int unitNum;
  /**
   *年份
   */
  @TableField("year")
  private int year;
  /**
   *地区
   */
  @TableField("name")
  private String name;
  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;
}
