package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2021/12/27 13:58
 * @Version: 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_rectify_data_view")
public class TRptRectifyDataView {
    /**
     *唯一标识
     */
    @TableId(value = "id",type = IdType.AUTO)
    private long id;

    /**
     *整改中
     */
    @TableField("rectify")
    private int rectify;
    /**
     * 未整改
     */
    @TableField("no_rectify")
    private int noRectify;
    /**
     * 已整改单位
     */
    @TableField("yes_rectify")
    private int yesRectify;
    /**
     * 整改总数
     */
    @TableField("rectify_number")
    private int rectifyNumber;
    /**
     *年份
     */
    @TableField("year")
    private int year;
    /**
     *地区
     */
    @TableField("name")
    private String name;
    /**
     *地区id
     */
    @TableField("region_id")
    private long regionId;
    /**
     *管理局名称
     */
    @TableField("dept")
    private String dept;
}
