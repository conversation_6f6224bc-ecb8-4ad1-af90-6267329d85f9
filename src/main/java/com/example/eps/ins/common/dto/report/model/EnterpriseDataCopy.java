package com.example.eps.ins.common.dto.report.model;//package com.utopia.eps.server.system.model;
//
//import com.github.biyanwen.annotation.CsvProperty;
//import com.wuwenze.poi.annotation.Excel;
//import lombok.Data;
//
//import java.io.Serializable;
//
//
//@Data
//@Excel("单位数据")
//public class EnterpriseDataCopy implements Serializable {
//    private static final long serialVersionUID = 921991157363932095L;
//    /**
//     * 企业名称
//     */
//    @CsvProperty(name = "企业名称")
//    private String name;
//
//    /**
//     * 社信代码
//     */
//    @CsvProperty(name = "社信代码")
//    private String socialCode;
//
//    /**
//     * 行业类别
//     */
//    @CsvProperty(name = "行业类别")
//    private String industry;
//
//    /**
//     * 企业性质
//     */
//    @CsvProperty(name="单位性质")
//    private String nature;
//
//    /**
//     * 所属地区
//     */
//    @CsvProperty(name="所属区县")
//    private String regionCounty;
//
//    /**
//     * 所属镇街
//     */
//    @CsvProperty(name="所属镇街")
//    private String regionTown;
//
//    /**
//     * 从业人数
//     */
//    @CsvProperty(name="从业人数")
//    private String peopleNumber;
//    /**
//     * 法定代表人姓名
//     */
//    @CsvProperty(name="法人代表/主要负责人")
//    private String legalRepresentativeName;
//    /**
//     * 法定代表人联系电话
//     */
//    @CsvProperty(name="法人电话")
//    private String legalRepresentativePhone;
//
//    /**
//     * 安全负责人姓名
//     */
//    @CsvProperty(name="安全负责人")
//    private String safetyDirectorName;
//
//    /**
//     * 安全负责人电话
//     */
//    @CsvProperty(name="安全负责人电话")
//    private String safetyDirectorPhone;
//
//    /**
//     * 专职安全员人数
//     */
//    @CsvProperty(name="专职安全员人数")
//    private Integer safetyFullNumber;
//
//    /**
//     * 兼职安全员人数
//     */
//    @CsvProperty(name="兼职安全员人数")
//    private Integer safetyPartNumber;
//
//    /**
//     * 企业地址
//     */
//    @CsvProperty(name="注册地址")
//    private String address;
//
//    /**
//     * 生产经营状态
//     */
//    @CsvProperty(name="生产经营状态")
//    private String productionStatus;
//
//    /**
//     * 安全三同时：0-无；1-有
//     */
//    @CsvProperty(name="有无安全三同时或现状评价")
//    private String currentEvaluationStatus;
//    /**
//     * 安全三同时或现状评价时间
//     */
//    @CsvProperty(name="安全三同时或现状评价时间")
//    private String currentEvaluation;
//    /**
//     * 安全生产标准化是否达标
//     */
//    @CsvProperty(name="安全生产标准化是否达标")
//    private String safetyBck;
//    /**
//     * 安全生产标准化情况对标达标等级
//     */
//    @CsvProperty(name="对标达标等级")
//    private String safetyBckLevel;
//    /**
//     * 安全生产标准化情况对标达标时间
//     */
//    @CsvProperty(name="对标达标时间")
//    private String safetyBckTime;
//
//    /**
//     * 安全生产标准化情况专业达标时间
//     */
//    @CsvProperty(name="专业达标时间")
//    private String safetyMajorTime;
//
//    /**
//     * 安全生产标准化情况岗位达标时间
//     */
//    @CsvProperty(name="岗位达标时间")
//    private String safetyPostTime;
//    /**
//     * 应急预案评审
//     */
//    @CsvProperty(name="应急预案是否具备")
//    private String emergencyPlan;
//    /**
//     * 应急预案评审
//     */
//    @CsvProperty(name="应急预案是否评审")
//    private String emergencyPlanReview;
//    /**
//     * 应急预案备案
//     */
//    @CsvProperty(name="应急预案是否备案")
//    private String emergencyPlanRecord;
//    /**
//     * 规上规下企业：0-规下；1-规上
//     */
//    @CsvProperty(name="规上规下企业")
//    private String scaleStatus;
//    /**
//     * 安全保险1-工商保险;2-安全责任保险
//     */
//    @CsvProperty(name="安全保险")
//    private String safetyInsurance;
//
//
//}
