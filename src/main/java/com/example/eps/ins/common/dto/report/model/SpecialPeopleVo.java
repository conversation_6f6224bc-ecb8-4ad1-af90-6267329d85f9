package com.example.eps.ins.common.dto.report.model;

import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
public class SpecialPeopleVo  extends QueryRequest {
    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 姓名
     */
    private String specialName;
    /**
     * 岗位
     */
    private String specialPost;
    /**
     * 证书类别
     */
    private String specialCertificate;
    /**
     * 资格证号
     */
    private String specialQualifications;

    /**
     * 颁证机关
     */
    private String specialOffice;
    /**
     * 证书到期时间
     */
    private String specialExpirationTime;
    /**
     * 身份证号
     */
    private String specialIdCard;
    /**
     * 新建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 所属地区
     */
    private Long regionId;

    private String enterpriseName;

    private List<String> created;

}
