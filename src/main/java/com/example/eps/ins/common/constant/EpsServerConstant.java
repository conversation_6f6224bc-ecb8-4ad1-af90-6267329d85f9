package com.example.eps.ins.common.constant;

/**
 * <AUTHOR>
 */
public interface EpsServerConstant {

    String EPS_AUTH = "EPS-Auth";
    String EPS_CLOUD = "EPS-Cloud";
    String EPS_COMMON = "EPS-Common";
    String EPS_COMMON_CORE = "EPS-Common-Core";
    String EPS_COMMON_DATASOURCE = "EPS-Common-Datasource";
    String EPS_COMMON_REDIS = "EPS-Common-Redis";
    String EPS_GATEWAY = "EPS-Gateway";
    String EPS_APM = "EPS-APM";
    String EPS_ADMIN = "EPS-Admin";
    String EPS_SERVER = "EPS-Server";
    String EPS_SERVER_SYSTEM = "EPS-Server-System";
    String EPS_SERVER_TEST = "EPS-Server-Test";
    String EPS_SERVER_DOCUMENT = "EPS-Server-Document";
    String EPS_SERVER_JOB = "EPS-Server-Job";
    String EPS_SERVER_GENERATOR = "EPS-Server-Generator";
    String EPS_SERVER_SOCKET = "EPS-Server-Socket";
    String EPS_TX_MANAGER = "EPS-TX-Manager";
    String EPS_SERVER_ENTERPRISE = "EPS-Server-Enterprise";
}
