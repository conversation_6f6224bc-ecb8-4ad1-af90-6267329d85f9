package com.example.eps.ins.common.inteceptor;

import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.reflection.MetaObject;

/**
 * 数据权限处理
 */
public interface IDataPermissionHandle {
    /**
     * 执行数据权限逻辑.
     *
     * @return
     */
    default void doDataPermission(MetaObject metaObject, MappedStatement mappedStatement, BoundSql boundSql){

    }
}
