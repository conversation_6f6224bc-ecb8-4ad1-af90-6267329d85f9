package com.example.eps.ins.common.properties;

import lombok.Data;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 */
@SpringBootConfiguration
@ConfigurationProperties(prefix = "eps.sms")
@Data
public class EpsSmsProperties {

    private String url;
    private String account;
    private String password;
    @NestedConfigurationProperty
    private SmsTemplate register = new SmsTemplate();
    @NestedConfigurationProperty
    private SmsTemplate login = new SmsTemplate();
    @NestedConfigurationProperty
    private SmsTemplate resetPassword = new SmsTemplate();
}
