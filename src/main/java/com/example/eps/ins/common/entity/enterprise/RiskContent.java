package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
* 行业表 Entity
*
* <AUTHOR>
* @date 2021-10-20 13:49:40
*/
@Data
@TableName("t_risk_content")
public class RiskContent {

    /**
     * 全局唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 描述
     */
    @TableField("content_text")
    private String contentText;

    /**
     * 创建人
     */
    @TableField("risk_id")
    private Long riskId;

    /**
     * 创建日期
     */
    @TableField("industry_id")
    private Long industryId;

    @TableField(exist = false)
    private String contentTextUser;

}