package com.example.eps.ins.common.configure;

import com.github.dreamyoung.mprelation.AutoMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AutoMapperConfigure {
    @Bean
    public AutoMapper autoMapper() {
        return new AutoMapper(new String[] {"com.example.eps.ins.enterprise.common.entity.enterprise" });
    }
}
