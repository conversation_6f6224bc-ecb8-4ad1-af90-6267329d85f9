package com.example.eps.ins.common.dto.report.model;

import lombok.Data;

@Data
public class EpsError {
    private String column;
    private String message;

    public EpsError(String column, String message) {
        this.column = column;
        this.message = message;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer();
        sb.append(column).append(":").append(message);
        return sb.toString();
    }
}
