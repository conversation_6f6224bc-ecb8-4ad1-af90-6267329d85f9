package com.example.eps.ins.common.entity.router;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 构建 Vue路由
 *
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VueRouter<T> implements Serializable {

    private static final long serialVersionUID = -3327478146308500708L;

    private String id;
    private String parentId;

    private String path;
    private String name;
    private String component;
    private String redirect;
    private RouterMeta meta;
    private Boolean hidden = false;
    private Boolean alwaysShow = false;
    private Integer orderNum;
    private String icon;
    private String movePath;
    private String moveMenuName;
    private String moveIcon;
    private String moveColor;
    private List<VueRouter<T>> children;

    private Boolean hasParent = false;
    private Boolean hasChildren = false;

    public void initChildren() {
        this.children = new ArrayList<>();
    }

}
