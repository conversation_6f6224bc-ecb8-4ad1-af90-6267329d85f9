package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_sms_log")
public class SmsLog {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 新建时间
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 用户电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 短信次数
     */
    @TableField("count_num")
    private Integer countNum;
}
