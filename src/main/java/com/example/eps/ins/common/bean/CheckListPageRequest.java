package com.example.eps.ins.common.bean;

import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.List;

/**
 * @Description: <检查清单表Page查询>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Data
public class CheckListPageRequest extends QueryRequest {
    /**
     * 违法类别;
     */
    private Integer illegalType;
    /**
     * 状态;0-启用;1-停用
     */
    private Integer status;
    private String illegalDesc;
    private Integer illegalTypeCategory;
    private List<Integer> illegalTypeList;

    private List<Long> risks;//风险
    private List<Long> keyEnterprises;//三类
    private List<Long> industries;//行业
}