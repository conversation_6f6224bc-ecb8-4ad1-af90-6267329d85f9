package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
* 企业风险表 Entity
*
* <AUTHOR>
* @date 2021-10-20 13:49:43
*/
@Data
@TableName("t_enterprise_risk")
public class EnterpriseRisk {

    /**
     * 企业ID/企业申报ID
     */
    @TableField("enterprise_id")
    private Long enterpriseId;

    /**
     * 风险类型ID
     */
    @TableField("risk_id")
    private Long riskId;

    /**
     * 选择风险类型值
     */
    @TableField("value")
    private String value;

    public EnterpriseRisk(Long enterpriseId, Long riskId, String value) {
        this.enterpriseId = enterpriseId;
        this.riskId = riskId;
        this.value = value;
    }

    public EnterpriseRisk() {
    }
}