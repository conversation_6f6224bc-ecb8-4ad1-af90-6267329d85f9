package com.example.eps.ins.common.utils;


/**
 * TMS api response model.
 *
 * <AUTHOR>
 */
public class Response {
    /**
     * Http status code.
     */
    private int statusCode;
    /**
     * Http response body.
     */
    private String result;

    /**
     * Get method.
     *
     * @return The status code.
     */
    public int getStatusCode() {
        return statusCode;
    }

    /**
     * Set method.
     *
     * @param statusCode The status code.
     */
    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    /**
     * Get method.
     *
     * @return The result.
     */
    public String getResult() {
        return result;
    }

    /**
     * Set method.
     *
     * @param result The result.
     */
    public void setResult(String result) {
        this.result = result;
    }

    /**
     * The to string method.
     */
    @Override
    public String toString() {
        return "TmsResponse{" +
                "statusCode=" + statusCode +
                ", result='" + result + '\'' +
                '}';
    }
}
