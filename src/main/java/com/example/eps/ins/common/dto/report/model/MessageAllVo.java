package com.example.eps.ins.common.dto.report.model;


import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MessageAllVo extends QueryRequest {

  private Long id;
  /**
   *内容
   */
  private String contentText;
  /**
   *行业id
   */
  private Long industryId;
  /**
   *创建人
   */
  private Long createId;
  /**
   *创建时间
   */
  private Date createTime;

  /**
   * 行业id
   */
  private List<Long> industryIds;
  private List<String> publishTimeSlot;
}
