package com.example.eps.ins.common.dto.device;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 设备绑定响应DTO
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class DeviceBindingResponse {
    
    /**
     * 绑定记录ID
     */
    private Long id;
    
    /**
     * 企业ID
     */
    private Long enterpriseId;
    
    /**
     * 企业名称
     */
    private String enterpriseName;
    
    /**
     * 外部设备ID
     */
    private Long externalDeviceId;
    
    /**
     * 设备ID
     */
    private Long deviceId;
    
    /**
     * 设备名称（用户输入的自定义名称）
     */
    private String deviceName;
    
    /**
     * 设备型号
     */
    private String deviceModel;
    
    /**
     * 绑定状态（0-未绑定，1-已绑定，2-已解绑）
     */
    private Integer bindingStatus;
    
    /**
     * 绑定状态描述
     */
    private String bindingStatusDesc;
    
    /**
     * 绑定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bindingTime;
    
    /**
     * 解绑时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date unbindingTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 设备序列号（来自设备表）
     */
    private String deviceSerial;
    
    /**
     * 设备状态（来自设备表）
     */
    private String deviceStatus;
    
    /**
     * 设备IP地址（来自设备表）
     */
    private String deviceIp;
}
