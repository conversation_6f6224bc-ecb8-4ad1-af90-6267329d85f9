package com.example.eps.ins.common.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
/**
 * 创建记录返回.
 * <AUTHOR>
 */
@Data
public class CreatedResponse implements Serializable {
  private static final long serialVersionUID = 1L;

  @JsonProperty("id")
  private Long id = null;

  public CreatedResponse(Long id) {
    this.id = id;
  }

  public CreatedResponse() {
  }

  public CreatedResponse id(Long id) {
    this.id = id;
    return this;
  }
}

