package com.example.eps.ins.common.dto.report.model;

import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
public class HiddenDangerVo extends QueryRequest {
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 6516800943817041235L;
    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 隐患排查制度名称
     */

    private String hiddenDangerName;
    /**
     * 隐患排查制度维护文件
     */
    private String hiddenDangerFile;
    /**
     * 摘要
     */
    private String digest;

    /**
     * 上传时间
     */
    private Date createTime;
    /**
     * 所属地区
     */
    private Long regionId;

    private String enterpriseName;

    private List<String> created;
}
