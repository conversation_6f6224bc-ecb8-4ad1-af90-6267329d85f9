package com.example.eps.ins.common.service;

import com.github.tobato.fastdfs.domain.fdfs.MetaData;
import com.github.tobato.fastdfs.domain.fdfs.StorePath;
import com.github.tobato.fastdfs.domain.proto.storage.DownloadByteArray;
import com.github.tobato.fastdfs.service.FastFileStorageClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.net.SocketException;
import java.util.Set;

/**
 * FastDFS重试服务
 * 提供带重试机制的FastDFS操作，解决网络连接问题
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FastDfsRetryService {

    private final FastFileStorageClient fastFileStorageClient;

    /**
     * 上传文件（带重试机制）
     * 
     * @param inputStream 文件输入流
     * @param fileSize 文件大小
     * @param fileExtName 文件扩展名
     * @param metaDataSet 元数据
     * @return 存储路径
     */
    @Retryable(
        value = {SocketException.class, RuntimeException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public StorePath uploadFile(InputStream inputStream, long fileSize, String fileExtName, Set<MetaData> metaDataSet) {
        try {
            log.debug("开始上传文件，大小: {}字节，扩展名: {}", fileSize, fileExtName);
            
            StorePath storePath = fastFileStorageClient.uploadFile(inputStream, fileSize, fileExtName, metaDataSet);
            
            log.debug("文件上传成功，路径: {}", storePath.getFullPath());
            
            return storePath;
            
        } catch (Exception e) {
            log.error("FastDFS上传文件时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载文件（带重试机制）
     * 
     * @param groupName 组名
     * @param path 文件路径
     * @return 文件字节数组
     */
    @Retryable(
        value = {SocketException.class, RuntimeException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public byte[] downloadFile(String groupName, String path) {
        try {
            log.debug("开始下载文件，组名: {}，路径: {}", groupName, path);
            
            DownloadByteArray downloadByteArray = new DownloadByteArray();
            byte[] bytes = fastFileStorageClient.downloadFile(groupName, path, downloadByteArray);
            
            log.debug("文件下载成功，大小: {}字节", bytes != null ? bytes.length : 0);
            
            return bytes;
            
        } catch (Exception e) {
            log.error("FastDFS下载文件时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除文件（带重试机制）
     * 
     * @param groupName 组名
     * @param path 文件路径
     */
    @Retryable(
        value = {SocketException.class, RuntimeException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2)
    )
    public void deleteFile(String groupName, String path) {
        try {
            log.debug("开始删除文件，组名: {}，路径: {}", groupName, path);
            
            fastFileStorageClient.deleteFile(groupName, path);
            
            log.debug("文件删除成功");
            
        } catch (Exception e) {
            log.error("FastDFS删除文件时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("文件删除失败: " + e.getMessage(), e);
        }
    }
}
