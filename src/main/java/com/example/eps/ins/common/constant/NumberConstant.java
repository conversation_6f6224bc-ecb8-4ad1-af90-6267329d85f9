package com.example.eps.ins.common.constant;

import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.exception.EpsException;

import java.math.BigDecimal;

public class NumberConstant {
    /**
     * 2021年事故统计开始结束时间
     */
    public static final String START_TIME="-01-01 00:00:00";
    public static final String END_TIME="-12-31 23:59:59";
    /**
     * 涉燃爆粉尘
     */
    public static final String BURST="RBFC_i1";
    /**
     *涉高温熔炉
     */
    public static final String FURNACE="GWRR_i2";
    /**
     *涉冶金煤气
     */
    public static final String COAL_GAS="YJMQ_i3";
    /**
     *涉氨制冷
     */
    public static final String AMMONIA_REFRIGERATION="AZL_i4";
    /**
     *涉有限空间
     */
    public static final String FINITE_SPACE="YXKJ_i5";
    /**
     *涉其他燃爆毒危化品
     */
    public static final String OTHER_TOXIC_CHEMICALS="QTRBDWHP_i6";
    /**
     * 所有涉及的风险集合
     */
    public static final String[] LIST ={BURST,FURNACE,COAL_GAS,AMMONIA_REFRIGERATION,FINITE_SPACE,OTHER_TOXIC_CHEMICALS};
    /**
     * 地区层级1
     */
    public static final int LEVE_ONE=1;
    public static final Integer LEVE_TWO = 2;
    /**
     * 地区层级3
     */
    public static final int LEVE_THREE=3;
    public static final Integer ZERO=0;
    public static final Integer ONE=1;
    public static final long INDUSTRY_ZERO=0;
    public static final long RISK_PARENT_ID=0;
    public static final BigDecimal LOSS= BigDecimal.valueOf(0);

    /**
     * 比较地区层级关系
     * @param userRegion 用户所在地区
     * @param region 参数查询地区
     */
    public static void regionCompareUserRegion(Region userRegion, Region region){
        //登录人跨级查询数据，抛出异常
        if (region.getLevel() < userRegion.getLevel()){
            throw new EpsException("权限不足!");
        }
        //登录人地区层级为三，但是与参数的地区id不同，抛出异常
        if (userRegion.getLevel().equals(NumberConstant.LEVE_THREE) &&
                !userRegion.getId().equals(region.getId())){
            throw new EpsException("权限不足!");
        }
        //登录人地区层级是二，但是参数的parentId不是登录人的id，抛出异常
        if (userRegion.getLevel().equals(NumberConstant.LEVE_TWO)
                && !region.getLevel().equals(NumberConstant.LEVE_TWO)
                && !userRegion.getId().equals(region.getParentId())){
            throw new EpsException("权限不足!");
        }
        //登录人和传参地区id都是层级二，但是id不同，抛出异常
        if (userRegion.getLevel().equals(NumberConstant.LEVE_TWO)
                && region.getLevel().equals(NumberConstant.LEVE_TWO)
                && !region.getId().equals(userRegion.getId())){
            throw new EpsException("权限不足!");
        }
    }
}
