package com.example.eps.ins.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 最小化Tomcat配置类
 * 使用最基本的配置方式，确保与Spring Boot 2.2.4完全兼容
 */
@Configuration
@Slf4j
public class MinimalTomcatConfig {

    /**
     * 最小化Tomcat配置
     */
    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> minimalTomcatCustomizer() {
        return new WebServerFactoryCustomizer<TomcatServletWebServerFactory>() {
            @Override
            public void customize(TomcatServletWebServerFactory factory) {
                log.info("开始最小化Tomcat配置...");
                
                try {
                    // 添加连接器自定义配置
                    factory.addConnectorCustomizers(connector -> {
                        try {
                            log.info("配置Tomcat连接器属性...");
                            
                            // 设置最大POST大小
                            connector.setMaxPostSize(104857600);
                            
                            // 设置连接超时时间（通过属性设置）
                            connector.setAttribute("connectionTimeout", 120000);
                            
                            // 设置基本的连接属性
                            connector.setAttribute("keepAliveTimeout", 60000);
                            connector.setAttribute("maxKeepAliveRequests", 100);
                            
                            log.info("Tomcat连接器配置成功: maxPostSize={}bytes, connectionTimeout={}ms",
                                    104857600, 120000);
                                
                        } catch (Exception e) {
                            log.warn("连接器配置失败: {}", e.getMessage());
                        }
                    });
                    
                    log.info("最小化Tomcat配置完成");
                    
                } catch (Exception e) {
                    log.error("Tomcat配置失败: {}", e.getMessage());
                }
            }
        };
    }
}
