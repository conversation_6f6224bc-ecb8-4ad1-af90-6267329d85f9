package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_area_accident")
public class TRptAreaAccident {

  /**
   * 唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;

  /**
   *区县事故总数
   */
  @TableField("area_num")
  private long areaNum;

  /**
   *年份
   */
  @TableField("year")
  private Integer year;

  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;

  /**
   *地区名
   */
  @TableField("name")
  private String name;
}
