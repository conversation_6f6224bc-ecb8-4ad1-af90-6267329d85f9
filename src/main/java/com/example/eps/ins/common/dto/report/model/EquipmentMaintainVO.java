package com.example.eps.ins.common.dto.report.model;

import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
public class EquipmentMaintainVO  extends QueryRequest {
    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 设备名称
     */
    private String equipmentName;
    /**
     * 设备型号
     */
    private String equipmentModel;
    /**
     * 位置
     */
    private String equipmentAddress;
    /**
     * 保养维护日期
     */
    private String maintenanceDate;

    /**
     * 保养维护内容
     */
    private String maintenanceDetails;
    /**
     * 保养维护单位
     */
    private String maintenanceCompany;
    /**
     * 保养人员
     */
    private String maintenancePeople;
    /**
     * 现场保养图片
     */
    private String maintenanceImage;
    /**
     * 现场保养图片
     */
    private String maintenanceFile;
    /**
     * 新建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 所属地区
     */
    private Long regionId;
    /**
     * 所属地区
     */
    private Integer maintenanceType;

    private String enterpriseName;

    private List<String> created;
}
