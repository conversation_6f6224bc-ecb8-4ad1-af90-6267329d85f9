package com.example.eps.ins.common.entity.enterprise;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("t_finite_space_work")
public class FiniteSpaceWork {

  /**
   * 全局唯一标识符
   */
  @TableId(value = "id",type = IdType.AUTO)
  private Long id;

  /**
   * 场所/单元
   */
  @TableField("space_address")
  private String spaceAddress;

  /**
   * 有限空间名称
   */
  @TableField("space_name")
  private String spaceName;

  /**
   * 主要危险因素
   */
  @TableField("space_danger")
  private String spaceDanger;

  /**
   * 作业单位或部门
   */
  @TableField("space_dept")
  private String spaceDept;

  /**
   * 监护人
   */
  @TableField("space_guardian")
  private String spaceGuardian;

  /**
   * 作业内容
   */
  @TableField("space_content")
  private String spaceContent;

  /**
   * 作业人员
   */
  @TableField("space_people")
  private String spacePeople;

  /**
   * 作业开始时间段
   */
  @TableField("space_start_time")
  private String spaceStartTime;
  /**
   * 作业结束时间段
   */
  @TableField("space_end_time")
  private String spaceEndTime;
  /**
   * 主要安全措施
   */
  @TableField("space_security")
  private String spaceSecurity;

  /**
   * 主要应急措施
   */
  @TableField("space_emergency")
  private String spaceEmergency;

  /**
   * 有限空间作业许可证
   */
  @TableField("space_file")
  private String spaceFile;

  /**
   * 创建时间
   */
  @TableField("create_time")
  private Date createTime;

  /**
   * 地区id
   */
  @TableField("region_id")
  private Long regionId;

  /**
   * 创建人
   */
  @TableField("creator_id")
  private Long creatorId;

  @TableField(exist = false)
  private String enterpriseName;

  @TableField(exist = false)
  private List<String> created;
}
