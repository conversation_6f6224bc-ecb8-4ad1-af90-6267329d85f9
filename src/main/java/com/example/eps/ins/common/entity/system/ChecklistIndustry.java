package com.example.eps.ins.common.entity.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: <清单行业表实体>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Data
@TableName("t_checklist_industry")
public class ChecklistIndustry implements Serializable{
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 9204443138355402836L;

    /**
     * 检查清单ID
     */
    @TableField(value = "checklist_id")
    private Long checklistId;
    /**
     * 行业ID
     */
    @TableField(value = "industry_id")
    private Long industryId;
}