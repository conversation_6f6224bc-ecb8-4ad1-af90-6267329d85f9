package com.example.eps.ins.common.dto.riskPush;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 相机响应DTO
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class CameraResponse {
    
    /**
     * 子设备ID
     */
    private Long id;
    
    /**
     * 外部子设备ID
     */
    private Long externalSubDeviceId;
    
    /**
     * 父设备ID
     */
    private Long parentDeviceId;
    
    /**
     * 企业ID
     */
    private Long enterpriseId;
    
    /**
     * 企业名称
     */
    private String enterpriseName;
    
    /**
     * 设备名称（父设备）
     */
    private String deviceName;
    
    /**
     * 设备序列号（父设备）
     */
    private String deviceSerial;
    
    /**
     * 设备IP地址（父设备）
     */
    private String deviceIp;
    
    /**
     * 设备类型（父设备）
     */
    private String deviceType;
    
    /**
     * 设备状态（父设备）
     */
    private String deviceStatus;
    
    /**
     * 相机ID（子设备序列号）
     */
    private String cameraId;
    
    /**
     * 相机名称（子设备名称）
     */
    private String cameraName;
    
    /**
     * 相机状态
     */
    private String cameraStatus;
    
    /**
     * 相机类型
     */
    private String cameraType;
    
    /**
     * 相机IP地址
     */
    private String cameraIp;
    
    /**
     * 同步状态（0-未同步，1-已同步）
     */
    private Integer syncStatus;
    
    /**
     * 同步状态描述
     */
    private String syncStatusDesc;
    
    /**
     * 最后同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastSyncTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 绑定状态（来自企业设备绑定表）
     */
    private Integer bindingStatus;
    
    /**
     * 绑定状态描述
     */
    private String bindingStatusDesc;
    
    /**
     * 绑定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bindingTime;
}
