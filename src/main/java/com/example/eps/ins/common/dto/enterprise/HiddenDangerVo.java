package com.example.eps.ins.common.dto.enterprise;

import lombok.Data;

import java.util.Date;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
public class HiddenDangerVo {
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 6516800943817041235L;
    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 隐患排查制度名称
     */

    private String hiddenDangerName;
    /**
     * 隐患排查制度维护文件
     */
    private String hiddenDangerFile;
    /**
     * 摘要
     */
    private String digest;

    /**
     * 上传时间
     */
    private Date createTime;
}
