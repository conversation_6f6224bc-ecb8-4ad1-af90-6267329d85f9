package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_accident_type")
public class TRptAccidentType {
  /**
   *唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;
  /**
   *事故类型
   */
  @TableField("accident")
  private String accident;
  /**
   *此类事故发生数量
   */
  @TableField("accident_num")
  private int accidentNum;
  /**
   *年份
   */
  @TableField("year")
  private int year;
  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;
  /**
   *地区名
   */
  @TableField("name")
  private String name;
}
