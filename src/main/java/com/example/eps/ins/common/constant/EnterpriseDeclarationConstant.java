package com.example.eps.ins.common.constant;

public class EnterpriseDeclarationConstant {
    /**
     * 暂存
     */
    public static final int ENTERPRISE_DECLARATION_STATUS_SAVE= -1;
    /**
     * 已提交
     */
    public static final int ENTERPRISE_DECLARATION_STATUS_WAIT_REVIEW= 0;
    /**
     * 初审通过
     */
    public static final int ENTERPRISE_DECLARATION_STATUS_FIRST_REVIEW_ADOPT = 1;
    /**
     * 初审不通过
     */
    public static final int ENTERPRISE_DECLARATION_STATUS_FIRST_REVIEW_REFUSE = 2;

    /**
     * 复审通过
     */
    public static final int ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_ADOPT = 3;
    /**
     * 复审不通过
     */
    public static final int ENTERPRISE_DECLARATION_STATUS_SECOND_REVIEW_REFUSE = 4;

    /**
     * 审核通过
     */
    public static final int ENTERPRISE_DECLARATION_REVIEW_STATUS_ADOPT = 1;

    /**
     * 审核拒绝
     */
    public static final int ENTERPRISE_DECLARATION_REVIEW_STATUS_REFUSE = 2;

    /**
     * 隐患是否处理（0 未处理  1 处理,2-无需处理）
     */
    public static final int INSPECT_STATUS_OK = 1;
    /**
     * 隐患是否处理（0 未处理  1 处理,2-无需处理）
     */
    public static final int INSPECT_STATUS_NO = 0;
    /**
     * 隐患是否处理（0 未处理  1 处理,2-无需处理）
     */
    public static final int INSPECT_STATUS= 2;


    /**
     * 是否能够修改有无隐患（0 不能  1 能）
     */
    public static final int UPDATE_STATUS_NO = 0;
    /**
     * 日排查
     */
    public static final int HIDDEN_DANGER_ONE = 0;
    /**
     * 是否能够修改有无隐患（0 不能  1 能）
     */
    public static final int UPDATE_STATUS_OK = 1;

    /**
     * 是否隐患（0-否；1是）
     */
    public static final int HIDDEN_STATUS_NO = 0;
    /**
     * 是否隐患（0-否；1是）
     */
    public static final int HIDDEN_STATUS_OK= 1;

    /**
     * 规上企业
     */
    public static final int SCALE_STATUS_UP= 1;
    /**
     * 规下企业
     */
    public static final int SCALE_STATUS_DOWN = 0;
    /**
     * 统计数量一
     */
    public static final int ONE = 1;
    /**
     * 两单两卡已创建
     */
    public static final int ORDER_AND_CARD = 2;

    /**
     * 区级
     */
    public static final int REGION_TWO_LIVE = 2;
    /**
     * 镇街
     */
    public static final int REGION_THREE_LIVE = 3;

    /**
     * 一键分享
     */
    public static final int MESSAGE_ALL_OK= 1;
    /**
     * 规下企业
     */
    public static final int MESSAGE_ALL_NO = 0;
}
