package com.example.eps.ins.common.entity.device;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业设备绑定关系表 Entity
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@TableName("t_enterprise_device_binding")
public class EnterpriseDeviceBinding implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 企业ID
     */
    @TableField("enterprise_id")
    private Long enterpriseId;

    /**
     * 外部设备ID（来自第三方API）
     */
    @TableField("external_device_id")
    private Long externalDeviceId;

    /**
     * 设备ID（关联t_device表的id）
     */
    @TableField("device_id")
    private Long deviceId;

    /**
     * 设备名称（用户输入的自定义名称）
     */
    @TableField("device_name")
    private String deviceName;

    /**
     * 设备型号
     */
    @TableField("device_model")
    private String deviceModel;

    /**
     * 绑定状态（0-未绑定，1-已绑定，2-已解绑）
     */
    @TableField("binding_status")
    private Integer bindingStatus;

    /**
     * 绑定时间
     */
    @TableField("binding_time")
    private Date bindingTime;

    /**
     * 解绑时间
     */
    @TableField("unbinding_time")
    private Date unbindingTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建人ID
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
