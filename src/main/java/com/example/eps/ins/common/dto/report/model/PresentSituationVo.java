package com.example.eps.ins.common.dto.report.model;

import com.example.eps.ins.common.entity.QueryRequest;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
public class PresentSituationVo  extends QueryRequest {
    /**
     * 全局唯一标识符
     */
    private Long id;

    /**
     * 创建人
     */
    private Long creatorId;
    /**
     * 0-安全三同时，1-现状评价
     */
    private Integer presentType;
    /**
     * 安全三同时类别
     * 1.安全生产条件和设施综合分析报告/安全预评价报告；
     * 2.安全设施设计报告；
     * 3.安全设施竣工验收报告。
     */
    private String presentModel;
    /**
     * 图片
     */
    private String presentImage;
    /**
     * 附件
     */
    private String presentFile;
    /**
     * 提交时间
     */
    private String presentTime;
    /**
     * 上传时间
     */
    private Date createTime;
    /**
     * 所属地区
     */
    private Long regionId;

    private String enterpriseName;

    private List<String> created;
}
