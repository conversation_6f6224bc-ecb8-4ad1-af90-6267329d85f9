package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_message_send_one")
public class MessageSendOne {

  /** 全局唯一标识符 */
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /** 发送人 */
  @TableField("send_user")
  private Long sendUser;

  /** 接收人 */
  @TableField("receive_user")
  private Long receiveUser;
  /** 联系电话 */
  @TableField("receive_phone")
  private String receivePhone;
  /** 消息内容 */
  @TableField("message_content")
  private String messageContent;
  /** 接收人姓名 */
  @TableField("receive_name")
  private String receiveName;
  /** 发送人 */
  @TableField("send_name")
  private String sendName;
  /** 新建时间 */
  @TableField("create_time")
  private Date createTime;
  /** 消息状态：0-未读；1-已读 */
  @TableField("status")
  private Integer status;
  @TableField(exist = false)
  private Integer noSee;
}
