package com.example.eps.ins.common.entity.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: <清单风险表实体>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Data
@TableName("t_checklist_risk")
public class ChecklistRisk implements Serializable{
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 7704109094525436195L;

    /**
     * 检查清单ID
     */
    @TableField(value = "checklist_id")
    private Long checklistId;
    /**
     * 风险类型ID
     */
    @TableField(value = "risk_id")
    private Long riskId;
}