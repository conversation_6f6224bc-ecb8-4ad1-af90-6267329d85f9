package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_accident_contrast")
public class TRptAccidentContrast {
  /**
   * 唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;
  /**
   *月份
   */
  @TableField("month")
  private int month;
  /**
   *年份
   */
  @TableField("year")
  private int year;
  /**
   *事故数量
   */
  @TableField("number")
  private int number;
  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;
  /**
   *地区名
   */
  @TableField("name")
  private String name;
}
