package com.example.eps.ins.common.utils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2023/1/11 16:41
 * @Version: 1.0
 */
@Getter
@Setter
@HeadRowHeight(40)
@ContentRowHeight(30)
@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND)
@EqualsAndHashCode
public class MessageExcelAdminDate {

    @ColumnWidth(13)
    @ExcelProperty({"用户类型"})
    private String type;

    @ColumnWidth(30)
    @ExcelProperty({"名称"})
    private String name;

    @ColumnWidth(13)
    @ExcelProperty({"联系电话"})
    private String phone;

}
