package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_stress_three")
public class TRptStressThree {
  /**
   *唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;
  /**
   *三类重点企业单位总数
   */
  @TableField("number")
  private int number;
  /**
   *粉尘涉爆企业数量
   */
  @TableField("explosion_related")
  private int explosionRelated;
  /**
   *钢铁企业数量
   */
  @TableField("steel")
  private int steel;
  /**
   *铝加工企业数量
   */
  @TableField("aluminium")
  private int aluminium;
  /**
   *年份
   */
  @TableField("year")
  private int year;
  /**
   *地区名
   */
  @TableField("name")
  private String name;
  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;

}
