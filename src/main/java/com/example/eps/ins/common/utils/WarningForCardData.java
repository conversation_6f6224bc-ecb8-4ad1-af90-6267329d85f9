package com.example.eps.ins.common.utils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/11/10 10:18
 * @Version: 1.0
 */
@Data
public class WarningForCardData {
    /**
     * 企业名称
     */
    @ColumnWidth(20)
    @ExcelProperty({"企业名称"})
    private String name;
    /**
     * 企业联系人
     */
    @ColumnWidth(13)
    @ExcelProperty({"联系电话"})
    private String mobile;
    /**
     * 所属镇街
     */
    @ColumnWidth(13)
    @ExcelProperty({"所属镇街"})
    private String regionName;
    /**
     * 注册地址
     */
    @ColumnWidth(20)
    @ExcelProperty({"注册地址"})
    private String address;
    /**
     * 预警类型
     */
    @ColumnWidth(13)
    @ExcelProperty({"预警时间"})
    private String time;
    /**
     * 企业端说明
     */
    @ColumnWidth(20)
    @ExcelProperty({"预警类型"})
    private String remarkTwo;
    /**
     * 备注
     */
    @ColumnWidth(20)
    @ExcelProperty({"备注"})
    private String remark;
}
