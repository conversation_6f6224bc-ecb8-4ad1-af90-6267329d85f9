package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
* 行业表 Entity
*
* <AUTHOR>
* @date 2021-10-20 13:49:40
*/
@Data
@TableName("t_industry")
public class Industry {

    /**
     * 全局唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 行业名称
     */
    @TableField("name")
    private String name;

    /**
     * 描述
     */
    @TableField("industry_desc")
    private String industryDesc;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modify_user_id")
    private Long modifyUserId;

    /**
     * 修改日期
     */
    @TableField("modify_time")
    private Date modifyTime;

}