package com.example.eps.ins.common.dto.device;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 设备API响应DTO
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class DeviceApiResponse {
    
    /**
     * 响应状态
     */
    @JsonProperty("status")
    private ApiStatus status;
    
    /**
     * 响应内容
     */
    @JsonProperty("content")
    private DevicePageContent content;
    
    @Data
    public static class ApiStatus {
        /**
         * 状态码
         */
        @JsonProperty("code")
        private Integer code;
        
        /**
         * 状态消息
         */
        @JsonProperty("message")
        private String message;
    }
}
