package com.example.eps.ins.common.dto.riskPush;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 预警查询请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class AlarmQueryRequest {
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 企业名称（支持模糊查询）
     */
    private String enterpriseName;
    
    /**
     * 事件类型列表（可以传多个）
     * 121=安全帽检测, 122=反光衣检测, 123=人员入侵等
     */
    private List<Integer> eventTypes;
    
    /**
     * 告警开始时间
     */
    private String alarmStartTime;
    
    /**
     * 告警结束时间
     */
    private String alarmEndTime;
    
    /**
     * 子设备（摄像头ID或设备序列号）
     */
    private String subDevice;
    
    /**
     * 设备序列号
     */
    private String deviceSerial;
    
    /**
     * 摄像头ID
     */
    private String cameraId;
    
    /**
     * 摄像头名称
     */
    private String cameraName;
    
    /**
     * 是否有告警（0=无, 1=有）
     */
    private Integer haveAlarm;
    
    /**
     * 处理状态（0=未处理, 1=已处理, 2=处理失败）
     */
    private Integer processStatus;
    
    /**
     * 告警状态（0=未告警, 1=已告警）
     */
    private Integer alarmStatus;
    
    /**
     * 设备IP地址
     */
    private String deviceIp;
    
    /**
     * 设备类型
     */
    private String deviceType;
    
    /**
     * 最小置信度
     */
    private Double minConfidence;
    
    /**
     * 最大置信度
     */
    private Double maxConfidence;
}
