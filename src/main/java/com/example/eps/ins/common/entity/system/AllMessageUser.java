package com.example.eps.ins.common.entity.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2023/6/7 19:14
 * @Version: 1.0
 */
@Data
@TableName("t_all_message_user")
public class AllMessageUser {
    /**
     * 消息id
     */
    @TableField("message_id")
    private Long messageId;
    /**
     * 消息id
     */
    @TableField("user_id")
    private Long userId;
    /**
     * 消息id
     */
    @TableField("create_time")
    private Date createTime;

}
