package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON>yin
 * @Date: Created in 2022/11/7 19:07
 * @Version: 1.0
 */
@Data
@TableName("t_train_education_report")
public class TrainEducationReport {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /**
     * 数量
     */
    @TableField("number")
    private Integer number;
    /**
     * 月
     */
    @TableField("month")
    private Integer month;
    /**
     * 年
     */
    @TableField("year")
    private Integer year;
    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;
    /**
     * 所属地区上级id
     */
    @TableField("region_parent_id")
    private Long regionParentId;
}
