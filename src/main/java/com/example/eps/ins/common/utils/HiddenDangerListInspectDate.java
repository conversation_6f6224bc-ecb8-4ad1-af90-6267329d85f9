package com.example.eps.ins.common.utils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
public class HiddenDangerListInspectDate {
    /**
     * 企业名称
     */
    @ColumnWidth(20)
    @ExcelProperty({"企业名称"})
    private String enterpriseName;
    /**
     * 隐患检查周期（0 日 1 周  2 月）
     */
    @ColumnWidth(13)
    @ExcelProperty({"隐患检查周期"})
    private String hiddenDangerCycle;
    /**
     * 隐患内容
     */
    @ColumnWidth(50)
    @ExcelProperty({"隐患内容"})
    private String hiddenDangerContent;
    /**
     * 隐患检查日期
     */
    @ColumnWidth(13)
    @ExcelProperty({"隐患检查日期"})
    private String hiddenDangerInspect;
    /**
     * 隐患部门
     */
    @ColumnWidth(13)
    @ExcelProperty({"隐患部门"})
    private String hiddenDangerDept;
    /**
     * 隐患整改时间
     */
    @ColumnWidth(13)
    @ExcelProperty({"隐患整改时间"})
    private String hiddenDangerReviseTime;
    /**
     * 隐患整改要求
     */
    @ColumnWidth(50)
    @ExcelProperty({"隐患整改要求"})
    private String hiddenDangerRevise;
    /**
     * 检察人员
     */
    @ColumnWidth(13)
    @ExcelProperty({"检查人员"})
    private String inspectPeople;
    /**
     * 形成原因分析
     */
    @ColumnWidth(50)
    @ExcelProperty({"形成原因分析"})
    private String inspectReason;
    /**
     * 纠正措施
     */
    @ColumnWidth(50)
    @ExcelProperty({"纠正措施"})
    private String inspectCorrect;
    /**
     * 验收意见
     */
    @ColumnWidth(50)
    @ExcelProperty({"验收意见"})
    private String inspectOpinion;

}
