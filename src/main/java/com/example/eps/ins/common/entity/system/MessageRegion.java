package com.example.eps.ins.common.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: <消息地区表实体>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Data
@TableName("t_message_region")
public class MessageRegion implements Serializable{
    /**
     * 序列化时候使用
     */
    private static final long serialVersionUID = 5561729421861146576L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 消息表id
     */
    @TableField(value = "message_id")
    private Long messageId;
    /**
     * 地区表id
     */
    @TableField(value = "region_id")
    private Long regionId;
}