package com.example.eps.ins.common.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_rpt_company_view")
public class TRptCompanyView {
  private static final long serialVersionUID = 8116528378002618886L;
  /**
   *唯一标识
   */
  @TableId(value = "id",type = IdType.AUTO)
  private long id;
  /**
   *单位总数
   */
  @TableField("unit_num")
  private int unitNum;
  /**
   *0-30人企业数量
   */
  @TableField("smaller")
  private int smaller;
  /**
   *100人以上企业数量
   */
  @TableField("big")
  private int big;
  /**
   *50-100人企业数量
   */
  @TableField("fifty_to_hundred")
  private int fiftyToHundred;
  /**
   *30-50人企业数量
   */
    @TableField("thirty_to_fifty")
  private int thirtyToFifty;
  /**
   *个体工商户数量
   */
  @TableField("individual")
  private int individual;
  /**
   *年份
   */
  @TableField("year")
  private int year;
  /**
   *地区
   */
  @TableField("name")
  private String name;
  /**
   *地区id
   */
  @TableField("region_id")
  private long regionId;
  /**
   * 企业
   */
  @TableField("enterprise")
  private Integer enterprise;
  /**
   * 企业
   */
  @TableField("ten_people")
  private Integer tenPeople;

}
