package com.example.eps.ins.common.dto.report.model;


import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SpecialCertificateVo  extends QueryRequest {

  /**
   * 全局唯一标识符
   */
  private Long id;

  /**
   * 特种作业许可证类型：
   * 1-动火作业、2-高处作业、3-吊装作业、4-抽堵盲板作业、
   * 5-设备检修作业、6-动土作业、7-断路作业、8-临时用电作业
   */
  private String specialType;

  /**
   * 作业许可证图片
   */
  private String specialFile;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 地区id
   */
  private Long regionId;

  /**
   * 创建人
   */
  private Long creatorId;

  private String enterpriseName;

  private List<String> created;
}
