package com.example.eps.ins.common.exception;

import java.util.List;

public class FileUploadException extends ApplicationException {
    public FileUploadException(String exceptionMessage, ApplicationError applicationError) {
        super(exceptionMessage, applicationError);
    }

    public FileUploadException(String exceptionMessage, List<ApplicationError> errors) {
        super(exceptionMessage, errors);
    }
}
