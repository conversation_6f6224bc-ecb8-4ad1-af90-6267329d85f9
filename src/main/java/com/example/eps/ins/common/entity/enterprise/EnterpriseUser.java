package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.example.eps.ins.common.annotation.IsMobile;
import com.example.eps.ins.common.utils.DateUtil;
import com.example.eps.ins.common.validator.ValidationGroups;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
*  Entity
*
* <AUTHOR>
* @date 2021-10-20 13:49:30
*/
@Data
@TableName("t_enterprise_user")
public class EnterpriseUser {

    /**
     * 企业用户ID
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    @NotNull(groups = ValidationGroups.Update.class, message = "{required.userId}")
    private Long userId;

    /**
     * 手机号码
     */
    @TableField("mobile")
    @IsMobile(message = "{mobile}")
    @NotBlank(message = "{required.mobile}")
    private String mobile;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = DateUtil.FULL_TIME_SPLIT_PATTERN)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = DateUtil.FULL_TIME_SPLIT_PATTERN)
    private Date updateTime;

    @TableField("enterprise_name")
    private String enterpriseName;


    @TableField("password")
    private String password;

    @TableField(exist = false)
    private String sms;

    private transient String createTimeFrom;

    private transient String createTimeTo;
}