package com.example.eps.ins.common.handler;

import com.example.eps.ins.common.exception.ApplicationError;
import com.example.eps.ins.common.exception.ApplicationException;

import java.util.List;

public class FileNotFoundExecption extends ApplicationException {
    public FileNotFoundExecption(String exceptionMessage, ApplicationError applicationError) {
        super(exceptionMessage, applicationError);
    }

    public FileNotFoundExecption(String exceptionMessage, List<ApplicationError> errors) {
        super(exceptionMessage, errors);
    }
}
