package com.example.eps.ins.common.exception;

import javax.annotation.Nonnull;
import java.util.Arrays;
import java.util.Objects;

/**
 * The top error definition.
 *
 * <AUTHOR>
 * @since 16/04/2020
 */
public class ApplicationError {

    protected String errorCode;
    protected String otaErrorCode;
    protected String errorMessage;
    protected String lang;
    protected String errorType;
    protected Object[] i18nMsgParams;

    public ApplicationError(@Nonnull String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(@Nonnull String errorCode) {
        this.errorCode = errorCode;
    }

    public String getOtaErrorCode() {
        return otaErrorCode;
    }

    public void setOtaErrorCode(String otaErrorCode) {
        this.otaErrorCode = otaErrorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public Object[] getI18nMsgParams() {
        return i18nMsgParams;
    }

    public void setI18nMsgParams(Object[] i18nMsgParams) {
        this.i18nMsgParams = i18nMsgParams;
    }

    public ApplicationError errorCode(@Nonnull String errorCode) {
        this.errorCode = errorCode;
        return this;
    }

    public ApplicationError otaErrorCode(String otaErrorCode) {
        this.otaErrorCode = otaErrorCode;
        return this;
    }

    public ApplicationError errorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
        return this;
    }

    public ApplicationError lang(String lang) {
        this.lang = lang;
        return this;
    }

    public ApplicationError errorType(String errorType) {
        this.errorType = errorType;
        return this;
    }

    public ApplicationError i18nMsgParams(Object[] i18nMsgParams) {
        this.i18nMsgParams = i18nMsgParams;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ApplicationError that = (ApplicationError) o;
        return Objects.equals(errorCode, that.errorCode) &&
                Objects.equals(otaErrorCode, that.otaErrorCode) &&
                Objects.equals(errorMessage, that.errorMessage) &&
                Objects.equals(lang, that.lang) &&
                Objects.equals(errorType, that.errorType)&&
                Arrays.equals(i18nMsgParams, that.i18nMsgParams);
    }

    @Override
    public int hashCode() {
        return Objects.hash(errorCode, otaErrorCode, errorMessage, lang,i18nMsgParams);
    }

    @Override
    public String toString() {
        return "ApplicationError{" +
                "errorCode='" + errorCode + '\'' +
                ", otaErrorCode='" + otaErrorCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", lang='" + lang + '\'' +
                ", errorType='" + errorType + '\'' +
                ", i18nMsgParams='" + Arrays.deepToString(this.i18nMsgParams) + '\'' +
                '}';
    }
}
