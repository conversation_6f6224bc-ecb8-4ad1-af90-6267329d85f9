package com.example.eps.ins.common.entity.device;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 子设备信息表 Entity
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@TableName("t_sub_device")
public class SubDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父设备ID（关联t_device表的id）
     */
    @TableField("parent_device_id")
    private Long parentDeviceId;

    /**
     * 外部子设备ID（来自第三方API）
     */
    @TableField("external_sub_device_id")
    private Long externalSubDeviceId;

    /**
     * 子设备名称
     */
    @TableField("device_name")
    private String deviceName;

    /**
     * 子设备序列号
     */
    @TableField("serial")
    private String serial;

    /**
     * 子设备状态（ONLINE/OFFLINE等）
     */
    @TableField("status")
    private String status;

    /**
     * 子设备类型（SUB_DEVICE等）
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 子设备IP地址
     */
    @TableField("ip")
    private String ip;

    /**
     * 同步状态（0-未同步，1-已同步）
     */
    @TableField("sync_status")
    private Integer syncStatus;

    /**
     * 最后同步时间
     */
    @TableField("last_sync_time")
    private Date lastSyncTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}
