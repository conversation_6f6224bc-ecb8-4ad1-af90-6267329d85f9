package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
@TableName("t_train_education")
public class TrainEducation {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 培训主题
     */
    @TableField("train_theme")
    private String trainTheme;
    /**
     * 培训内容
     */
    @TableField("train_content")
    private String trainContent;
    /**
     * 培训图片
     */
    @TableField("train_image")
    private String trainImage;
    /**
     * 培训文档
     */
    @TableField("train_file")
    private String trainFile;

    /**
     * 培训时间
     */
    @TableField("train_date")
    private String trainDate;

    /**
     * 新建人
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 新建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;

    @TableField(exist = false)
    private String enterpriseName;

    @TableField(exist = false)
    private List<String> created;
}
