package com.example.eps.ins.common.handler;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.utils.EpsUtil;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class EpsAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException {
        EpsResponse epsResponse = new EpsResponse();
        EpsUtil.makeJsonResponse(response, HttpServletResponse.SC_FORBIDDEN, epsResponse.message("没有权限访问该资源"));
    }
}
