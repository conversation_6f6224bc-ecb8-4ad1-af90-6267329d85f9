package com.example.eps.ins.common.entity.enterprise;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@TableName("t_outsource_work")
public class OutsourceWork {

  /**
   * 全局唯一标识符
   */
  @TableId(value = "id",type = IdType.AUTO)
  private Long id;

  /**
   * 外委作业类型：
   * 1-有限空间作业、2-建筑作业、3-装修作业、
   * 4-清掏作业、5-设备维护维修作业、6-特种设备、7-其他作业
   */
  @TableField("work_type")
  private Integer workType;

  /**
   * 外委公司名称
   */
  @TableField("work_enterprise")
  private String workEnterprise;

  /**
   * 合同或安全协议等附件上传
   */
  @TableField("work_file")
  private String workFile;
  /**
   * 创建时间
   */
  @TableField("create_time")
  private Date createTime;

  /**
   * 地区id
   */
  @TableField("region_id")
  private Long regionId;

  /**
   * 创建人
   */
  @TableField("creator_id")
  private Long creatorId;

  @TableField(exist = false)
  private String enterpriseName;

  @TableField(exist = false)
  private List<String> created;
}
