package com.example.eps.ins.common.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.example.eps.ins.common.entity.enterprise.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 企业信息表 Entity
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:42
 */
@Data
public class EnterpriseResponse {

    /**
     * 全局唯一标识符
     */
    private Long id;
    /**
     * 证书编号
     */
    private String certificateCode;
    /**
     * 行业ID
     */
    private Long industryId;

    /**
     * 地区-区县ID
     */
    private Long regionCountyId;

    /**
     * 地区-镇街ID
     */
    private Long regionTownId;

    /**
     * 企业性质
     */
    private String nature;
    /**
     * 安全生产标准化 -0：有；1无
     */
    @TableField("safety_bck")
    private Integer safetyBck;
    /**
     * 从业人数
     */
    private String peopleNumber;

    /**
     * 生产经营状态
     */
    private String productionStatus;

    /**
     * 安全三同时或现状评价
     */
    private String currentEvaluation;

    /**
     * 安全生产标准化情况对标达标等级
     */
    private String safetyBckLevel;

    /**
     * 安全生产标准化情况对标达标时间
     */
    private String safetyBckTime;

    /**
     * 安全生产标准化情况专业达标时间
     */
    private String safetyMajorTime;
    /**
     * 公司简介
     */
    private String enterpriseIntroduce;
    /**
     * 安全生产标准化情况岗位达标时间
     */
    private String safetyPostTime;

    private String emergencyPlanReview;

    private String emergencyPlanRecord;

    /**
     * 安全保险
     */
    private List<Integer> safetyInsurance;

    /**
     * 企业名称
     */
    private String name;
    /**
     * 0-无；1-批发  2-仓储   零售   住宿   餐饮
     */
    private List<Integer> tradeType;
    /**
     * 企业地址
     */
    private String address;

    /**
     * 社信代码
     */
    private String socialCode;

    /**
     * 安全负责人姓名
     */
    private String safetyDirectorName;

    /**
     * 安全负责人电话
     */
    private String safetyDirectorPhone;

    /**
     * 专职安全员人数
     */
    private Integer safetyFullNumber;

    /**
     * 兼职安全员人数
     */
    private Integer safetyPartNumber;

    /**
     * 企业联系人姓名
     */
    private String contactName;

    /**
     * 企业联系人电话
     */
    private String contactPhone;

    /**
     * 法定代表人姓名
     */
    private String legalRepresentativeName;

    /**
     * 法定代表人职务
     */
    private String legalRepresentativeJob;

    /**
     * 法定代表人联系电话
     */
    private String legalRepresentativePhone;

    /**
     * 三类重点企业，1-钢铁;2-铝加工;3-粉尘;4-非三类重点企业
     */
    private Integer keyEnterprise;

    /**
     * 生产经营地址
     */
    private String businessAddress;

    /**
     * 企业生产经营地址定位-经度
     */
    private BigDecimal lng;

    /**
     * 企业生产经营地址定位-纬度
     */
    private BigDecimal lat;

    private String risks;
    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 修改人
     */
    private Long modifyUserId;

    /**
     * 修改日期
     */
    private Date modifyTime;

    private EnterpriseUser creator;

    private Industry industry;

    private Region regionCounty;

    private Region regionTown;
    /**
     * 规上规下企业：0-规下；1-规上
     */
    private Integer scaleStatus;
    /**
     * 是否建立隐患排查制度：0-未；1-建立
     */
    private Integer hiddenStatus;
    /**
     * 是否建立应急预案：0-未；1-建立
     */
    private Integer planStatus;
    /**
     *  两单两卡创建情况：0-未创建；1-创建中；2-已创建
     */
    private Integer orderAndCard;
    /**
     * 安全三同时：0-无；1-有
     */
    private Integer currentEvaluationStatus;

    private List<HiddenDanger> hiddenDanger;

    private List<EmergencyPlan> emergencyPlan;

    private List<String> riskTypeSearch;

    /**
     * 企业登录手机号
     */
    private String creatorPhone;
}