package com.example.eps.ins.common.converter;

import com.wuwenze.poi.convert.ReadConverter;
import com.wuwenze.poi.exception.ExcelKitWriteConverterException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 字符串去空加强版:去除两边以及中间的空格
 *
 * <AUTHOR>
 */
@Slf4j
public class StringTrimIntensifyConverter implements ReadConverter {
    @Override
    public String convert(Object value) {
        if (value == null) {
            return StringUtils.EMPTY;
        } else {
            try {
                return value.toString().trim()
                        .replaceAll(" ", "")
                        .replaceAll("　", "");
            } catch (Exception e) {
                String message = "字符串去空异常";
                log.error(message, e);
                throw new ExcelKitWriteConverterException(message);
            }
        }
    }

}
