package com.example.eps.ins.common.constant;


import java.math.BigDecimal;

public class SystemConstant {
    /**
     * 2021年事故统计开始结束时间
     */
    public static final String START_TIME="-01-01 00:00:00";
    public static final String END_TIME="-12-31 23:59:59";
    /**
     * 类别（0-预警警告，1-预警）
     */
    public static final Integer WARNING=1;
    /**
     * 类别（0-预警警告，1-预警）
     */
    public static final Integer EARLY_WARNING=0;
    /**
     *涉有限空间
     */
    public static final String FINITE_SPACE="YXKJ_i5";
    /**
     *涉其他燃爆毒危化品
     */
    public static final String OTHER_TOXIC_CHEMICALS="QTRBDWHP_i6";
    /**
     * 地区层级1
     */
    public static final int LEVE_ONE=1;
    public static final Integer LEVE_TWO = 2;
    /**
     * 地区层级3
     */
    public static final int LEVE_THREE=3;
    public static final Integer ZERO=0;
    public static final Integer ONE=1;
    public static final long INDUSTRY_ZERO=0;
    public static final long RISK_PARENT_ID=0;
    public static final BigDecimal LOSS= BigDecimal.valueOf(0);
}
