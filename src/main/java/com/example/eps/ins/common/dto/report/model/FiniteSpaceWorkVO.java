package com.example.eps.ins.common.dto.report.model;


import com.example.eps.ins.common.entity.QueryRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class FiniteSpaceWorkVO  extends QueryRequest {

  /**
   * 全局唯一标识符
   */
  private Long id;

  /**
   * 场所/单元
   */
  private String spaceAddress;

  /**
   * 有限空间名称
   */
  private String spaceName;

  /**
   * 主要危险因素
   */
  private String spaceDanger;

  /**
   * 作业单位或部门
   */
  private String spaceDept;

  /**
   * 监护人
   */
  private String spaceGuardian;

  /**
   * 作业内容
   */
  private String spaceContent;

  /**
   * 作业人员
   */
  private String spacePeople;

  /**
   * 作业开始时间段
   */
  private String spaceStartTime;
  /**
   * 作业结束时间段
   */
  private String spaceEndTime;
  /**
   * 主要安全措施
   */
  private String spaceSecurity;

  /**
   * 主要应急措施
   */
  private String spaceEmergency;

  /**
   * 有限空间作业许可证
   */
  private String spaceFile;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 地区id
   */
  private Long regionId;

  /**
   * 创建人
   */
  private Long creatorId;

  private String enterpriseName;

  private List<String> created;
}
