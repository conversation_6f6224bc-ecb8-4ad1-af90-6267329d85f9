package com.example.eps.ins.common.bean;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: <检查清单表实体>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Data
public class CheckListRequest {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 违法类别;
     */
    @NotNull(message = "违法类别不能为空")
    private Integer illegalType;
    /**
     * 违法行为概述
     */
    private String illegalDesc;
    /**
     * 相关标准或依据
     */
    private String relevantStandard;
    /**
     * 法律依据
     */
    private String legalBasis;
    /**
     * 法律责任
     */
    private String legalResponsibility;
    /**
     * 连续处罚
     */
    private String continuousPunishment;
    /**
     * 加重情形或并罚情形
     */
    private String aggravatingPunishment;
    /**
     * 备注
     */
    private String checkDesc;
    /**
     * 是否通用;0-通用;1-不通用
     */
    private Integer universal;
    /**
     * 三类重点企业，1-钢铁;2-铝加工;3-粉尘;4-非三类重点企业
     */
    private List<Long> keyEnterprise;
    /**
     * 国标或行标
     */
    private Integer nationalStandard;
    /**
     * 所属规范性文件
     */
    private String normativeDocuments;
    /**
     * 状态;0-启用;1-停用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    private List<Long> risks;//风险
    private List<Long> industries;//行业
}