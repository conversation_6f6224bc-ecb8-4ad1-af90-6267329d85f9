package com.example.eps.ins.common.handler;

import com.example.eps.ins.common.entity.EpsResponse;
import com.example.eps.ins.common.utils.EpsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
public class EpsAuthExceptionEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        String requestUri = request.getRequestURI();
        int status = HttpServletResponse.SC_UNAUTHORIZED;
        String message = "登录状态已失效,请重新登录！";
        log.error("客户端访问{}请求失败: {}", requestUri, message, authException);
        EpsUtil.makeJsonResponse(response, status, new EpsResponse().message(message));
    }
}
