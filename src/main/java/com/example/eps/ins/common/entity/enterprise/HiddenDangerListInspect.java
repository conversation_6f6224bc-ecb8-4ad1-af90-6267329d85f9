package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/10/11 14:41
 * @Version: 1.0
 */
@Data
@TableName("t_hidden_danger_list_inspect")
public class HiddenDangerListInspect {
    /**
     * 全局唯一标识符
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;
    /**
     * 隐患id
     */
    @TableField("hidden_danger_id")
    private Long hiddenDangerId;
    /**
     * 检察人员
     */
    @TableField("inspect_people")
    private String inspectPeople;
    /**
     * 检查日期
     */
    @TableField("inspect_time")
    private String inspectTime;

    /**
     * 通知日期
     */
    @TableField("inspect_notice_time")
    private String inspectNoticeTime;
    /**
     * 形成原因分析
     */
    @TableField("inspect_reason")
    private String inspectReason;
    /**
     * 纠正措施
     */
    @TableField("inspect_correct")
    private String inspectCorrect;
    /**
     * 验收意见
     */
    @TableField("inspect_opinion")
    private String inspectOpinion;
    /**
     * 隐患整改图片上传
     */
    @TableField("inspect_image")
    private String inspectImage;
    /**
     * 隐患整改附件上传
     */
    @TableField("inspect_file")
    private String inspectFile;
    /**
     * 新建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;
    /**
     * 隐患检查日期
     */
    @TableField(exist = false)
    private String hiddenDangerInspect;
    /**
     * 隐患整改时间
     */
    @TableField(exist = false)
    private String hiddenDangerReviseTime;
    /**
     * 隐患检查周期（0 日 1 周  2 月）
     */
    @TableField(exist = false)
    private Integer hiddenDangerCycle;
    /**
     * 隐患内容
     */
    @TableField(exist = false)
    private String hiddenDangerContent;
    /**
     * 隐患部门
     */
    @TableField(exist = false)
    private String hiddenDangerDept;
    /**
     * 隐患整改要求
     */
    @TableField(exist = false)
    private String hiddenDangerRevise;
    /**
     * 隐患整改要求
     */
    @TableField(exist = false)
    private String hiddenDangerEnd;

    /**
     * 所属地区
     */
    @TableField("region_id")
    private Long regionId;
}
