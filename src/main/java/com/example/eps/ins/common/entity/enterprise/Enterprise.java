package com.example.eps.ins.common.entity.enterprise;

import com.baomidou.mybatisplus.annotation.*;
import com.github.dreamyoung.mprelation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 企业信息表 Entity
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:42
 */
@Data
@TableName("t_enterprise")
public class Enterprise {

    /**
     * 全局唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 行业ID
     */
    @TableField(value = "industry_id", whereStrategy = FieldStrategy.NOT_EMPTY)
    private Long industryId;

    /**
     * 地区-区县ID
     */
    @TableField("region_county_id")
    private Long regionCountyId;

    /**
     * 地区-镇街ID
     */
    @TableField("region_town_id")
    private Long regionTownId;

    /**
     * 企业性质
     */
    @TableField(value = "nature", whereStrategy = FieldStrategy.NOT_EMPTY)
    private String nature;

    /**
     * 从业人数
     */
    @TableField(value = "people_number", whereStrategy = FieldStrategy.NOT_EMPTY)
    private String peopleNumber;

    /**
     * 生产经营状态
     */
    @TableField("production_status")
    private String productionStatus;

    /**
     * 安全三同时或现状评价
     */
    @TableField(value = "current_evaluation" ,updateStrategy = FieldStrategy.IGNORED)
    private String currentEvaluation;
    /**
     * 安全三同时：0-无；1-有
     */
    @TableField(value = "current_evaluation_status",updateStrategy = FieldStrategy.IGNORED)
    private Integer currentEvaluationStatus;

    /**
     * 安全生产标准化情况对标达标等级
     */
    @TableField(value = "safety_bck_level",updateStrategy = FieldStrategy.IGNORED)
    private String safetyBckLevel;

    /**
     * 安全生产标准化 -0：有；1无
     */
    @TableField("safety_bck")
    private Integer safetyBck;

    /**
     * 安全生产标准化情况对标达标时间
     */
    @TableField(value = "safety_bck_time" ,updateStrategy = FieldStrategy.IGNORED)
    private String safetyBckTime;

    /**
     * 安全生产标准化情况专业达标时间
     */
    @TableField(value = "safety_major_time" ,updateStrategy = FieldStrategy.IGNORED)
    private String safetyMajorTime;

    /**
     * 安全生产标准化情况岗位达标时间
     */
    @TableField(value = "safety_post_time",updateStrategy = FieldStrategy.IGNORED)
    private String safetyPostTime;

    /**
     * 应急预案
     */
    @TableField(value = "emergency_plan_review",updateStrategy = FieldStrategy.IGNORED)
    private String emergencyPlanReview;

    /**
     * 应急预案
     */
    @TableField(value = "emergency_plan_record",updateStrategy = FieldStrategy.IGNORED)
    private String emergencyPlanRecord;

    /**
     * 安全保险
     */
    @TableField("safety_insurance")
    private String safetyInsurance;

    /**
     * 企业名称
     */
    @TableField("name")
    private String name;

    /**
     * 企业地址
     */
    @TableField("address")
    private String address;

    /**
     * 社信代码
     */
    @TableField("social_code")
    private String socialCode;

    /**
     * 安全负责人姓名
     */
    @TableField("safety_director_name")
    private String safetyDirectorName;

    /**
     * 安全负责人电话
     */
    @TableField("safety_director_phone")
    private String safetyDirectorPhone;

    /**
     * 专职安全员人数
     */
    @TableField(value = "safety_full_number" ,updateStrategy = FieldStrategy.IGNORED)
    private Integer safetyFullNumber;

    /**
     * 兼职安全员人数
     */
    @TableField(value =  "safety_part_number",updateStrategy = FieldStrategy.IGNORED)
    private Integer safetyPartNumber;

    /**
     * 企业联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 企业联系人电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     *  两单两卡创建情况：0-未创建；1-创建中；2-已创建
     */
    @TableField("order_and_card")
    private Integer orderAndCard;

    /**
     * 法定代表人姓名
     */
    @TableField("legal_representative_name")
    private String legalRepresentativeName;

    /**
     * 法定代表人职务
     */
    @TableField("legal_representative_job")
    private String legalRepresentativeJob;

    /**
     * 法定代表人联系电话
     */
    @TableField("legal_representative_phone")
    private String legalRepresentativePhone;

    /**
     * 三类重点企业，1-钢铁;2-铝加工;3-粉尘;4-非三类重点企业
     */
    @TableField(value = "key_enterprise", whereStrategy = FieldStrategy.NOT_EMPTY)
    private Integer keyEnterprise;

    /**
     * 生产经营地址
     */
    @TableField("business_address")
    private String businessAddress;

    /**
     * 企业生产经营地址定位-经度
     */
    @TableField("lng")
    private BigDecimal lng;

    /**
     * 企业生产经营地址定位-纬度
     */
    @TableField("lat")
    private BigDecimal lat;

    /**
     * 风险项
     */
    @TableField("risks")
    private String risks;

    /**
     * 创建人
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 创建日期
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("modify_user_id")
    private Long modifyUserId;

    /**
     * 修改日期
     */
    @TableField("modify_time")
    private Date modifyTime;
    /**
     * 规上规下企业：0-规下；1-规上
     */
    @TableField("scale_status")
    private Integer scaleStatus;

    /**
     * 是否建立隐患排查制度：0-未；1-建立
     */
    @TableField("hidden_status")
    private Integer hiddenStatus;
    /**
     * 是否建立应急预案：0-未；1-建立
     */
    @TableField("plan_status")
    private Integer planStatus;
    /**
     * 修改人
     */
    @TableField("risk_type")
    private String riskType;

    /**
     * 0-无；1-批发  2-仓储   零售   住宿   餐饮
     */
    @TableField("trade_type")
    private String tradeType;
    /**
     * 公司简介
     */
    @TableField("enterprise_introduce")
    private String enterpriseIntroduce;

    @TableField(exist = false)
    @OneToOne
    @JoinColumn(name = "creator_id", referencedColumnName = "user_id", table = "t_enterprise_user")
    private EnterpriseUser creator;

    @TableField(exist = false)
    @OneToOne
    @JoinColumn(name = "industry_id", referencedColumnName = "id", table = "t_industry")
    private Industry industry;

    /**
     * 证书编号
     */
    @TableField("certificate_code")
    private String certificateCode;

    @TableField(exist = false)
    @OneToOne
    @JoinColumn(name = "region_county_id", referencedColumnName = "id", table = "t_region")
    private Region regionCounty;

    @TableField(exist = false)
    @OneToOne
    @JoinColumn(name = "region_town_id", referencedColumnName = "id", table = "t_region")
    private Region regionTown;

//    @TableField(exist = false)
//    private List<Integer> keyEnterpriseSearch;

    @TableField(exist = false)
    private List<String> riskTypeSearch;

    @TableField(exist = false)
    private String creatorPhone;

    /**
     * 半年内未建立隐患排查制度
     */
    @TableField(exist = false)
    private Integer noCommit;

    /**
     * 登录人联系电话
     */
    @TableField(exist = false)
    private String phone;
    /**
     * 所属区县
     */
    @TableField(exist = false)
    private String county;

    /**
     * 所属镇街
     */
    @TableField(exist = false)
    private String town;
    /**
     * 所属镇街
     */
    @TableField(exist = false)
    private String industryName;
    /**
     * 点对点消息未读条数
     */
    @TableField(exist = false)
    private Integer noSee;
}