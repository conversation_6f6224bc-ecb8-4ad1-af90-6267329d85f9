package com.example.eps.ins.common.dto.riskPush;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 预警查询响应DTO
 * 
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class AlarmQueryResponse {
    
    /**
     * 事件ID
     */
    private Long id;
    
    /**
     * 外部事件ID
     */
    private String externalEventId;
    
    /**
     * 企业ID
     */
    private Long enterpriseId;
    
    /**
     * 企业名称
     */
    private String enterpriseName;
    
    /**
     * 设备ID
     */
    private Long deviceId;
    
    /**
     * 设备序列号
     */
    private String deviceSerial;
    
    /**
     * 设备IP地址
     */
    private String deviceIp;
    
    /**
     * 设备类型
     */
    private String deviceType;
    
    /**
     * 设备状态
     */
    private String deviceStatus;
    
    /**
     * 摄像头ID
     */
    private String cameraId;
    
    /**
     * 摄像头名称
     */
    private String cameraName;
    
    /**
     * 事件类型
     */
    private Integer eventType;
    
    /**
     * 事件类型名称
     */
    private String eventTypeName;
    
    /**
     * 事件源
     */
    private Integer eventSource;
    
    /**
     * 置信度
     */
    private Double confidence;
    
    /**
     * 检测分数
     */
    private Double detectionScore;
    
    /**
     * 是否有告警（0=无, 1=有）
     */
    private Integer haveAlarm;
    
    /**
     * 告警状态描述
     */
    private String haveAlarmDesc;
    
    /**
     * 告警次数
     */
    private Integer alarmTimes;
    
    /**
     * 检测对象名称
     */
    private String objectName;
    
    /**
     * 边界框坐标
     */
    private String boundingBox;
    
    /**
     * 图片URL
     */
    private String imgUrl;
    
    /**
     * 大图URL
     */
    private String bigUrl;
    
    /**
     * 图片宽度
     */
    private Integer imgWidth;
    
    /**
     * 图片高度
     */
    private Integer imgHeight;
    
    /**
     * 事件开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    
    /**
     * 事件结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    
    /**
     * 事件标记时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date markTime;
    
    /**
     * 处理状态（0=未处理, 1=已处理, 2=处理失败）
     */
    private Integer processStatus;
    
    /**
     * 处理状态描述
     */
    private String processStatusDesc;
    
    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date processTime;
    
    /**
     * 处理结果
     */
    private String processResult;
    
    /**
     * 告警状态（0=未告警, 1=已告警）
     */
    private Integer alarmStatus;
    
    /**
     * 告警状态描述
     */
    private String alarmStatusDesc;
    
    /**
     * 告警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date alarmTime;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 备注
     */
    private String remark;
}
