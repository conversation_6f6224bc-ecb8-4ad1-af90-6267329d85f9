package com.example.eps.ins.mapper;

import com.example.eps.ins.common.dto.report.resp.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2021/12/14 15:14
 * @Version: 1.0
 */
@Mapper
public interface HomePageMapper {

    EnterpriseViewResp selectByHomeAll(@Param("regionId") Long regionId, @Param("year") String year);


    /**
     * 获取年份
     * @return 年份集合
     * @param regionId 地区id
     */
    List<Integer> getYears(@Param("regionId") Long regionId);

    /**
     * 获取年份数据
     * @param regionId 地区id
     * @param integer 年份
     * @return AccidentYearResp
     */
    AccidentYearResp getAccidentYear(@Param("regionId")Long regionId, @Param("year") Integer integer);

    RiskListResp riskAll(
            @Param("regionId") Long nameId,
            @Param("riskId") Long riskId,
            @Param("year") String year);

    /**
     * 获取年度数据总览
     * @param regionId 地区id
     * @return 数据
     */
    EnforcementHomeResp getDataViewAll(@Param("regionId") Long regionId);
    /**
     * 获取年度数据总览(整改)
     * @param regionId 地区id
     * @return 数据
     */
    EnforcementHomeResp getRectifyAll(@Param("regionId") Long regionId);

    /**
     *
     * @param regionId
     * @return
     */
    List<TRptIndustryScatterResp> getCity( @Param("regionId") Long regionId);

    /**
     * 事故类型排序
     * @return
     * @param regionId
     */
    List<AccidentSortResp> getAccidentSort(@Param("regionId") Long regionId);

    /**
     * 获取区县年度数据总览
     * @param regions 地区id
     * @return 数据
     */
    EnforcementHomeResp getDataViewOrTwoLive(@Param("regionId") List<Long> regions);
    /**
     * 获取区县年度数据总览(整改)
     * @param regions 地区id
     * @return 数据
     */
    EnforcementHomeResp getRectifyOrTwoLive(@Param("regionId") List<Long> regions);

    EnterpriseViewResp selectByHomeAllLiveTwo(@Param("regionId") Long regionId,@Param("year") String year,
                                              @Param("regionParentId")Long regionParentId);

    RiskListResp riskAllTwo(@Param("regionId") Long nameId,
                            @Param("riskId") Long riskId
            ,@Param("year") String year);
}
