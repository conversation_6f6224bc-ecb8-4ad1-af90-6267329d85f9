package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.enterprise.EnterpriseUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *  Mapper
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:30
 */
@Mapper
public interface EnterpriseUserMapper extends BaseMapper<EnterpriseUser> {

    void updateName(@Param("name") String name,@Param("id")Long userId);

    void updatePhone(@Param("mobile") String mobile,@Param("id")Long userId);
}
