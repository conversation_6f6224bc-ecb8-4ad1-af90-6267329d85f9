package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.report.TRptCompanyView;
import com.example.eps.ins.common.dto.report.resp.TRptCompanyViewResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TRptCompanyViewMapper extends BaseMapper<TRptCompanyView> {
    TRptCompanyViewResp selectByParentId(@Param("regionId") Long id,@Param("year") Integer year);

    /**
     * 区级
     * @param regionId
     * @param year
     * @return
     */
    TRptCompanyViewResp selectLiveTwo(@Param("regionId") List<Long> regionId, Integer year);
}
