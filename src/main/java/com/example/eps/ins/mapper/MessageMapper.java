package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.bean.MessageRequest;
import com.example.eps.ins.common.entity.enterprise.Region;
import com.example.eps.ins.common.entity.system.Message;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: Created in 2022/10/31 17:00
 * @Version: 1.0
 */
@Mapper
public interface MessageMapper extends BaseMapper<Message> {

    Message findMessage(@Param("id") long id);

    List<Region> findRegions(@Param("id") long id);

    List<Message> findByPage(@Param("page") MessageRequest page);


    long count(@Param("page") MessageRequest page);

    List<Message> findMessages(@Param("regionId") Long regionId,@Param("type") String type);
}
