package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.entity.system.SystemUser;
import com.example.eps.ins.common.annotation.DataPermission;
import com.example.eps.ins.common.entity.system.UserDataPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@DataPermission(field = "region_id",methods = {"selectPage"})
public interface UserMapper extends BaseMapper<SystemUser> {

    /**
     * 查找用户详细信息
     *
     * @param page 分页对象
     * @param user 用户对象，用于传递查询条件
     * @param <T>  type
     * @return Ipage
     */
    <T> IPage<SystemUser> findUserDetailPage(Page<T> page, @Param("user") SystemUser user);

    /**
     * 查找用户详细信息
     *
     * @param user 用户对象，用于传递查询条件
     * @return List<User>
     */
    List<SystemUser> findUserDetail(@Param("user") SystemUser user);

    /**
     * 获取可查看消息的用户
     * @param regionIds
     * @return
     */
    List<MessageUser> selectMessage(  @Param("regionId")Long regionId,
                                      @Param("regionIds") List<Long> regionIds,
                                      @Param("messageId")Long messageId);

    /**
     * 获取用户
     *
     * @param username 用户名
     * @return 用户
     */
    SystemUser findByName(String username);

    /**
     * 获取用户数据权限
     *
     * @param userId 用户id
     * @return 数据权限
     */
    List<UserDataPermission> findUserDataPermissions(Long userId);
}
