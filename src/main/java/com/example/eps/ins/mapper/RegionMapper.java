package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.dto.report.resp.RegionResp;
import com.example.eps.ins.common.entity.enterprise.Region;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地区表 Mapper
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:39
 */
@Mapper
public interface RegionMapper extends BaseMapper<Region> {
    List<RegionResp> selectRegion();

    /**
     * 查询下级地区id
     * @param id
     * @return
     */
    List<Long> selectSonId(@Param("id") Long id);

    List<Long> selectSon(@Param("currentUser") Long currentUser);
}
