package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.entity.system.MessageUserSee;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: <消息用户关联表Mapper接口>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Mapper
public interface MessageUserSeeMapper extends BaseMapper<MessageUserSee> {

    void insertAll(@Param("messageUsers")List<MessageUser> messageUsers);

}
