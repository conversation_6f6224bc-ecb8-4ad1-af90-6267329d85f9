package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.report.TRptIllegalBehavior;
import com.example.eps.ins.common.dto.report.resp.BehaviorResp;
import com.example.eps.ins.common.dto.report.resp.EnforcementResp;
import com.example.eps.ins.common.dto.report.resp.YearCheckListResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: zhanghongyin
 * @Date: Created in 2021/12/1 17:05
 * @Version: 1.0
 */
@Mapper
public interface BehaviorMapper extends BaseMapper<TRptIllegalBehavior> {
    /**
     * 获取违法数据排序数据
     * @param regionId 地区id
     * @param year 年份
     * @param limit 分页条数
     * @return list
     */
    List<BehaviorResp> selectByCheckId(@Param("regionId") Long regionId, @Param("year") Integer year
    ,@Param("limit")Integer limit);

    /**
     * 获取年度执法数据统计(市)
     * @param year 年份
     * @param regionId 地区id
     * @return 信息
     */
    YearCheckListResp getCityAll(@Param("year") Integer year, @Param("regionId") Long regionId);

    /**
     * 获取年度数据总览
     * @param year 年份
     * @param regionId 地区id
     * @return 数据
     */
    EnforcementResp getDataViewAll(@Param("year") Integer year, @Param("regionId") Long regionId);
    /**
     * 获取年度数据总览(整改)
     * @param year 年份
     * @param regionId 地区id
     * @return 数据
     */
    EnforcementResp getRectifyAll(@Param("year") Integer year, @Param("regionId") Long regionId);

    List<BehaviorResp> selectLiveTwo(@Param("regionId")List<Long> regions, @Param("year")Integer year,
                                     @Param("limit")Integer sort);

    /**
     * 获取区县年度数据总览
     * @param year 年份
     * @param regions 地区id
     * @return 数据
     */
    EnforcementResp getDataViewOrTwoLive(@Param("year")Integer year,@Param("regionId") List<Long> regions);
    /**
     * 获取区县年度数据总览(整改)
     * @param year 年份
     * @param regions 地区id
     * @return 数据
     */
    EnforcementResp getRectifyOrTwoLive(@Param("year")Integer year, @Param("regionId") List<Long> regions);

    /**
     * 获取区县年度执法数据统计(市)
     * @param year 年份
     * @param regions 地区id
     * @return 信息
     */
    YearCheckListResp getCityAllOrTwoLive(@Param("year")Integer year, @Param("regionId") List<Long> regions);
}
