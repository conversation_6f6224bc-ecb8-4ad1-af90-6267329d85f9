package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.eps.ins.common.entity.device.Device;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备信息 Mapper
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Mapper
public interface DeviceMapper extends BaseMapper<Device> {

    /**
     * 根据外部设备ID查询设备
     *
     * @param externalDeviceId 外部设备ID
     * @return 设备信息
     */
    Device selectByExternalDeviceId(@Param("externalDeviceId") Long externalDeviceId);

    /**
     * 根据序列号查询设备
     *
     * @param serial 设备序列号
     * @return 设备信息
     */
    Device selectBySerial(@Param("serial") String serial);

    /**
     * 分页查询设备列表
     *
     * @param page 分页对象
     * @param device 查询条件
     * @return 设备分页列表
     */
    IPage<Device> selectDevicePage(Page<Device> page, @Param("device") Device device);

    /**
     * 批量插入设备信息
     *
     * @param deviceList 设备列表
     * @return 插入数量
     */
    int batchInsert(@Param("deviceList") List<Device> deviceList);

    /**
     * 根据同步状态查询设备列表
     *
     * @param syncStatus 同步状态
     * @return 设备列表
     */
    List<Device> selectBySyncStatus(@Param("syncStatus") Integer syncStatus);
}
