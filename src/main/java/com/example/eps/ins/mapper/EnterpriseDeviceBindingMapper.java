package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.eps.ins.common.dto.device.DeviceBindingResponse;
import com.example.eps.ins.common.entity.device.EnterpriseDeviceBinding;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业设备绑定关系 Mapper
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Mapper
public interface EnterpriseDeviceBindingMapper extends BaseMapper<EnterpriseDeviceBinding> {

    /**
     * 根据企业ID和外部设备ID查询绑定关系
     *
     * @param enterpriseId 企业ID
     * @param externalDeviceId 外部设备ID
     * @return 绑定关系
     */
    EnterpriseDeviceBinding selectByEnterpriseIdAndExternalDeviceId(
            @Param("enterpriseId") Long enterpriseId, 
            @Param("externalDeviceId") Long externalDeviceId);

    /**
     * 根据企业ID查询绑定的设备列表
     *
     * @param enterpriseId 企业ID
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> selectByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

    /**
     * 根据外部设备ID查询绑定关系列表
     *
     * @param externalDeviceId 外部设备ID
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> selectByExternalDeviceId(@Param("externalDeviceId") Long externalDeviceId);

    /**
     * 根据设备ID查询绑定关系列表
     *
     * @param deviceId 设备ID
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> selectByDeviceId(@Param("deviceId") Long deviceId);

    /**
     * 分页查询绑定关系列表（带详细信息）
     *
     * @param page 分页对象
     * @param binding 查询条件
     * @return 绑定关系分页列表
     */
    IPage<DeviceBindingResponse> selectBindingPageWithDetails(
            Page<DeviceBindingResponse> page, 
            @Param("binding") EnterpriseDeviceBinding binding);

    /**
     * 根据企业ID和绑定状态查询绑定关系列表
     *
     * @param enterpriseId 企业ID
     * @param bindingStatus 绑定状态
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> selectByEnterpriseIdAndStatus(
            @Param("enterpriseId") Long enterpriseId, 
            @Param("bindingStatus") Integer bindingStatus);

    /**
     * 根据绑定状态查询绑定关系列表
     *
     * @param bindingStatus 绑定状态
     * @return 绑定关系列表
     */
    List<EnterpriseDeviceBinding> selectByBindingStatus(@Param("bindingStatus") Integer bindingStatus);

    /**
     * 批量插入绑定关系
     *
     * @param bindingList 绑定关系列表
     * @return 插入数量
     */
    int batchInsert(@Param("bindingList") List<EnterpriseDeviceBinding> bindingList);

    /**
     * 更新绑定状态
     *
     * @param id 绑定关系ID
     * @param bindingStatus 绑定状态
     * @param unbindingTime 解绑时间（可选）
     * @return 更新数量
     */
    int updateBindingStatus(
            @Param("id") Long id, 
            @Param("bindingStatus") Integer bindingStatus, 
            @Param("unbindingTime") java.util.Date unbindingTime);

    /**
     * 根据企业ID统计绑定设备数量
     *
     * @param enterpriseId 企业ID
     * @return 绑定设备数量
     */
    int countByEnterpriseId(@Param("enterpriseId") Long enterpriseId);

    /**
     * 根据企业ID和绑定状态统计设备数量
     *
     * @param enterpriseId 企业ID
     * @param bindingStatus 绑定状态
     * @return 设备数量
     */
    int countByEnterpriseIdAndStatus(
            @Param("enterpriseId") Long enterpriseId, 
            @Param("bindingStatus") Integer bindingStatus);
}
