package com.example.eps.ins.mapper;


import com.example.eps.ins.common.dto.report.resp.AccidentAllResp;
import com.example.eps.ins.common.dto.report.resp.AccidentSortResp;
import com.example.eps.ins.common.dto.report.resp.AreaResp;
import com.example.eps.ins.common.dto.report.resp.IndustryAccidentResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccidentReportMapper {
    /**
     * 获取某一区的风险数据
     * @param year 年份
     * @param nameId 地区id
     * @return AccidentAllResp
     */
    AccidentAllResp getOneCityAll(@Param("year") Integer year,@Param("nameId") Long nameId);


    /**
     * 获取全市行业事故统计统计表数据
     * @param industryName 行业名
     * @param year 年份
     * @return AccidentAllResp
     */
    IndustryAccidentResp getIndustryAccident(@Param("industryName") String industryName, @Param("year") Integer year);
    /**
     * 获取全市行业事故统计统计表数据
     * @param year 年份
     * @param nameId 地区id
     * @return AccidentAllResp
     */
    List<IndustryAccidentResp> getOneIndustryAccident(@Param("year") Integer year, @Param("nameId") Long nameId);




    /**
     * 获取事故类型排序表中的某地区的数据

     * @param year 年份
     * @return AccidentSortResp
     */
    List<AccidentSortResp> getAccidentSort(@Param("year")Integer year,@Param("nameId") Long nameId);

    /**
     * 取得区县事故统计数据
     * @param year 年份
     * @return list
     */
    List<AreaResp> getAreaNum(@Param("year") Integer year);
}
