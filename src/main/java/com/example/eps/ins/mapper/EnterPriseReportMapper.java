package com.example.eps.ins.mapper;

import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.dto.report.resp.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: Zhanghongyin
 * @Date: Created in 2022/11/8 15:04
 * @Version: 1.0
 */
@Mapper
public interface EnterPriseReportMapper {
    CakeResp getRiskEnterPrise(@Param("regionId") Long regionId,
                               @Param("regionParentId") Long regionParentId);

    List<RiskListResp> getRisk(@Param("regionId") Long regionId,
                               @Param("regionParentId") Long regionParentId);

    CakeResp getUpAndDown(@Param("regionId") Long regionId,
                          @Param("regionParentId") Long regionParentId);

    EnterpriseProportionResp getThreeNow(@Param("regionId") Long regionId,
                                         @Param("regionParentId") Long regionParentId);

    EnterpriseProportionResp getDangerStandard(@Param("regionId") Long regionId,
                                               @Param("regionParentId") Long regionParentId);

    EnterpriseProportionResp getPlanReport(@Param("regionId") Long regionId,
                                           @Param("regionParentId") Long regionParentId);

    EnterpriseProportionResp getHiddenReport(@Param("regionId") Long regionId,
                                             @Param("regionParentId") Long regionParentId);

    List<EnterpriseHistogramResp> getDrillReport(@Param("regionId") Long regionId,
                                                 @Param("regionParentId") Long regionParentId,
                                                 @Param("year") String year);

    List<HistogramResp> getListReport(@Param("regionId") Long regionId,
                                      @Param("regionParentId") Long regionParentId,
                                      @Param("year") String year);

    List<EnterpriseHistogramResp> getMaintainReport(@Param("regionId") Long regionId,
                                                    @Param("regionParentId") Long regionParentId,
                                                    @Param("year") String year);

    List<EnterpriseHistogramResp> getTrainReport(@Param("regionId") Long regionId,
                                                 @Param("regionParentId") Long regionParentId,
                                                 @Param("year") String year);

    EnterpriseProportionResp getOrderCard(@Param("regionId") Long regionId,
                                          @Param("regionParentId") Long regionParentId);

    List<WarningForCard> getWarningForCard(@Param("regionId") Long regionId,
                                           @Param("regionParentId") Long regionParentId,
                                           @Param("type")Integer type,
                                           @Param("name")String name);

    List<TRptIndustryScatterResp> selectIndustryUnitNum(@Param("regionId") Long regionId,
                                                        @Param("regionParentId") Long regionParentId);

    TRptStressThreeResp selectThreeStress(@Param("regionId") Long regionId,
                                          @Param("regionParentId") Long regionParentId);

    TRptCompanyViewResp selectUnitReport(@Param("regionId") Long regionId,
                                         @Param("regionParentId") Long regionParentId);

    List<RiskListResp> selectRiskList(@Param("regionId") Long regionId,
                                      @Param("regionParentId") Long regionParentId);
}
