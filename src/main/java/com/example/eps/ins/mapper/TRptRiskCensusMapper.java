package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.report.TRptRiskCensus;
import com.example.eps.ins.common.dto.report.resp.RiskListResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TRptRiskCensusMapper extends BaseMapper<TRptRiskCensus> {
  RiskListResp riskAll(
      @Param("regionId") Long nameId,
      @Param("year") Integer year,
      @Param("riskId") Long riskId,
      @Param("industryId") Long industryId);


  RiskListResp selectLiveTwo(@Param("regionId")List<Long> regions,
                             @Param("year") Integer year,
                             @Param("riskId") Long riskId,
                             @Param("industryId") Long industryId);
}
