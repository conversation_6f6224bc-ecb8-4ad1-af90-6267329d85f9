package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.enterprise.EnterpriseDeclaration;
import com.example.eps.ins.common.entity.system.MessageUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业信息申报表 Mapper
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
@Mapper
public interface EnterpriseDeclarationMapper extends BaseMapper<EnterpriseDeclaration> {
    @SqlParser(filter = true)
    List<MessageUser> selectAll(@Param("industryIds") List<Long> industryIds,
                                @Param("riskIds")List<Long> riskIds,
                                @Param("regionId")Long regionId,
                                @Param("messageId")Long id,
                                @Param("regionLive")Integer regionLive,
                                @Param("regionIds")List<Long> regionIds);

}
