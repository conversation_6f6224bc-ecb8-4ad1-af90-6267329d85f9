package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.enterprise.Enterprise;
import com.example.eps.ins.common.entity.enterprise.RiskContent;
import com.example.eps.ins.common.model.ReportVo;
import com.example.eps.ins.common.utils.HiddenExcelDate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业信息表 Mapper
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:42
 */
@Mapper
public interface EnterpriseMapper extends BaseMapper<Enterprise> {

    void updateHiddenStatus(@Param("creatorId") Long creatorId);
    void updatePlanStatus(@Param("creatorId")Long creatorId);
    List<Long> getRisks(@Param("enterpriseId")Long enterpriseId);

    List<Long> selectRisk(@Param("enterpriseId")Long enterpriseId);

    List<RiskContent> selectRiskContentAll(@Param("id")Long id,
                                           @Param("contentText") String contentText,
                                           @Param("pageNum")Integer pageNum,
                                           @Param("pageSize")Integer pageSize);
    List<Enterprise> getHiddenList(@Param("name") String name, @Param("riskTypeSearch")List<String> riskTypeSearch,
                                   @Param("noCommit")Integer noCommit, @Param("industry")Long industry,
                                   @Param("pageSize")Integer pageSize, @Param("pageNum")Integer pageNum,
                                   @Param("regionIds")List<Long> regionIds,@Param("sendId")Long sendId);
    List<Enterprise> getHiddenEnterprise(@Param("name") String name, @Param("riskTypeSearch")List<String> riskTypeSearch,
                                         @Param("noCommit")Integer noCommit, @Param("industry")Long industry,
                                         @Param("pageSize")Integer pageSize, @Param("pageNum")Integer pageNum,
                                         @Param("regionIds")List<Long> regionIds,@Param("sendId")Long sendId);
    List<HiddenExcelDate> getHiddenExeclList(@Param("name") String name, @Param("riskTypeSearch")List<String> riskTypeSearch,
                                             @Param("noCommit")Integer noCommit, @Param("industry")Long industry,
                                             @Param("pageSize")Integer pageSize, @Param("pageNum")Integer pageNum,
                                             @Param("regionIds")List<Long> regionIds,@Param("sendId")Long sendId);

    List<Long> selectReport(@Param("tableName") String tableName,
                            @Param("startTime")String startTime,
                            @Param("endTime")String endTime,
                            @Param("column")String column);

    List<ReportVo> getReport();

    Integer selectNoSee(@Param("creatorId")Long creatorId,@Param("sendId")Long sendId);
    List<Enterprise> enterpriseList(@Param("name") String name,
                                    @Param("regionIds")List<Long> selectSon,
                                    @Param("pageSize")Integer pageSize,
                                    @Param("pageNum")Integer pageNum);
    List<HiddenExcelDate> enterpriseExcelList(@Param("name") String name,
                                              @Param("regionIds")List<Long> selectSon);
}
