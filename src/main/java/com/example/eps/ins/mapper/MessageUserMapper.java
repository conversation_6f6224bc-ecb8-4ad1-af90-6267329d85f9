package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.system.MessageUser;
import com.example.eps.ins.common.utils.MessageExcelDate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: <消息用户关联表Mapper接口>
 * @Author: utopia
 * @CreateDate: 2021-11-09
 */
@Mapper
public interface MessageUserMapper extends BaseMapper<MessageUser> {

    void insertAll(@Param("messageUsers")List<MessageUser> messageUsers);

    @SqlParser(filter = true)
    List<MessageExcelDate> getMessageExcel(@Param("id")Long id,
                                           @Param("type")Integer type,
                                           @Param("riskStatus")Integer riskStatus,
                                           @Param("enterpriseName")String enterpriseName);
}
