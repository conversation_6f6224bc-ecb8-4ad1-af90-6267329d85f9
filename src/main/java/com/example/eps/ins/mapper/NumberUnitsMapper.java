package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.report.TRptCompanyView;
import com.example.eps.ins.common.entity.report.TRptIndustryScatter;
import com.example.eps.ins.common.entity.report.TRptStressThree;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NumberUnitsMapper extends BaseMapper<TRptCompanyView> {
    Integer getBurst(String burst);

    TRptCompanyView companyView(@Param("year") Integer year,@Param("nameId") Long nameId);

    List<TRptIndustryScatter> getIndustry(@Param("year") Integer year, @Param("nameId") Long nameId);

    TRptStressThree getThreeStress(@Param("year") Integer year, @Param("nameId") Long nameId);
}
