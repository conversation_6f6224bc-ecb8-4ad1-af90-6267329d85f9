package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.system.Menu;
import com.example.eps.ins.common.entity.system.MenuEnterprise;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MenuMapper extends BaseMapper<Menu> {

    /**
     * 获取用户权限集
     *
     * @param username 用户名
     * @return 用户权限集
     */
    List<Menu> findUserPermissions(String username);

    /**
     * 获取用户菜单
     *
     * @param username 用户名
     * @return 用户菜单
     */
    List<Menu> findUserMenus(String username);

    /**
     * 获取企业端菜单
     *
     * @return 企业端菜单
     */
    List<MenuEnterprise> findEnterpriseMenus();
}