package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.enterprise.WarningForCard;
import com.example.eps.ins.common.entity.system.TDictionaries;
import com.example.eps.ins.common.utils.WarningForCardData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: Zhang<PERSON>yin
 * @Date: Created in 2022/11/10 13:58
 * @Version: 1.0
 */
@Mapper
public interface WarningForCardMapper extends BaseMapper<WarningForCard> {
    List<WarningForCard> selectAll(@Param("code")String code,
                                   @Param("userId")Long userId,
                                   @Param("pageNum")Integer pageNum, @Param("pageSize")Integer pageSize);


    List<TDictionaries> getCode();

    Boolean countWaringForCard(@Param("creatorId")Long creatorId,@Param("code")String code);

    void updateWaringForCard(@Param("creatorId")Long creatorId,@Param("code")String code);

    void updateStatus(@Param("id")Long id);

    List<WarningForCard> selectAllSystem(@Param("level") Integer level, @Param("code")String code,
                                   @Param("name")String name, @Param("sendId")Long sendId,
                                   @Param("regionId")Long regionId,
                                   @Param("pageNum")Integer pageNum, @Param("pageSize")Integer pageSize);

    List<WarningForCardData> selectAllExcel(@Param("level") Integer level, @Param("code")String code,
                                            @Param("name")String name, @Param("sendId")Long sendId,
                                            @Param("regionId")Long regionId);

}
