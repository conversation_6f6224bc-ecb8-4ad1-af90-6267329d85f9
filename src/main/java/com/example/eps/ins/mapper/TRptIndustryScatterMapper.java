package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.report.TRptIndustryScatter;
import com.example.eps.ins.common.dto.report.resp.TRptIndustryScatterResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TRptIndustryScatterMapper extends BaseMapper<TRptIndustryScatter> {
  List<TRptIndustryScatterResp> getCity(
      @Param("year") Integer year,
      @Param("regionId") Long nameId);

  List<TRptIndustryScatterResp> selectLiveTwo(@Param("regionId")List<Long> regions,@Param("year") Integer year);
}