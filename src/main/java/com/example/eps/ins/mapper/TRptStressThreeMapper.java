package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.report.TRptStressThree;
import com.example.eps.ins.common.dto.report.resp.TRptStressThreeResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface TRptStressThreeMapper extends BaseMapper<TRptStressThree> {
    /**
     *  获取区县数据
     * @param year 年份
     * @param nameId 地区id
     * @return TRptStressThreeResp
     */
    TRptStressThreeResp getCity(@Param("year") Integer year, @Param("regionId") Long nameId);

    TRptStressThreeResp selectLiveTwo(@Param("regionId") List<Long> regions,@Param("year") Integer year);
}
