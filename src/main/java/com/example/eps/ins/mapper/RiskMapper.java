package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.eps.ins.common.entity.enterprise.Risk;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 风险类型表 Mapper
 *
 * <AUTHOR>
 * @date 2021-10-20 13:49:41
 */
@Mapper
public interface RiskMapper extends BaseMapper<Risk> {
    List<Long> selectByParent(Long id);

    String getRiskName(Long id);
}
