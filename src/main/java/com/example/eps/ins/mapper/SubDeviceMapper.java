package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.eps.ins.common.dto.riskPush.CameraResponse;
import com.example.eps.ins.common.entity.device.SubDevice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 子设备信息 Mapper
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Mapper
public interface SubDeviceMapper extends BaseMapper<SubDevice> {

    /**
     * 根据父设备ID查询子设备列表
     *
     * @param parentDeviceId 父设备ID
     * @return 子设备列表
     */
    List<SubDevice> selectByParentDeviceId(@Param("parentDeviceId") Long parentDeviceId);

    /**
     * 根据外部子设备ID查询子设备
     *
     * @param externalSubDeviceId 外部子设备ID
     * @return 子设备信息
     */
    SubDevice selectByExternalSubDeviceId(@Param("externalSubDeviceId") Long externalSubDeviceId);

    /**
     * 根据父设备ID和序列号查询子设备
     *
     * @param parentDeviceId 父设备ID
     * @param serial 子设备序列号
     * @return 子设备信息
     */
    SubDevice selectByParentDeviceIdAndSerial(@Param("parentDeviceId") Long parentDeviceId, @Param("serial") String serial);

    /**
     * 分页查询子设备列表
     *
     * @param page 分页对象
     * @param subDevice 查询条件
     * @return 子设备分页列表
     */
    IPage<SubDevice> selectSubDevicePage(Page<SubDevice> page, @Param("subDevice") SubDevice subDevice);

    /**
     * 批量插入子设备信息
     *
     * @param subDeviceList 子设备列表
     * @return 插入数量
     */
    int batchInsert(@Param("subDeviceList") List<SubDevice> subDeviceList);

    /**
     * 根据同步状态查询子设备列表
     *
     * @param syncStatus 同步状态
     * @return 子设备列表
     */
    List<SubDevice> selectBySyncStatus(@Param("syncStatus") Integer syncStatus);

    /**
     * 根据父设备ID删除所有子设备
     *
     * @param parentDeviceId 父设备ID
     * @return 删除数量
     */
    int deleteByParentDeviceId(@Param("parentDeviceId") Long parentDeviceId);

    /**
     * 根据父设备ID和状态查询子设备列表
     *
     * @param parentDeviceId 父设备ID
     * @param status 设备状态
     * @return 子设备列表
     */
    List<SubDevice> selectByParentDeviceIdAndStatus(@Param("parentDeviceId") Long parentDeviceId, @Param("status") String status);

    /**
     * 根据企业ID查询相机列表（带企业和设备信息）
     *
     * @param enterpriseId 企业ID
     * @return 相机列表
     */
    List<CameraResponse> selectCamerasByEnterpriseId(@Param("enterpriseId") Long enterpriseId);
}
