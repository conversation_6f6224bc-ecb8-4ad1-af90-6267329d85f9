package com.example.eps.ins.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.eps.ins.common.dto.riskPush.AlarmQueryRequest;
import com.example.eps.ins.common.dto.riskPush.AlarmQueryResponse;
import com.example.eps.ins.common.entity.safety.SafetyMonitorEvent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 安全监控事件 Mapper
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Mapper
public interface SafetyMonitorEventMapper extends BaseMapper<SafetyMonitorEvent> {

    /**
     * 根据外部事件ID查询事件
     */
    SafetyMonitorEvent selectByExternalEventId(@Param("externalEventId") String externalEventId);

    /**
     * 查询指定时间范围内的事件
     */
    List<SafetyMonitorEvent> selectByTimeRange(@Param("startTime") Date startTime, 
                                               @Param("endTime") Date endTime);

    /**
     * 根据设备ID查询事件
     */
    List<SafetyMonitorEvent> selectByDeviceId(@Param("deviceId") Long deviceId);

    /**
     * 根据事件类型查询事件
     */
    List<SafetyMonitorEvent> selectByEventType(@Param("eventType") Integer eventType);

    /**
     * 查询告警事件
     */
    List<SafetyMonitorEvent> selectAlarmEvents(@Param("haveAlarm") Integer haveAlarm);

    /**
     * 统计事件数量按事件类型
     */
    List<Map<String, Object>> countEventsByType(@Param("startTime") Date startTime, 
                                                @Param("endTime") Date endTime);

    /**
     * 统计告警事件数量按设备
     */
    List<Map<String, Object>> countAlarmEventsByDevice(@Param("startTime") Date startTime, 
                                                       @Param("endTime") Date endTime);

    /**
     * 查询未处理的事件
     */
    List<SafetyMonitorEvent> selectUnprocessedEvents();

    /**
     * 批量更新处理状态
     */
    int updateProcessStatus(@Param("ids") List<Long> ids, 
                           @Param("processStatus") Integer processStatus,
                           @Param("processResult") String processResult);

    /**
     * 查询最近的告警事件
     */
    List<SafetyMonitorEvent> selectRecentAlarmEvents(@Param("limit") Integer limit);

    /**
     * 根据设备序列号和时间范围查询事件
     */
    List<SafetyMonitorEvent> selectByDeviceSerialAndTimeRange(@Param("deviceSerial") String deviceSerial,
                                                              @Param("startTime") Date startTime,
                                                              @Param("endTime") Date endTime);

    /**
     * 分页查询预警列表（带企业信息）
     */
    IPage<AlarmQueryResponse> selectAlarmPageWithEnterpriseInfo(Page<AlarmQueryResponse> page,
                                                                @Param("request") AlarmQueryRequest request);
}
