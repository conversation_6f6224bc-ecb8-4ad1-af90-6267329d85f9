{"content": {"type": "warn", "params": {"CityEventListObject": {"CityEventObject": [{"Confidence": 0.0, "Census": 0.0, "Unit": 3, "Id": "12117515985017309e475b1a95724f3bb6d9e3ef59d1df34", "BigUrl": "kaabCK13PEt3BGdLi0/SfMiyIZL68khszebJTbvMblop1RoZM68tV6x+Wt+2uiX/DjPH7b4m29", "ImgUrl": "/2025-07-04_11/10822791cb73fb9-c5a1-421a-9918-3b4e397b8907.jpg", "CreateTime": "20250704110821", "MarkTime": "20250704110820", "Serialnumber": "175159724128697", "CameraId": "1", "SlaveIP": "************", "EventSource": 3, "StartTime": "20250704110818", "EndTime": "20250704110820", "HaveAlarm": 1, "EventType": 121, "SubList": {"TrainPlatRaw": [{"name": "human_detection", "id": "c52dd3eb", "version": "20240806", "val": {"annotations": [{"class_id": 0, "score": 0.8899, "bbox": [755, 277, 145, 461]}, {"class_id": 0, "score": 0.7164, "bbox": [760, 144, 115, 248]}, {"class_id": 0, "score": 0.5121, "bbox": [133, 342, 101, 181]}, {"class_id": 0, "score": 0.4931, "bbox": [721, 137, 63, 257]}]}}], "LocationInfo": [{"LeftTopX": 755, "LeftTopY": 277, "RightBtmX": 900, "RightBtmY": 738, "FrameIdx": 913, "StartFrameIdx": 0, "EndFrameIdx": 0, "ReID": 0, "udrId": "175159798194818", "udrName": "u533au57df0", "IsFMOD": 2, "TrainPlatRaw": [{}]}], "ThresholdDetail": {}, "ImageInfo": {"Width": 1920.0, "Height": 1080.0, "Downsample": 1.0}, "Config": {"confidence": 0.0, "event": "121", "extInfo": {}, "optParam": {"durationMinTime": 2, "timeInterval": 10, "enableFMODReview": true}, "sec": 1.0, "timesSetting": {"enable": 1, "times": [{"startTime": "00:00:00", "endTime": "23:59:59", "weeks": [1, 2, 3, 4, 5, 6, 7]}]}, "tripwiresSetting": [], "udrSetting": {"udrVertices": [{"verticesNum": 4, "verticesType": 0, "udrId": "175159798194818", "udrName": "u533au57df0", "vertices": [0.336, 0.3799, 0.1556, 0.969, 0.9933, 0.9808, 0.9652, 0.2032]}], "isInterested": true, "udrNum": 1}}}, "ExtInfo": {"DataInfo": {"OptParam": {"FmodConfig": {"fmodReviewCrypt": "fYsryhpWU@n1xEs#hP3YBLw3NqYzgq4ZDdPbmrV9jViMBUL9bYTReQ@8d!6EaPcyWYCOsPwwMJu#iVk4mcnysHbAUn2Oz7NdncpXRGeLZ0nhLaL9VW0L@kJ4Q7knELA!KNa016ck0mytTW8AgP3NmLHfHTo8Bt@ea6CjMnZzjjErm7RsXNRPlAygzUaCKwLRZZBOr6lbOeXginT2Qp54UL6Zsj3mhqnaWpcertmNXjOvoTPVXN!M5CG0QRjJZ6dQTZtOr6lbOg@mbOhLgAuuLcTK8fVg4igd72P8GOx1EbNG"}}, "AlarmTimes": 2, "HaveAlarm": 1, "Compare": 4, "ObjectName": "u672au6234u5b89u5168u5e3d"}, "Custom": {"taskType": 1, "serialnumber": "175159724128697", "cameraId": "1", "appName": "SVIAS-APP"}}, "SecondaryResult": 2}]}}}, "deviceId": 1940708612932972546, "deviceType": "GATEWAY", "ip": "************", "serial": "5CF838712B91", "status": "ONLINE"}