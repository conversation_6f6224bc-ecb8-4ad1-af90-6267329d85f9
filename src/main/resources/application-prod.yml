spring:
    redis:
        open: false  # 是否开启redis缓存  true开启   false关闭
        database: 0
        host: **************
        port: 6379
        password: redis@cache   # 密码（默认为空）
        timeout: 6000ms  # 连接超时时长（毫秒）
        jedis:
            pool:
                max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
                max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
                max-idle: 10      # 连接池中的最大空闲连接
                min-idle: 5       # 连接池中的最小空闲连接
    datasource:
        dynamic:
            primary: master # 必须指定主数据源
            strict: false # 非严格模式
            datasource:
                master:
                    username: root
                    password: epS!@#_20230416
                    driver-class-name: com.mysql.cj.jdbc.Driver
                    url: **********************************************************************************************************************************************************************
    main:
        web-application-type: servlet
        allow-bean-definition-overriding: true

eps:
  frontUrl: 'http://localhost:9527'
  server:
    system:
  cloud:
    security:
      enable: true
      anon-uris: /actuator/**,/auth/captcha,/smsCaptcha,/social/**,/v2/api-docs,/v2/api-docs-ext,/login,/resource/**,/validateCode,/enterpriseUser/check/**,/risk/push/**,/device/**
  gateway:
    enhance: false
    jwt:
      secret: 123456
      expiration: 36000
  sms:
    account: M1520O
    password: eps@123
    register:
      template: 【安全云平台】验证码 %s 有效期5分钟，勿泄漏给他人，如非本人操作请忽略。
    login:
      template: 【安全云平台】验证码 %s 有效期5分钟，勿泄漏给他人，如非本人操作请忽略。
    reset-password:
      template: 【安全云平台】验证码 %s 有效期5分钟，勿泄漏给他人，如非本人操作请忽略。
    url: http://sms.meitangyun.com:8001/std/sms
    switch: true

user:
  password: 123456
fdfs:
  so-timeout: 1500
  connect-timeout: 600
  thumb-image:
    width: 150
    height: 150
  tracker-list:
    - **************:22122
  webServerUrl: http://**************:9000/
