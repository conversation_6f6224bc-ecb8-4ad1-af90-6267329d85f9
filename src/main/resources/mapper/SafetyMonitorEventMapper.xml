<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.SafetyMonitorEventMapper">

    <!-- 根据外部事件ID查询事件 -->
    <select id="selectByExternalEventId" resultType="com.example.eps.ins.common.entity.safety.SafetyMonitorEvent">
        SELECT * FROM t_safety_monitor_event 
        WHERE external_event_id = #{externalEventId} 
        AND is_deleted = 0
    </select>

    <!-- 查询指定时间范围内的事件 -->
    <select id="selectByTimeRange" resultType="com.example.eps.ins.common.entity.safety.SafetyMonitorEvent">
        SELECT * FROM t_safety_monitor_event 
        WHERE create_time BETWEEN #{startTime} AND #{endTime}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据设备ID查询事件 -->
    <select id="selectByDeviceId" resultType="com.example.eps.ins.common.entity.safety.SafetyMonitorEvent">
        SELECT * FROM t_safety_monitor_event 
        WHERE device_id = #{deviceId} 
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据事件类型查询事件 -->
    <select id="selectByEventType" resultType="com.example.eps.ins.common.entity.safety.SafetyMonitorEvent">
        SELECT * FROM t_safety_monitor_event 
        WHERE event_type = #{eventType} 
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询告警事件 -->
    <select id="selectAlarmEvents" resultType="com.example.eps.ins.common.entity.safety.SafetyMonitorEvent">
        SELECT * FROM t_safety_monitor_event 
        WHERE have_alarm = #{haveAlarm} 
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 统计事件数量按事件类型 -->
    <select id="countEventsByType" resultType="java.util.Map">
        SELECT 
            event_type,
            event_type_name,
            COUNT(*) as event_count,
            SUM(CASE WHEN have_alarm = 1 THEN 1 ELSE 0 END) as alarm_count
        FROM t_safety_monitor_event 
        WHERE is_deleted = 0
        <if test="startTime != null and endTime != null">
            AND create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY event_type, event_type_name
        ORDER BY event_count DESC
    </select>

    <!-- 统计告警事件数量按设备 -->
    <select id="countAlarmEventsByDevice" resultType="java.util.Map">
        SELECT 
            device_id,
            device_serial,
            device_ip,
            device_type,
            COUNT(*) as alarm_count,
            MAX(create_time) as last_alarm_time
        FROM t_safety_monitor_event 
        WHERE have_alarm = 1 
        AND is_deleted = 0
        <if test="startTime != null and endTime != null">
            AND create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY device_id, device_serial, device_ip, device_type
        ORDER BY alarm_count DESC
    </select>

    <!-- 查询未处理的事件 -->
    <select id="selectUnprocessedEvents" resultType="com.example.eps.ins.common.entity.safety.SafetyMonitorEvent">
        SELECT * FROM t_safety_monitor_event 
        WHERE process_status = 0 
        AND is_deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 批量更新处理状态 -->
    <update id="updateProcessStatus">
        UPDATE t_safety_monitor_event 
        SET process_status = #{processStatus},
            process_result = #{processResult},
            process_time = NOW(),
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 查询最近的告警事件 -->
    <select id="selectRecentAlarmEvents" resultType="com.example.eps.ins.common.entity.safety.SafetyMonitorEvent">
        SELECT * FROM t_safety_monitor_event 
        WHERE have_alarm = 1 
        AND is_deleted = 0
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据设备序列号和时间范围查询事件 -->
    <select id="selectByDeviceSerialAndTimeRange" resultType="com.example.eps.ins.common.entity.safety.SafetyMonitorEvent">
        SELECT * FROM t_safety_monitor_event
        WHERE device_serial = #{deviceSerial}
        <if test="startTime != null and endTime != null">
            AND create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询预警列表（带企业信息） -->
    <select id="selectAlarmPageWithEnterpriseInfo" resultType="com.example.eps.ins.common.dto.riskPush.AlarmQueryResponse">
        SELECT
            sme.id,
            sme.external_event_id as externalEventId,
            sme.device_id as deviceId,
            sme.device_serial as deviceSerial,
            sme.device_ip as deviceIp,
            sme.device_type as deviceType,
            sme.device_status as deviceStatus,
            sme.camera_id as cameraId,
            sme.camera_name as cameraName,
            sme.event_type as eventType,
            sme.event_type_name as eventTypeName,
            sme.event_source as eventSource,
            sme.confidence,
            sme.detection_score as detectionScore,
            sme.have_alarm as haveAlarm,
            CASE
                WHEN sme.have_alarm = 0 THEN '无告警'
                WHEN sme.have_alarm = 1 THEN '有告警'
                ELSE '未知'
            END as haveAlarmDesc,
            sme.alarm_times as alarmTimes,
            sme.object_name as objectName,
            sme.bounding_box as boundingBox,
            sme.img_url as imgUrl,
            sme.big_url as bigUrl,
            sme.img_width as imgWidth,
            sme.img_height as imgHeight,
            sme.start_time as startTime,
            sme.end_time as endTime,
            sme.mark_time as markTime,
            sme.process_status as processStatus,
            CASE
                WHEN sme.process_status = 0 THEN '未处理'
                WHEN sme.process_status = 1 THEN '已处理'
                WHEN sme.process_status = 2 THEN '处理失败'
                ELSE '未知状态'
            END as processStatusDesc,
            sme.process_time as processTime,
            sme.process_result as processResult,
            sme.alarm_status as alarmStatus,
            CASE
                WHEN sme.alarm_status = 0 THEN '未告警'
                WHEN sme.alarm_status = 1 THEN '已告警'
                ELSE '未知状态'
            END as alarmStatusDesc,
            sme.alarm_time as alarmTime,
            sme.create_time as createTime,
            sme.remark,
            edb.enterprise_id as enterpriseId,
            e.name as enterpriseName
        FROM t_safety_monitor_event sme
        LEFT JOIN t_enterprise_device_binding edb ON sme.device_id = edb.external_device_id AND edb.binding_status = 1 AND edb.is_deleted = 0
        LEFT JOIN t_enterprise e ON edb.enterprise_id = e.id
        WHERE sme.is_deleted = 0

        <!-- 企业名称模糊查询 -->
        <if test="request.enterpriseName != null and request.enterpriseName != ''">
            AND e.name LIKE CONCAT('%', #{request.enterpriseName}, '%')
        </if>

        <!-- 事件类型查询（支持多个） -->
        <if test="request.eventTypes != null and request.eventTypes.size() > 0">
            AND sme.event_type IN
            <foreach collection="request.eventTypes" item="eventType" open="(" separator="," close=")">
                #{eventType}
            </foreach>
        </if>

        <!-- 告警时间范围查询 -->
        <if test="request.alarmStartTime != null and request.alarmEndTime != null">
            AND sme.create_time BETWEEN #{request.alarmStartTime} AND #{request.alarmEndTime}
        </if>
        <if test="request.alarmStartTime != null and request.alarmEndTime == null">
            AND sme.create_time &gt;= #{request.alarmStartTime}
        </if>
        <if test="request.alarmStartTime == null and request.alarmEndTime != null">
            AND sme.create_time &lt;= #{request.alarmEndTime}
        </if>

        <!-- 子设备查询（摄像头ID或设备序列号） -->
        <if test="request.subDevice != null and request.subDevice != ''">
            AND (sme.camera_id LIKE CONCAT('%', #{request.subDevice}, '%')
                 OR sme.device_serial LIKE CONCAT('%', #{request.subDevice}, '%')
                 OR sme.camera_name LIKE CONCAT('%', #{request.subDevice}, '%'))
        </if>

        <!-- 设备序列号查询 -->
        <if test="request.deviceSerial != null and request.deviceSerial != ''">
            AND sme.device_serial LIKE CONCAT('%', #{request.deviceSerial}, '%')
        </if>

        <!-- 摄像头ID查询 -->
        <if test="request.cameraId != null and request.cameraId != ''">
            AND sme.camera_id LIKE CONCAT('%', #{request.cameraId}, '%')
        </if>

        <!-- 摄像头名称查询 -->
        <if test="request.cameraName != null and request.cameraName != ''">
            AND sme.camera_name LIKE CONCAT('%', #{request.cameraName}, '%')
        </if>

        <!-- 是否有告警 -->
        <if test="request.haveAlarm != null">
            AND sme.have_alarm = #{request.haveAlarm}
        </if>

        <!-- 处理状态 -->
        <if test="request.processStatus != null">
            AND sme.process_status = #{request.processStatus}
        </if>

        <!-- 告警状态 -->
        <if test="request.alarmStatus != null">
            AND sme.alarm_status = #{request.alarmStatus}
        </if>

        <!-- 设备IP地址 -->
        <if test="request.deviceIp != null and request.deviceIp != ''">
            AND sme.device_ip LIKE CONCAT('%', #{request.deviceIp}, '%')
        </if>

        <!-- 设备类型 -->
        <if test="request.deviceType != null and request.deviceType != ''">
            AND sme.device_type = #{request.deviceType}
        </if>

        <!-- 置信度范围 -->
        <if test="request.minConfidence != null">
            AND sme.confidence &gt;= #{request.minConfidence}
        </if>
        <if test="request.maxConfidence != null">
            AND sme.confidence &lt;= #{request.maxConfidence}
        </if>

        ORDER BY sme.create_time DESC
    </select>

</mapper>
