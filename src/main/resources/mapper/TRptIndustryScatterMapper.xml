<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.TRptIndustryScatterMapper">
    <select id="getCity" resultType="com.example.eps.ins.common.dto.report.resp.TRptIndustryScatterResp">
        SELECT IFNULL(SUM(unit_num), 0) as unitNum,
        industry_name as industryName
        FROM t_rpt_industry_scatter
        <where>
            <if test="year != null and year != 1">
                AND YEAR = #{year}
            </if>
            <if test="regionId != null and regionId != 1">
                and region_id=#{regionId}
            </if>
        </where>
        GROUP BY industry_id
    </select>
    <select id="selectLiveTwo" resultType="com.example.eps.ins.common.dto.report.resp.TRptIndustryScatterResp">
        SELECT IFNULL(SUM(unit_num), 0) as unitNum,
        industry_name as industryName
        FROM t_rpt_industry_scatter
        where
        region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
            <if test="year != null and year != 1">
                AND YEAR = #{year}
            </if>
        GROUP BY industry_id
    </select>
</mapper>