<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.EnterpriseDeviceBindingMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.eps.ins.common.entity.device.EnterpriseDeviceBinding">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="enterprise_id" property="enterpriseId" jdbcType="BIGINT"/>
        <result column="external_device_id" property="externalDeviceId" jdbcType="BIGINT"/>
        <result column="device_id" property="deviceId" jdbcType="BIGINT"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="device_model" property="deviceModel" jdbcType="VARCHAR"/>
        <result column="binding_status" property="bindingStatus" jdbcType="INTEGER"/>
        <result column="binding_time" property="bindingTime" jdbcType="TIMESTAMP"/>
        <result column="unbinding_time" property="unbindingTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="creator_id" property="creatorId" jdbcType="BIGINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 详细信息结果映射 -->
    <resultMap id="DetailResultMap" type="com.example.eps.ins.common.dto.device.DeviceBindingResponse">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="enterprise_id" property="enterpriseId" jdbcType="BIGINT"/>
        <result column="enterprise_name" property="enterpriseName" jdbcType="VARCHAR"/>
        <result column="external_device_id" property="externalDeviceId" jdbcType="BIGINT"/>
        <result column="device_id" property="deviceId" jdbcType="BIGINT"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="device_model" property="deviceModel" jdbcType="VARCHAR"/>
        <result column="binding_status" property="bindingStatus" jdbcType="INTEGER"/>
        <result column="binding_status_desc" property="bindingStatusDesc" jdbcType="VARCHAR"/>
        <result column="binding_time" property="bindingTime" jdbcType="TIMESTAMP"/>
        <result column="unbinding_time" property="unbindingTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="device_serial" property="deviceSerial" jdbcType="VARCHAR"/>
        <result column="device_status" property="deviceStatus" jdbcType="VARCHAR"/>
        <result column="device_ip" property="deviceIp" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, enterprise_id, external_device_id, device_id, device_name, device_model,
        binding_status, binding_time, unbinding_time, create_time, update_time,
        creator_id, remark, is_deleted
    </sql>

    <!-- 根据企业ID和外部设备ID查询绑定关系 -->
    <select id="selectByEnterpriseIdAndExternalDeviceId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_enterprise_device_binding
        WHERE enterprise_id = #{enterpriseId}
        AND external_device_id = #{externalDeviceId}
        AND is_deleted = 0
    </select>

    <!-- 根据企业ID查询绑定的设备列表 -->
    <select id="selectByEnterpriseId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_enterprise_device_binding
        WHERE enterprise_id = #{enterpriseId}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据外部设备ID查询绑定关系列表 -->
    <select id="selectByExternalDeviceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_enterprise_device_binding
        WHERE external_device_id = #{externalDeviceId}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据设备ID查询绑定关系列表 -->
    <select id="selectByDeviceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_enterprise_device_binding
        WHERE device_id = #{deviceId}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询绑定关系列表（带详细信息） -->
    <select id="selectBindingPageWithDetails" resultMap="DetailResultMap">
        SELECT 
            edb.id,
            edb.enterprise_id,
            e.name as enterprise_name,
            edb.external_device_id,
            edb.device_id,
            edb.device_name,
            edb.device_model,
            edb.binding_status,
            CASE 
                WHEN edb.binding_status = 0 THEN '未绑定'
                WHEN edb.binding_status = 1 THEN '已绑定'
                WHEN edb.binding_status = 2 THEN '已解绑'
                ELSE '未知状态'
            END as binding_status_desc,
            edb.binding_time,
            edb.unbinding_time,
            edb.create_time,
            edb.remark,
            d.serial as device_serial,
            d.status as device_status,
            d.ip as device_ip
        FROM t_enterprise_device_binding edb
        LEFT JOIN t_enterprise e ON edb.enterprise_id = e.id
        LEFT JOIN t_device d ON edb.device_id = d.id
        WHERE edb.is_deleted = 0
        <if test="binding.enterpriseId != null">
            AND edb.enterprise_id = #{binding.enterpriseId}
        </if>
        <if test="binding.externalDeviceId != null">
            AND edb.external_device_id = #{binding.externalDeviceId}
        </if>
        <if test="binding.deviceId != null">
            AND edb.device_id = #{binding.deviceId}
        </if>
        <if test="binding.bindingStatus != null">
            AND edb.binding_status = #{binding.bindingStatus}
        </if>
        <if test="binding.deviceName != null and binding.deviceName != ''">
            AND edb.device_name LIKE CONCAT('%', #{binding.deviceName}, '%')
        </if>
        <if test="binding.deviceModel != null and binding.deviceModel != ''">
            AND edb.device_model LIKE CONCAT('%', #{binding.deviceModel}, '%')
        </if>
        ORDER BY edb.create_time DESC
    </select>

    <!-- 根据企业ID和绑定状态查询绑定关系列表 -->
    <select id="selectByEnterpriseIdAndStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_enterprise_device_binding
        WHERE enterprise_id = #{enterpriseId}
        AND binding_status = #{bindingStatus}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据绑定状态查询绑定关系列表 -->
    <select id="selectByBindingStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_enterprise_device_binding
        WHERE binding_status = #{bindingStatus}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入绑定关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_enterprise_device_binding (
        enterprise_id, external_device_id, device_id, device_name, device_model,
        binding_status, binding_time, create_time, update_time, creator_id, remark, is_deleted
        ) VALUES
        <foreach collection="bindingList" item="binding" separator=",">
            (
            #{binding.enterpriseId},
            #{binding.externalDeviceId},
            #{binding.deviceId},
            #{binding.deviceName},
            #{binding.deviceModel},
            #{binding.bindingStatus},
            #{binding.bindingTime},
            #{binding.createTime},
            #{binding.updateTime},
            #{binding.creatorId},
            #{binding.remark},
            0
            )
        </foreach>
    </insert>

    <!-- 更新绑定状态 -->
    <update id="updateBindingStatus">
        UPDATE t_enterprise_device_binding
        SET binding_status = #{bindingStatus},
            update_time = NOW()
            <if test="unbindingTime != null">
                , unbinding_time = #{unbindingTime}
            </if>
        WHERE id = #{id}
        AND is_deleted = 0
    </update>

    <!-- 根据企业ID统计绑定设备数量 -->
    <select id="countByEnterpriseId" parameterType="java.lang.Long" resultType="int">
        SELECT COUNT(*)
        FROM t_enterprise_device_binding
        WHERE enterprise_id = #{enterpriseId}
        AND binding_status = 1
        AND is_deleted = 0
    </select>

    <!-- 根据企业ID和绑定状态统计设备数量 -->
    <select id="countByEnterpriseIdAndStatus" resultType="int">
        SELECT COUNT(*)
        FROM t_enterprise_device_binding
        WHERE enterprise_id = #{enterpriseId}
        AND binding_status = #{bindingStatus}
        AND is_deleted = 0
    </select>

</mapper>
