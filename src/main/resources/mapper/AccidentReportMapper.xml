<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.AccidentReportMapper">
    <select id="getOneCityAll" resultType="com.example.eps.ins.common.dto.report.resp.AccidentAllResp">
        SELECT IFNULL(SUM(accident_num), 0) AS accidentNum,
        IFNULL(SUM(death), 0) AS death,
        IFNULL(SUM(loss), 0) AS money,
        IFNULL(SUM(injured), 0) AS injured
        FROM t_rpt_accident_census
        WHERE year = #{year}
        <if test="nameId != null and nameId != 1">
            and region_id = #{nameId}
        </if>

    </select>
    <select id="getIndustryAccident" resultType="com.example.eps.ins.common.dto.report.resp.IndustryAccidentResp">
        select industry_name,
               SUM(number) as number
        from t_rpt_industry_accident
        WHERE industry_name = #{industryName}
          and year = #{year}
    </select>
    <select id="getOneIndustryAccident" resultType="com.example.eps.ins.common.dto.report.resp.IndustryAccidentResp">
        select industry_name,
        SUM(number) as number
        from t_rpt_industry_accident
        WHERE `year` = #{year}
        <if test="nameId != null and nameId != 1">
            and region_id = #{nameId}
        </if>
        GROUP BY industry_name
    </select>
    <select id="getAccidentSort" resultType="com.example.eps.ins.common.dto.report.resp.AccidentSortResp">
        SELECT
        (SELECT SUM(accident_num) FROM t_rpt_accident_type
        where year=#{year}
        <if test="nameId != null and nameId != 1">
            and region_id=#{nameId}
        </if>
        ) as number,
        accident,
        sum( accident_num ) as accidentNum
        FROM
        t_rpt_accident_type
        WHERE year=#{year}
        <if test="nameId != null and nameId != 1">
            and region_id=#{nameId}
        </if>
        GROUP BY
        accident ORDER BY accidentNum desc
    </select>
    <select id="getAreaNum" resultType="com.example.eps.ins.common.dto.report.resp.AreaResp">
        SELECT area_num as accidentNumber,
               name     as name
        FROM t_rpt_area_accident
        where year = #{year}
    </select>
</mapper>