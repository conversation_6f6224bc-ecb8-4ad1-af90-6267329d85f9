<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.EnterpriseDeclarationMapper">

    <select id="selectAll" resultType="com.example.eps.ins.common.entity.system.MessageUser">
        Select
        a.creator_id as userId,
        #{messageId} as messageId,
        0 as `status`,
        2 as type,
        now() as createTime,
        #{regionId} as regionId,
        a.`name` as enterpriseName,
        if(a.risk_type IS NULL, 0, 1) as riskStatus,
        b.mobile as enterprisePhone
        from t_enterprise as a ,
        t_enterprise_user as b
        where
        a.creator_id=b.user_id
        and (a.region_county_id in
        <foreach collection="regionIds" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        or a.region_town_id in
        <foreach collection="regionIds" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>)
        <if test="industryIds != null and industryIds.size()>0">
            and a.industry_id in
            <foreach collection="industryIds" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="riskIds != null and riskIds.size()>0">
            and(
            <foreach collection="riskIds" item="item">
                <if test="riskIds.get(0) == item">
                    a.risk_type like concat( '%',#{item},'%')
                </if>
                <if test="riskIds.get(0) != item">
                    or a.risk_type like concat( '%',#{item},'%')
                </if>
            </foreach>
            )
        </if>
        <if test="regionLive != null and regionLive == 2">
            <if test="regionId != null">
                and a.region_county_id=#{regionId}
            </if>
        </if>
        <if test="regionLive != null and regionLive == 3">
            <if test="regionId != null">
                and a.region_town_id=#{regionId}
            </if>
        </if>
    </select>
</mapper>
