<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.RegionMapper">
    <select id="selectRegion" resultType="com.example.eps.ins.common.dto.report.resp.RegionResp">
        SELECT id   as regionId,
               name as name
        FROM t_region
    </select>
    <select id="selectSonId" resultType="java.lang.Long">
        SELECT id
        FROM t_region
        where parent_id = #{id}
    </select>

    <select id="selectSon" resultType="java.lang.Long">
        select  id  from t_region where  parent_id=#{currentUser}
    </select>
</mapper>
