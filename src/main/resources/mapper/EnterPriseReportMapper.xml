<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.EnterPriseReportMapper">
    <select id="getRiskEnterPrise" resultType="com.example.eps.ins.common.dto.report.resp.CakeResp">
        <!--        select (select ifnull(count(*),0) from t_basic_risk as a-->
        <!--        where-->
        <!--        (risk_i1 IS NOT NULL-->
        <!--        or risk_i2 IS NOT NULL-->
        <!--        or risk_i3 IS NOT NULL-->
        <!--        or risk_i4 IS NOT NULL-->
        <!--        or risk_i5 IS NOT NULL-->
        <!--        or risk_i6 IS NOT NULL)-->
        <!--        <if test="regionId != null">-->
        <!--            and a.region_id=#{regionId}-->
        <!--        </if>-->
        <!--        <if test="regionParentId != null">-->
        <!--            and a.region_parent_id=#{regionParentId}-->
        <!--        </if>) as numberOne,-->
        <!--        (SELECT-->
        <!--        count(*)-->
        <!--        FROM-->
        <!--        `t_basic_risk` as a-->
        <!--        where-->
        <!--        1=1-->
        <!--        <if test="regionId != null">-->
        <!--            and a.region_id=#{regionId}-->
        <!--        </if>-->
        <!--        <if test="regionParentId != null">-->
        <!--            and a.region_parent_id=#{regionParentId}-->
        <!--        </if>-->
        <!--        ) as numberTwo-->
        SELECT
        COUNT( CASE WHEN risk_type IS NOT NULL AND risk_type &lt;&gt;'' THEN 1 ELSE NULL END ) as numberOne,
        COUNT( CASE WHEN risk_type IS NULL OR risk_type = '' THEN 1 ELSE NULL END ) as numberTwo
        FROM
        `t_enterprise`
        <where>
            <if test="regionId != null">
                and region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and region_town_id=#{regionParentId}
            </if>
        </where>
    </select>
    <select id="getRisk" resultType="com.example.eps.ins.common.dto.report.resp.RiskListResp">
        SELECT '涉燃爆粉尘' AS `risk`,
        IFNULL(SUM(risk_i1), 0) AS num
        FROM t_basic_risk as a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
        UNION
        SELECT '涉高温熔融' AS `risk`,
        IFNULL(SUM(risk_i2), 0) AS num
        FROM t_basic_risk as a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
        UNION
        SELECT '涉冶金煤气' AS `risk`,
        IFNULL(SUM(risk_i3), 0) AS num
        FROM t_basic_risk as a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
        UNION
        SELECT '涉氨制冷' AS `risk`,
        IFNULL(SUM(risk_i4), 0) AS num
        FROM t_basic_risk as a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
        UNION
        SELECT '有限空间' AS `risk`,
        IFNULL(SUM(risk_i5), 0) AS num
        FROM t_basic_risk as a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
        UNION
        SELECT '其他燃爆毒危化品使用' AS `risk`,
        IFNULL(SUM(risk_i6), 0) AS num
        FROM t_basic_risk as a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
    </select>
    <select id="getUpAndDown" resultType="com.example.eps.ins.common.dto.report.resp.CakeResp">
        <!--        SELECT-->
        <!--        ifnull(count(*)- ifnull(SUM( scale_status ),0),0) AS numberOne,-->
        <!--        ifnull(SUM( scale_status ),0) as numberTwo-->
        <!--        FROM-->
        <!--        t_basic_risk as a-->
        <!--        where-->
        <!--        1=1-->
        <!--        <if test="regionId != null">-->
        <!--            and a.region_id=#{regionId}-->
        <!--        </if>-->
        <!--        <if test="regionParentId != null">-->
        <!--            and a.region_parent_id=#{regionParentId}-->
        <!--        </if>-->
        SELECT
        COUNT( CASE WHEN scale_status = 1 THEN 1 ELSE NULL END ) numberTwo,
        COUNT( CASE WHEN scale_status &lt;&gt;1 OR scale_status is NULL THEN 1 ELSE NULL END ) numberOne
        FROM
        `t_enterprise`
        <where>
            <if test="regionId != null">
                and a.region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and a.region_town_id=#{regionParentId}
            </if>
        </where>
    </select>
    <select id="getThreeNow" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseProportionResp">
        SELECT
        count(*) AS `count`,
        count(*)- count( current_evaluation ) AS numNo,
        count( current_evaluation ) AS numOk
        FROM
        t_basic_risk AS a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
    </select>
    <select id="getDangerStandard" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseProportionResp">
        SELECT
        count(*) AS `count`,
        count(*)- count( safety_bck ) AS numNo,
        count( safety_bck ) AS numOk
        FROM
        t_basic_risk AS a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
    </select>
    <select id="getPlanReport" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseProportionResp">
        SELECT
        IFNULL(SUM(`aggregate`),0) as `count`,
        IFNULL(SUM(complete),0) as numOk,
        IFNULL(SUM(`aggregate`),0)-IFNULL(SUM(complete),0) as numNo
        FROM
        t_emergency_plan_report AS a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
    </select>
    <select id="getHiddenReport" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseProportionResp">
        SELECT
        IFNULL(SUM(`aggregate`),0) as `count`,
        IFNULL(SUM(complete),0) as numOk,
        IFNULL(SUM(`aggregate`),0)-IFNULL(SUM(complete),0) as numNo
        FROM
        t_hidden_danger_report AS a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
    </select>
    <select id="getDrillReport" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseHistogramResp">
        SELECT
        `month` as `month`,
        IFNULL( SUM( number ), 0 ) AS `num`
        FROM
        `t_emergency_drill_report` as a
        WHERE
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="year != null and year != ''">
            and a.year=#{year}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
        GROUP BY
        `month`
        ORDER BY `month`
    </select>
    <select id="getListReport" resultType="com.example.eps.ins.common.dto.report.resp.HistogramResp">
        SELECT
        `month` as `month`,
        IFNULL( SUM( number ), 0 ) AS `num` ,
        IFNULL( SUM( complete ), 0 ) AS `complete`
        FROM
        `t_hidden_danger_list_report` as a
        WHERE
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="year != null and year != ''">
            and a.year=#{year}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
        GROUP BY
        `month`
        ORDER BY `month`
    </select>
    <select id="getMaintainReport" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseHistogramResp">
        SELECT
        `month` as `month`,
        IFNULL( SUM( number ), 0 ) AS `num`
        FROM
        `t_equipment_maintain_report` as a
        WHERE
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="year != null and year != ''">
            and a.year=#{year}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
        GROUP BY
        `month`
        ORDER BY `month`
    </select>
    <select id="getTrainReport" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseHistogramResp">
        SELECT
        `month` as `month`,
        IFNULL( SUM( number ), 0 ) AS `num`
        FROM
        `t_train_education_report` as a
        WHERE
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="year != null and year != ''">
            and a.year=#{year}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
        GROUP BY
        `month`
        ORDER BY `month`
    </select>
    <select id="getOrderCard" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseProportionResp">
        SELECT
        count(*) as `count`,
        count(`order_and_card`) as numOk,
        count(*)-count(`order_and_card`) as numNo
        FROM
        t_basic_risk AS a
        where
        1=1
        <if test="regionId != null">
            and a.region_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_parent_id=#{regionParentId}
        </if>
    </select>
    <select id="getWarningForCard" resultType="com.example.eps.ins.common.entity.enterprise.WarningForCard">
        SELECT
        id as id,
        enterprise_id as enterpriseId,
        name as `name`,
        title as title,
        time as `time`,
        remarkOne as remarkOne,
        remarkTwo as remarkTwo,
        type as type,
        region_town_id as regionTownId,
        region_county_id as regionCountyId,
        status as `status`
        FROM
        t_warning_for_card AS a
        where
        1=1
        <if test="regionId != null">
            and a.region_town_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and a.region_county_id=#{regionParentId}
        </if>
        <if test="type != null">
            and a.type=#{type}
        </if>
        <if test="name != null and name != ''">
            and a.name like concat('%',#{name},'%')
        </if>
    </select>

    <select id="selectIndustryUnitNum" resultType="com.example.eps.ins.common.dto.report.resp.TRptIndustryScatterResp">
        SELECT
        COUNT(CASE WHEN b.id IS NOT NULL THEN 1 ELSE NULL END) AS unitNum,
        a.`name` AS industryName
        FROM
        t_industry AS a
        LEFT JOIN `t_enterprise` AS b
        ON a.id = b.industry_id
        <if test="regionId != null">
            and b.region_county_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and b.region_town_id=#{regionParentId}
        </if>
        GROUP BY
        a.id, a.`name`
    </select>

    <select id="selectThreeStress" resultType="com.example.eps.ins.common.dto.report.resp.TRptStressThreeResp">
        SELECT
            COUNT( CASE WHEN key_enterprise = 1 THEN 1 ELSE NULL END ) as steel,
            COUNT( CASE WHEN key_enterprise = 2 THEN 1 ELSE NULL END ) as aluminium,
            COUNT( CASE WHEN key_enterprise = 3 THEN 1 ELSE NULL END ) as explosionRelated,
            COUNT(CASE WHEN key_enterprise  in (1,2,3) THEN 1 ELSE NULL END) as number
        FROM
            `t_enterprise`
        <where>
            <if test="regionId != null">
                and region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and region_town_id=#{regionParentId}
            </if>
        </where>
    </select>

    <select id="selectUnitReport" resultType="com.example.eps.ins.common.dto.report.resp.TRptCompanyViewResp">

        SELECT
            COUNT(*) AS unitNum,
            COUNT( CASE WHEN people_number = 1 THEN 1 ELSE NULL END ) AS big,
            COUNT( CASE WHEN people_number = 2 THEN 1 ELSE NULL END ) AS smaller,
            COUNT( CASE WHEN people_number = 3 THEN 1 ELSE NULL END ) AS thirtyToFifty,
            COUNT( CASE WHEN people_number = 4 THEN 1 ELSE NULL END ) AS fiftyToHundred,
            COUNT( CASE WHEN people_number = 5 THEN 1 ELSE NULL END ) AS tenPeople
        FROM
            t_enterprise
        <where>
            <if test="regionId != null">
                and region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and region_town_id=#{regionParentId}
            </if>
        </where>
    </select>

    <select id="selectRiskList" resultType="com.example.eps.ins.common.dto.report.resp.RiskListResp">
        SELECT
        t.risk,
        CASE
        WHEN t.risk = '涉燃爆粉尘' THEN
        ( SELECT COUNT( CASE WHEN risk_type LIKE '%i1%' THEN 1 END ) FROM t_enterprise
        <where>
            <if test="regionId != null">
                and region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and region_town_id=#{regionParentId}
            </if>
        </where>
        )
        WHEN t.risk = '涉高温熔融' THEN
        ( SELECT COUNT( CASE WHEN risk_type LIKE '%i2%' THEN 1 END ) FROM t_enterprise
        <where>
            <if test="regionId != null">
                and region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and region_town_id=#{regionParentId}
            </if>
        </where>
        )
        WHEN t.risk = '涉冶金煤气' THEN
        ( SELECT COUNT( CASE WHEN risk_type LIKE '%i3%' THEN 1 END ) FROM t_enterprise
        <where>
            <if test="regionId != null">
                and region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and region_town_id=#{regionParentId}
            </if>
        </where>
        )
        WHEN t.risk = '涉氨制冷' THEN
        ( SELECT COUNT( CASE WHEN risk_type LIKE '%i4%' THEN 1 END ) FROM t_enterprise
        <where>
            <if test="regionId != null">
                and region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and region_town_id=#{regionParentId}
            </if>
        </where>
        )
        WHEN t.risk = '有限空间' THEN
        ( SELECT COUNT( CASE WHEN risk_type LIKE '%i5%' THEN 1 END ) FROM t_enterprise
        <where>
            <if test="regionId != null">
                and region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and region_town_id=#{regionParentId}
            </if>
        </where>
        )
        WHEN t.risk = '其他燃爆毒危化品使用' THEN
        ( SELECT COUNT( CASE WHEN risk_type LIKE '%i6%' THEN 1 END ) FROM t_enterprise
        <where>
            <if test="regionId != null">
                and region_county_id=#{regionId}
            </if>
            <if test="regionParentId != null">
                and region_town_id=#{regionParentId}
            </if>
        </where>
        )
        END AS
        num
        FROM
        ( SELECT '涉燃爆粉尘' AS risk UNION SELECT '涉高温熔融' UNION SELECT '涉冶金煤气' UNION SELECT '涉氨制冷' UNION
        SELECT '有限空间' UNION SELECT '其他燃爆毒危化品使用') t
    </select>
</mapper>