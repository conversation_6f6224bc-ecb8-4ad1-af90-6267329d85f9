<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.SubDeviceMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.eps.ins.common.entity.device.SubDevice">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="parent_device_id" property="parentDeviceId" jdbcType="BIGINT"/>
        <result column="external_sub_device_id" property="externalSubDeviceId" jdbcType="BIGINT"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="serial" property="serial" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="device_type" property="deviceType" jdbcType="VARCHAR"/>
        <result column="ip" property="ip" jdbcType="VARCHAR"/>
        <result column="sync_status" property="syncStatus" jdbcType="INTEGER"/>
        <result column="last_sync_time" property="lastSyncTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, parent_device_id, external_sub_device_id, device_name, serial, status, device_type, ip,
        sync_status, last_sync_time, create_time, update_time, remark, is_deleted
    </sql>

    <!-- 根据父设备ID查询子设备列表 -->
    <select id="selectByParentDeviceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sub_device
        WHERE parent_device_id = #{parentDeviceId}
        AND is_deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据外部子设备ID查询子设备 -->
    <select id="selectByExternalSubDeviceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sub_device
        WHERE external_sub_device_id = #{externalSubDeviceId}
        AND is_deleted = 0
    </select>

    <!-- 根据父设备ID和序列号查询子设备 -->
    <select id="selectByParentDeviceIdAndSerial" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sub_device
        WHERE parent_device_id = #{parentDeviceId}
        AND serial = #{serial}
        AND is_deleted = 0
    </select>

    <!-- 分页查询子设备列表 -->
    <select id="selectSubDevicePage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sub_device
        WHERE is_deleted = 0
        <if test="subDevice.parentDeviceId != null">
            AND parent_device_id = #{subDevice.parentDeviceId}
        </if>
        <if test="subDevice.deviceName != null and subDevice.deviceName != ''">
            AND device_name LIKE CONCAT('%', #{subDevice.deviceName}, '%')
        </if>
        <if test="subDevice.serial != null and subDevice.serial != ''">
            AND serial LIKE CONCAT('%', #{subDevice.serial}, '%')
        </if>
        <if test="subDevice.status != null and subDevice.status != ''">
            AND status = #{subDevice.status}
        </if>
        <if test="subDevice.deviceType != null and subDevice.deviceType != ''">
            AND device_type = #{subDevice.deviceType}
        </if>
        <if test="subDevice.syncStatus != null">
            AND sync_status = #{subDevice.syncStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入子设备信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_sub_device (
        parent_device_id, external_sub_device_id, device_name, serial, status, device_type, ip,
        sync_status, last_sync_time, create_time, update_time, remark, is_deleted
        ) VALUES
        <foreach collection="subDeviceList" item="subDevice" separator=",">
            (
            #{subDevice.parentDeviceId},
            #{subDevice.externalSubDeviceId},
            #{subDevice.deviceName},
            #{subDevice.serial},
            #{subDevice.status},
            #{subDevice.deviceType},
            #{subDevice.ip},
            #{subDevice.syncStatus},
            #{subDevice.lastSyncTime},
            #{subDevice.createTime},
            #{subDevice.updateTime},
            #{subDevice.remark},
            0
            )
        </foreach>
    </insert>

    <!-- 根据同步状态查询子设备列表 -->
    <select id="selectBySyncStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sub_device
        WHERE sync_status = #{syncStatus}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据父设备ID删除所有子设备 -->
    <update id="deleteByParentDeviceId" parameterType="java.lang.Long">
        UPDATE t_sub_device
        SET is_deleted = 1, update_time = NOW()
        WHERE parent_device_id = #{parentDeviceId}
        AND is_deleted = 0
    </update>

    <!-- 根据父设备ID和状态查询子设备列表 -->
    <select id="selectByParentDeviceIdAndStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sub_device
        WHERE parent_device_id = #{parentDeviceId}
        AND status = #{status}
        AND is_deleted = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据企业ID查询相机列表（带企业和设备信息） -->
    <select id="selectCamerasByEnterpriseId" resultType="com.example.eps.ins.common.dto.riskPush.CameraResponse">
        SELECT
            sd.id,
            sd.external_sub_device_id as externalSubDeviceId,
            sd.parent_device_id as parentDeviceId,
            edb.enterprise_id as enterpriseId,
            e.name as enterpriseName,
            d.device_name as deviceName,
            d.serial as deviceSerial,
            d.ip as deviceIp,
            d.device_type as deviceType,
            d.status as deviceStatus,
            sd.serial as cameraId,
            sd.device_name as cameraName,
            sd.status as cameraStatus,
            sd.device_type as cameraType,
            sd.ip as cameraIp,
            sd.sync_status as syncStatus,
            CASE
                WHEN sd.sync_status = 0 THEN '未同步'
                WHEN sd.sync_status = 1 THEN '已同步'
                ELSE '未知状态'
            END as syncStatusDesc,
            sd.last_sync_time as lastSyncTime,
            sd.create_time as createTime,
            sd.update_time as updateTime,
            sd.remark,
            edb.binding_status as bindingStatus,
            CASE
                WHEN edb.binding_status = 0 THEN '未绑定'
                WHEN edb.binding_status = 1 THEN '已绑定'
                WHEN edb.binding_status = 2 THEN '已解绑'
                ELSE '未知状态'
            END as bindingStatusDesc,
            edb.binding_time as bindingTime
        FROM t_sub_device sd
        INNER JOIN t_device d ON sd.parent_device_id = d.id
        INNER JOIN t_enterprise_device_binding edb ON d.id = edb.device_id
            AND edb.binding_status = 1 AND edb.is_deleted = 0
        LEFT JOIN t_enterprise e ON edb.enterprise_id = e.id
        WHERE edb.enterprise_id = #{enterpriseId}
        AND sd.is_deleted = 0
        AND d.is_deleted = 0
        ORDER BY sd.create_time DESC
    </select>

</mapper>
