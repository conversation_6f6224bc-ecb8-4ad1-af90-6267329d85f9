<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.RiskMapper">
    <select id="selectByParent" resultType="java.lang.Long">
        SELECT  id from t_risk
        where parent_id=#{id}
    </select>
    <select id="getRiskName" resultType="java.lang.String">
        select name from t_risk where id=#{id}
    </select>
</mapper>
