<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.NumberUnitsMapper">

    <select id="getBurst" resultType="java.lang.Integer">
        SELECT  count(DISTINCT id) from  t_enterprise_risk as a
            LEFT JOIN t_enterprise as c ON a.enterprise_id=c.id
        WHERE a.risk_id = (SELECT id FROM t_risk as a  WHERE a.`code`=#{burst})
    </select>
    <select id="companyView" resultType="com.example.eps.ins.common.entity.report.TRptCompanyView">
        select * from t_rpt_company_view
        where region_id=#{nameId}
          and  year=#{year}
    </select>
    <select id="getIndustry" resultType="com.example.eps.ins.common.entity.report.TRptIndustryScatter">
        select * from t_rpt_industry_scatter
        where region_id=#{nameId}
          and  year=#{year}
    </select>
    <select id="getThreeStress" resultType="com.example.eps.ins.common.entity.report.TRptStressThree">
        select * from t_rpt_stress_three
        where region_id=#{nameId}
          and  year=#{year}
    </select>

</mapper>