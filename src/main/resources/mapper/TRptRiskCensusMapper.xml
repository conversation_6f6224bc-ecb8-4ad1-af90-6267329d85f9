<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.TRptRiskCensusMapper">
    <select id="riskAll" resultType="com.example.eps.ins.common.dto.report.resp.RiskListResp">
        SELECT ifnull(SUM(risk_num), 0) as num,
        risk as risk
        FROM t_rpt_risk_census
        where risk_id = #{riskId}
        <if test="year != null and year != 1">
            and year = #{year}
        </if>
        <if test="regionId != null and regionId != 1">
            and region_id=#{regionId}
        </if>
        <if test="industryId != null and industryId != 0">
            and industry_id=#{industryId}
        </if>

    </select>
    <select id="selectLiveTwo" resultType="com.example.eps.ins.common.dto.report.resp.RiskListResp">
        SELECT ifnull(SUM(risk_num), 0) as num,
        risk as risk
        FROM t_rpt_risk_census
        where risk_id = #{riskId}
        and region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="year != null and year != 1">
            and year = #{year}
        </if>
        <if test="industryId != null and industryId != 0">
            and industry_id=#{industryId}
        </if>
    </select>
</mapper>