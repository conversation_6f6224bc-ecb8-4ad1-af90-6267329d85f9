<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.WarningForCardMapper">
    <update id="updateWaringForCard">
        update t_warning_for_card
        set status = 1
        where create_id = #{creatorId}
          and code = #{code}
    </update>

    <select id="selectAll" resultType="com.example.eps.ins.common.entity.enterprise.WarningForCard">
        select a.*,
        datediff(now(),a.time) as riskTime,
        b.address as address,
        c.name as regionName
        from t_warning_for_card as a,
        t_enterprise as b,
        t_region as c
        where a.enterprise_id=b.id
        and c.id=a.region_town_id
          and a.status=0
        <if test="userId != null">
            and a.create_id=#{userId}
        </if>
        <if test="code != null">
            and code = #{code}
        </if>
        order by time desc
        <if test="pageNum != null and pageSize != null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>
    <select id="getCode" resultType="com.example.eps.ins.common.entity.system.TDictionaries">
        select *
        from t_dictionaries
        where `status` = 1
    </select>
    <select id="countWaringForCard" resultType="java.lang.Boolean">
        SELECT
            CASE
            count(*)
            WHEN 0 THEN
            FALSE ELSE TRUE
            END
        from t_warning_for_card
        where create_id = #{creatorId}
          and code = #{code}
    </select>

    <update id="updateStatus">
        update t_warning_for_card set status =2 where id=#{id}
    </update>

    <select id="selectAllSystem" resultType="com.example.eps.ins.common.entity.enterprise.WarningForCard">
        select a.*,
        datediff(now(),a.time) as riskTime,
        b.address as address,
        c.name as regionName,
        (SELECT count(*)
        FROM t_message_send_one as c
        WHERE c.receive_user = a.create_id
        AND c.send_user = #{sendId}
        and status = 0) as noSee
        from t_warning_for_card as a,
        t_enterprise as b,
        t_region as c
        where a.enterprise_id=b.id
        and c.id=a.region_town_id
        and a.status = 0
        <if test="level == 2">
            and b.region_county_id=#{regionId}
        </if>
        <if test="level == 3">
            and b.region_town_id=#{regionId}
        </if>
        <if test="code != null and code != ''">
            and code = #{code}
        </if>
        <if test="name != null and name !=''">
            and b.name like concat('%',#{name},'%')
        </if>
        order by time desc
        <if test="pageNum != null and pageSize != null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

    <select id="selectAllExcel" resultType="com.example.eps.ins.common.utils.WarningForCardData">
        select a.*,
        b.address as address,
        c.name as regionName,
        (SELECT count(*)
        FROM t_message_send_one as c
        WHERE c.receive_user = a.create_id
        AND c.send_user = #{sendId}
        and status = 0) as noSee
        from t_warning_for_card as a,
        t_enterprise as b,
        t_region as c
        where a.enterprise_id=b.id
        and c.id=a.region_town_id
        <if test="level == 2">
            and region_county_id=#{regionId}
        </if>
        <if test="level == 3">
            and region_town_id=#{regionId}
        </if>
        <if test="code != null">
            and code = #{code}
        </if>
        <if test="name != null and name !=''">
            and name like concat('%',#{name},'%')
        </if>
        order by time desc
    </select>
</mapper>
