<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.UserMapper">
    <select id="findByName" parameterType="string" resultType="com.example.eps.ins.common.entity.system.SystemUser">
        SELECT
            re.dept,
            u.user_id userId,
            u.username,
            u.mobile,
            u.password,
            u.status,
            u.region_id,
            u.nick_name,
            u.create_time createTime,
            u.last_login_time lastLoginTime,
            u.modify_time modifyTime,
            GROUP_CONCAT(r.role_id) roleId,
            GROUP_CONCAT(r.ROLE_NAME) roleName
        FROM
            t_user u
                LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id)
                LEFT JOIN t_role r ON r.role_id = ur.role_id
                LEFT JOIN t_region re ON re.id = u.region_id
        WHERE  u.username = #{username}
        group by u.username,u.user_id,u.mobile,u.password, u.status,u.region_id,u.create_time,u.last_login_time,u.modify_time
    </select>

    <select id="findUserDataPermissions" parameterType="long" resultType="com.example.eps.ins.common.entity.system.UserDataPermission">
        select user_id userId, dept_id deptId from t_user_data_permission
        where user_id = #{userId}
    </select>

    <select id="findUserDetailPage" parameterType="systemUser" resultType="com.example.eps.ins.common.entity.system.SystemUser">
        SELECT
        u.USER_ID,
        u.NICK_NAME,
        u.USERNAME,
        u.ID_NO,
        u.MOBILE,
        (SELECT
        GROUP_CONCAT(r.ROLE_NAME) AS roleName
        FROM
        t_user_role ur,
        t_role r
        WHERE
        ur.ROLE_ID = r.ROLE_ID
        AND ur.USER_ID = u.USER_ID
        GROUP BY ur.USER_ID) AS roleName,
        u.REGION_ID,
        region.DEPT AS dept
        FROM
        t_user u,t_region region
        WHERE region.id = u.REGION_ID
        <if test="user.username != null and user.username != ''">
            AND u.USERNAME like CONCAT('%',#{user.username},'%')
        </if>
        <if test="user.nickName != null and user.nickName != ''">
            AND u.NICK_NAME like CONCAT('%',#{user.nickName},'%')
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            AND u.MOBILE like CONCAT('%',#{user.mobile},'%')
        </if>
        <if test="user.status != null">
            AND u.status = #{user.status}
        </if>
        <if test="user.regionId != null">
            u.region_id = #{user.regionId}
        </if>
    </select>

    <select id="findUserDetail" parameterType="systemUser" resultType="com.example.eps.ins.common.entity.system.SystemUser">
        SELECT
        u.user_id userId,
        u.username,
        u.password,
        u.mobile,
        u.status,
        u.create_time createTime,
        u.last_login_time lastLoginTime,
        u.modify_time modifyTime,
        GROUP_CONCAT(r.role_id) roleId,
        GROUP_CONCAT(r.ROLE_NAME) roleName
        FROM
        t_user u
        LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id)
        LEFT JOIN t_role r ON r.role_id = ur.role_id
        WHERE 1 = 1
        <if test="user.username != null and user.username != ''">
            AND u.username = #{user.username}
        </if>
        <if test="user.status != null and user.status != ''">
            AND u.status = #{user.status}
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            AND u.mobile = #{user.mobile}
        </if>
        group by u.username,u.user_id,u.mobile,u.status,u.create_time,u.last_login_time,u.modify_time
    </select>
    <select id="selectMessage" resultType="com.example.eps.ins.common.entity.system.MessageUser">
        select
        USER_ID as userId,
        #{messageId} as messageId,
        0 as `status`,
        1 as type,
        now() as createTime,
        #{regionId} as regionId,
        concat(b.name,'-',a.NICK_NAME) as enterpriseName,
        a.MOBILE   as enterprisePhone,
        null as riskStatus
        from t_user as a ,t_region as b
        where a.REGION_ID=b.id
        <if test="regionIds != null and regionIds.size()>0">
            and a.REGION_ID in
            <foreach collection="regionIds" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
