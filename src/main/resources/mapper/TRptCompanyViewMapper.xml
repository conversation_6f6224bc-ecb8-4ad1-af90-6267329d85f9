<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.TRptCompanyViewMapper">
    <select id="selectByParentId" resultType="com.example.eps.ins.common.dto.report.resp.TRptCompanyViewResp">
        SELECT IFNULL(SUM(enterprise), 0) as enterprise,
        IFNULL(SUM(smaller), 0) as smaller,
        IFNULL(SUM(big), 0) as big,
        IFNULL(SUM(individual), 0) as individual,
        IFNULL(SUM(unit_num), 0) as unitNum,
        IFNULL(SUM(fifty_to_hundred), 0) as fiftyToHundred,
        IFNULL(SUM(thirty_to_fifty), 0) as thirtyToFifty,
        IFNULL(SUM(ten_people), 0) as tenPeople
        FROM `t_rpt_company_view`
        <where>
            <if test="year != null and year != 1">
                and year=#{year}
            </if>
            <if test="regionId != null and regionId != 1">
                and region_id=#{regionId}
            </if>
        </where>
    </select>

    <select id="selectLiveTwo" resultType="com.example.eps.ins.common.dto.report.resp.TRptCompanyViewResp">
        SELECT IFNULL(SUM(enterprise), 0) as enterprise,
        IFNULL(SUM(smaller), 0) as smaller,
        IFNULL(SUM(big), 0) as big,
        IFNULL(SUM(individual), 0) as individual,
        IFNULL(SUM(unit_num), 0) as unitNum,
        IFNULL(SUM(fifty_to_hundred), 0) as fiftyToHundred,
        IFNULL(SUM(thirty_to_fifty), 0) as thirtyToFifty,
        IFNULL(SUM(ten_people), 0) as tenPeople
        FROM `t_rpt_company_view`
        where
        region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
        #{id}
        </foreach>
        <if test="year != null and year != 1">
                and year=#{year}
        </if>
    </select>
</mapper>