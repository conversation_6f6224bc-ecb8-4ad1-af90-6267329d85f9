<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.IndustryMapper">

    <select id="getNames" resultType="java.lang.String">
            SELECT
                b.`name`
            FROM
                `t_message_all` AS a,
                t_industry AS b
            WHERE
                a.industry_id like CONCAT('%',b.id,'%')
              AND a.id=#{id}
    </select>
</mapper>
