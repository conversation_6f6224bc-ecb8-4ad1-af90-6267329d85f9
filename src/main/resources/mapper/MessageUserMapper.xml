<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.MessageUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.eps.ins.common.entity.system.MessageUser">
        <id column="id" property="id"/>
        <result column="message_id" property="messageId"/>
        <result column="user_id" property="userId"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_id, user_id, status
    </sql>

    <insert id="insertAll">
        insert into t_message_user(id, message_id, user_id, status, type, create_time, region_id, enterprise_name,
        enterprise_phone, risk_status)
        VALUES
        <foreach collection="messageUsers" index="index" item="item" separator=",">
            (
            #{item.id},#{item.messageId},#{item.userId},#{item.status},#{item.type},#{item.createTime},#{item.regionId},#{item.enterpriseName},#{item.enterprisePhone},#{item.riskStatus}
            )
        </foreach>
    </insert>

    <select id="getMessageExcel" resultType="com.example.eps.ins.common.utils.MessageExcelDate">
        SELECT IF
        (type = 1, '管理员', '企业') AS `type`,
        enterprise_name AS `name`,
        enterprise_phone AS phone,
        CASE
        risk_status
        WHEN 0 THEN
        '否'
        WHEN 1 THEN
        '是'
        ELSE ''
        END as riskStatus
        from t_message_user
        where message_id = #{id}
        <if test="type != null">
            and type=#{type}
        </if>
        <if test="riskStatus != null">
            and risk_status=#{riskStatus}
        </if>
        <if test="enterpriseName != null and enterpriseName != ''">
            and enterprise_name like concat('%',#{enterpriseName},'%')
        </if>
    </select>
</mapper>
