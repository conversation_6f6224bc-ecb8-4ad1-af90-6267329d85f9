<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.TRptStressThreeMapper">
    <select id="getCity" resultType="com.example.eps.ins.common.dto.report.resp.TRptStressThreeResp">
        SELECT ifnull(SUM(number), 0) as number,
        ifnull(SUM(explosion_related), 0) as explosionRelated,
        ifnull(SUM(aluminium), 0) as aluminium,
        ifnull(SUM(steel), 0) as steel
        FROM t_rpt_stress_three
        <where>
            <if test="year != null and year != 1">
                and year = #{year}
            </if>
            <if test="regionId != null and regionId != 1">
                and region_id=#{regionId}
            </if>
        </where>
    </select>
    <select id="selectLiveTwo" resultType="com.example.eps.ins.common.dto.report.resp.TRptStressThreeResp">
        SELECT ifnull(SUM(number), 0) as number,
        ifnull(SUM(explosion_related), 0) as explosionRelated,
        ifnull(SUM(aluminium), 0) as aluminium,
        ifnull(SUM(steel), 0) as steel
        FROM t_rpt_stress_three
        where
        region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="year != null and year != 1">
            AND YEAR = #{year}
        </if>
    </select>
</mapper>