<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.RoleMapper">
    <resultMap id="roleMap" type="role">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="ROLE_ID" jdbcType="DECIMAL" property="roleId"/>
        <result column="ROLE_NAME" jdbcType="VARCHAR" property="roleName"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>

    <select id="findUserRole" resultMap="roleMap">
        SELECT
            r.*
        FROM
            t_role r
                LEFT JOIN t_user_role ur ON (r.role_id = ur.role_id)
                LEFT JOIN t_user u ON (u.user_id = ur.user_id)
        WHERE
            u.username = #{username}
    </select>

    <select id="findRolePage" parameterType="role" resultType="role">
        SELECT
        r.role_id roleId,
        r.role_name roleName,
        r.remark,
        r.create_time createTime,
        r.modify_time modifyTime,
        GROUP_CONCAT( rm.menu_id ) menuIds
        FROM
        t_role r
        LEFT JOIN t_role_menu rm ON ( r.role_id = rm.role_id )
        WHERE 1 = 1
        <if test="role.roleName != null and role.roleName != ''">
            AND r.role_name like CONCAT('%',#{role.roleName},'%')
        </if>
        GROUP BY
        r.role_id
    </select>

</mapper>