<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.EnterpriseMapper">

    <update id="updateHiddenStatus">
        update t_enterprise set hidden_status='1' where t_enterprise.creator_id=#{creatorId}
    </update>

    <update id="updatePlanStatus">
        update t_enterprise set plan_status='1' where t_enterprise.creator_id=#{creatorId}
    </update>
    <select id="getRisks" resultType="java.lang.Long">
        select risk_id from t_enterprise_risk where enterprise_id=#{enterpriseId}
    </select>
    <select id="selectRisk" resultType="java.lang.Long">
        select content_id from t_risk_content_user where enterprise_id=#{enterpriseId}
    </select>
    <select id="selectRiskContentAll" resultType="com.example.eps.ins.common.entity.enterprise.RiskContent">
        SELECT
            *
        FROM
            `t_risk_content_user` AS a
                LEFT JOIN t_risk_content AS b ON a.content_id = b.id
        WHERE
            a.enterprise_id = #{id}
        <if test="contentText != null and contentText != ''">
          and b.content_text LIKE CONCAT('%',#{contentText},'%')
        </if>
        order by create_time desc
        <if test="pageNum != null and pageSize != null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

    <select id="getHiddenList" resultType="com.example.eps.ins.common.entity.enterprise.Enterprise">
        select a.id,
        industry_id,
        region_county_id,
        nature,
        people_number,
        production_status,
        current_evaluation,
        safety_bck,
        safety_bck_level,
        safety_bck_time,
        safety_major_time,
        safety_post_time,
        emergency_plan_review,
        safety_insurance,
        a.name, address,
        social_code,
        safety_director_name,
        safety_director_phone,
        safety_full_number,
        safety_part_number,
        contact_name,
        contact_phone,
        legal_representative_name,
        legal_representative_job,
        legal_representative_phone,
        a.creator_id,
        a.create_time,
        a.modify_user_id,
        a.modify_time,
        key_enterprise,
        business_address,
        lng,
        lat,
        region_town_id,
        emergency_plan_record,
        risks,
        risk_type,
        order_and_card,
        current_evaluation_status,
        scale_status,
        plan_status,
        hidden_status,
        trade_type,
        county.name as county,
        town.name as town,
        `user`.mobile as phone,
        industry.name as industryName,
        (SELECT count(*) FROM t_message_send_one as c WHERE c.receive_user= a.creator_id AND c.send_user=#{sendId} and
        status=0) as noSee
        FROM
        t_enterprise as a,
        t_region as county,
        t_region as town,
        t_enterprise_user as `user`,
        t_industry as industry
        where
        a.region_county_id=county.id
        and a.region_town_id=town.id
        and `user`.user_id=a.creator_id
        and a.industry_id=industry.id
        <if test="name !=null and name != ''">
            and a.name like concat('%',#{name},'%')
        </if>
        <if test="riskTypeSearch !=null and riskTypeSearch.size>0">
            and (
            <foreach collection="riskTypeSearch" item="item">
                <if test="riskTypeSearch.get(0) == item">
                    a.risk_type like concat( '%',#{item},'%')
                </if>
                <if test="riskTypeSearch.get(0) != item">
                    or a.risk_type like concat( '%',#{item},'%')
                </if>
            </foreach>
            )
        </if>
        <if test="industry !=null and industry != ''">
            and a.industry_id=#{industry}
        </if>
        <if test="noCommit != null">
            and a.creator_id not IN (SELECT DISTINCT
            creator_id
            FROM
            (SELECT creator_id,max(create_time) as create_time FROM t_hidden_danger_list GROUP BY creator_id ) AS b
            WHERE DATE_ADD( b.create_time, INTERVAL 1 MONTH ) &gt; NOW())
        </if>
        <if test="regionIds != null and regionIds.size()>0">
            and a.region_town_id IN
            <foreach collection="regionIds" index="index" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="pageSize !=null and pageNum != null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

    <select id="getHiddenEnterprise" resultType="com.example.eps.ins.common.entity.enterprise.Enterprise">
        select a.creator_id,
        a.name
        FROM
        t_enterprise as a,
        t_region as county,
        t_region as town,
        t_enterprise_user as `user`,
        t_industry as industry
        where
        a.region_county_id=county.id
        and a.region_town_id=town.id
        and `user`.user_id=a.creator_id
        and a.industry_id=industry.id
        <if test="name !=null and name != ''">
            and a.name like concat('%',#{name},'%')
        </if>
        <if test="riskTypeSearch !=null and riskTypeSearch.size>0">
            and (
            <foreach collection="riskTypeSearch" item="item">
                <if test="riskTypeSearch.get(0) == item">
                    a.risk_type like concat( '%',#{item},'%')
                </if>
                <if test="riskTypeSearch.get(0) != item">
                    or a.risk_type like concat( '%',#{item},'%')
                </if>
            </foreach>
            )
        </if>
        <if test="industry !=null and industry != ''">
            and a.industry_id=#{industry}
        </if>
        <if test="noCommit != null">
            and a.creator_id not IN (SELECT DISTINCT
            creator_id
            FROM
            (SELECT creator_id,max(create_time) as create_time FROM t_hidden_danger_list GROUP BY creator_id ) AS b
            WHERE DATE_ADD( b.create_time, INTERVAL 1 MONTH ) &gt; NOW())
        </if>
        <if test="regionIds != null and regionIds.size()>0">
            and a.region_town_id IN
            <foreach collection="regionIds" index="index" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="pageSize !=null and pageNum != null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

    <select id="getHiddenExeclList" resultType="com.example.eps.ins.common.utils.HiddenExcelDate">
        select
        a.name as `name`,
        social_code as socialCode,
        county.name as county,
        town.name as town,
        `user`.mobile as phone,
        industry.name as industryName,
        (SELECT count(*) FROM t_message_send_one as c WHERE c.receive_user= a.creator_id AND status=0) as noSee
        FROM
        t_enterprise as a,
        t_region as county,
        t_region as town,
        t_enterprise_user as `user`,
        t_industry as industry
        where
        a.region_county_id=county.id
        and a.region_town_id=town.id
        and `user`.user_id=a.creator_id
        and a.industry_id=industry.id
        <if test="name !=null and name != ''">
            and a.name like concat('%',#{name},'%')
        </if>
        <if test="riskTypeSearch !=null and riskTypeSearch.size>0">
            and (
            <foreach collection="riskTypeSearch" item="item">
                <if test="riskTypeSearch.get(0) == item">
                    a.risk_type like concat( '%',#{item},'%')
                </if>
                <if test="riskTypeSearch.get(0) != item">
                    or a.risk_type like concat( '%',#{item},'%')
                </if>
            </foreach>
            )
        </if>
        <if test="industry !=null and industry != ''">
            and a.industry_id=#{industry}
        </if>
        <if test="noCommit != null">
            and a.creator_id not IN (SELECT DISTINCT
            creator_id
            FROM
            (SELECT creator_id,max(create_time) as create_time FROM t_hidden_danger_list GROUP BY creator_id ) AS b
            WHERE DATE_ADD( b.create_time, INTERVAL 1 MONTH ) &gt; NOW())
        </if>
        <if test="regionIds != null and regionIds.size()>0">
            and a.region_town_id IN
            <foreach collection="regionIds" index="index" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="pageSize !=null and pageNum != null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>

    <select id="selectReport" resultType="java.lang.Long">

        select distinct creator_id from ${tableName}
        <where>
            <if test="startTime != null and endTime != null">
                and ${column} between #{startTime} and #{endTime}
            </if>
        </where>
    </select>

    <select id="getReport" resultType="com.example.eps.ins.common.model.ReportVo">
        select code        as `code`,
               details     as `name`,
               details_two as `column`
        from t_dictionaries
        where details_one = 'report'
    </select>

    <select id="selectNoSee" resultType="java.lang.Integer">
        SELECT count(*)
        FROM t_message_send_one as c
        WHERE c.receive_user = #{creatorId}
          AND c.send_user = #{sendId}
          and status = 0
    </select>



    <select id="enterpriseExcelList" resultType="com.example.eps.ins.common.utils.HiddenExcelDate">
        select
        distinct  a.id,
        a.name as `name`,
        social_code as socialCode,
        county.name as county,
        town.name as town,
        `user`.mobile as phone,
        industry.name as industryName
        FROM
        t_enterprise as a,
        t_region as county,
        t_region as town,
        t_enterprise_user as `user`,
        t_industry as industry ,
        t_risk_content_user as trcu
        where
        a.region_county_id=county.id
        and a.region_town_id=town.id
        and `user`.user_id=a.creator_id
        and a.industry_id=industry.id
        and trcu.enterprise_id=a.id
        <if test="name != null and name != ''">
            and a.name like concat('%',#{name},'%')
        </if>
        <if test="regionIds != null and regionIds.size()>0">
            and a.region_town_id IN
            <foreach collection="regionIds" index="index" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="enterpriseList" resultType="com.example.eps.ins.common.entity.enterprise.Enterprise">
        select distinct a.id,
        industry_id,
        region_county_id,
        nature,
        people_number,
        production_status,
        current_evaluation,
        safety_bck,
        safety_bck_level,
        safety_bck_time,
        safety_major_time,
        safety_post_time,
        emergency_plan_review,
        safety_insurance,
        a.name, address,
        social_code,
        safety_director_name,
        safety_director_phone,
        safety_full_number,
        safety_part_number,
        contact_name,
        contact_phone,
        legal_representative_name,
        legal_representative_job,
        legal_representative_phone,
        a.creator_id,
        a.create_time,
        a.modify_user_id,
        a.modify_time,
        key_enterprise,
        business_address,
        lng,
        lat,
        region_town_id,
        emergency_plan_record,
        risks,
        risk_type,
        order_and_card,
        current_evaluation_status,
        scale_status,
        plan_status,
        hidden_status,
        trade_type,
        county.name as county,
        town.name as town,
        `user`.mobile as phone,
        industry.name as industryName
        FROM
        t_enterprise as a,
        t_region as county,
        t_region as town,
        t_enterprise_user as `user`,
        t_industry as industry ,
        t_risk_content_user as trcu
        where
        a.region_county_id=county.id
        and a.region_town_id=town.id
        and `user`.user_id=a.creator_id
        and a.industry_id=industry.id
        and trcu.enterprise_id=a.id
        <if test="name != null and name != ''">
            and a.name like concat('%',#{name},'%')
        </if>
        <if test="regionIds != null and regionIds.size()>0">
            and a.region_town_id IN
            <foreach collection="regionIds" index="index" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="pageSize !=null and pageNum != null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>
</mapper>
