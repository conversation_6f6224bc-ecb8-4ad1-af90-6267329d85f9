<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.HomePageMapper">
    <select id="selectByHomeAll" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseViewResp">
        SELECT
        IFNULL(SUM(unit_num), 0) as unitNum,
        IFNULL(SUM(enterprise), 0) as enterprise,
        IFNULL(SUM(individual), 0) as individual
        FROM
        `t_rpt_company_view`
        WHERE
         1=1
        <if test="regionId != null and regionId != 1">
            and region_id=#{regionId}
        </if>
    </select>

    <select id="getYears" resultType="java.lang.Integer">
        SELECT DISTINCT year
        FROM t_rpt_area_accident
        where 1=1
        <if test="regionId != null and regionId != 1">
            and region_id = #{regionId}
        </if>
        ORDER BY year

    </select>
    <select id="getAccidentYear" resultType="com.example.eps.ins.common.dto.report.resp.AccidentYearResp">
        SELECT SUM(area_num) as number, `year`
        FROM t_rpt_area_accident
        where year = #{year}
        <if test="regionId != null and regionId != 1">
            and region_id = #{regionId}
        </if>
    </select>
    <select id="riskAll" resultType="com.example.eps.ins.common.dto.report.resp.RiskListResp">
        SELECT ifnull(SUM(risk_num), 0) as num,
        risk as risk
        FROM t_rpt_risk_census
        where risk_id = #{riskId}
        <if test="regionId != null and regionId != 1">
            and region_id=#{regionId}
        </if>

    </select>
    <select id="getDataViewAll" resultType="com.example.eps.ins.common.dto.report.resp.EnforcementHomeResp">
        select COALESCE(sum(trdv.complete),0) as complete,
        COALESCE(sum(trdv.fine),0) as fine,
        COALESCE(sum(trdv.yes_check_list),0) as checkListNumber
        from t_rpt_data_view as trdv
        <if test="regionId != null and regionId != 1">
            where region_id = #{regionId}
        </if>
    </select>
    <select id="getRectifyAll" resultType="com.example.eps.ins.common.dto.report.resp.EnforcementHomeResp">
        select COALESCE(sum(trdv.rectify_number),0) as rectifyNumber
        from t_rpt_rectify_data_view as trdv
        <if test="regionId != null and regionId != 1">
            where region_id = #{regionId}
        </if>
    </select>
    <select id="getAccidentSort" resultType="com.example.eps.ins.common.dto.report.resp.AccidentSortResp">
        SELECT
        (SELECT SUM(accident_num) FROM t_rpt_accident_type
        <if test="regionId != null and regionId != 1">
            where and region_id=#{nameId}
        </if>
        ) as number,
        accident as accident,
        sum( accident_num ) as accidentNum
        FROM
        t_rpt_accident_type
        <if test="regionId != null and regionId != 1">
            WHERE and region_id=#{regionId}
        </if>
        GROUP BY
        accident ORDER BY accidentNum desc
    </select>
    <select id="getCity" resultType="com.example.eps.ins.common.dto.report.resp.TRptIndustryScatterResp">
        select industry_name,
        SUM(number) as unitNum
        from t_rpt_industry_accident
        WHERE 1=1
        <if test="regionId != null and regionId != 1">
            and region_id = #{regionId}
        </if>
        GROUP BY industry_name
    </select>
    <select id="getDataViewOrTwoLive" resultType="com.example.eps.ins.common.dto.report.resp.EnforcementHomeResp">
        select COALESCE(sum(trdv.complete),0) as complete,
        COALESCE(sum(trdv.fine),0) as fine,
        COALESCE(sum(trdv.check_list_number),0) as checkListNumber,
        COALESCE(sum(trdv.no_check_list),0) as noCheckList,
        COALESCE(sum(trdv.yes_check_list),0) as yesCheckList
        from t_rpt_data_view as trdv
        where region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getRectifyOrTwoLive" resultType="com.example.eps.ins.common.dto.report.resp.EnforcementHomeResp">
        select COALESCE(sum(trdv.no_rectify),0) as noRectify,
        COALESCE(sum(trdv.yes_rectify),0) as yesRectify,
        COALESCE(sum(trdv.rectify),0) as startRectify,
        COALESCE(sum(trdv.rectify_number),0) as rectifyNumber
        from t_rpt_rectify_data_view as trdv
        where region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="selectByHomeAllLiveTwo" resultType="com.example.eps.ins.common.dto.report.resp.EnterpriseViewResp">
<!--        SELECT IFNULL(SUM(unit_num), 0)   as unitNum,-->
<!--               IFNULL(SUM(enterprise), 0) as enterprise,-->
<!--               IFNULL(SUM(individual), 0) as individual-->
<!--        FROM `t_rpt_company_view`-->
<!--        where region_id in-->
<!--              (-->
<!--                  SELECT id-->
<!--                  FROM t_region-->
<!--                  WHERE parent_id = #{regionId})-->
        SELECT
        COUNT(*) AS unitNum,
        COUNT(CASE WHEN nature = 1 THEN 1 ELSE NULL END) AS enterprise,
        COUNT(CASE WHEN nature = 2 THEN 1 ELSE NULL END) AS individual
        FROM
        `t_enterprise`
        <where>
        <if test="regionId != null">
            and region_county_id=#{regionId}
        </if>
        <if test="regionParentId != null">
            and region_town_id=#{regionParentId}
        </if>
    </where>
    </select>
    <select id="riskAllTwo" resultType="com.example.eps.ins.common.dto.report.resp.RiskListResp">
        SELECT ifnull(SUM(risk_num), 0) as num,
        risk as risk
        FROM t_rpt_risk_census
        where risk_id = #{riskId}
        <if test="regionId != null and regionId != 1">
            AND region_id in (SELECT id FROM t_region WHERE parent_id=#{regionId})
        </if>

    </select>
</mapper>