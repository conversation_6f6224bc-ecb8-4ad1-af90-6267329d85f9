<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.MessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.example.eps.ins.common.entity.system.Message">
        <id column="id" property="id"/>
        <result column="type" property="type"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="attachment_path" property="attachmentPath"/>
        <result column="publish_time" property="publishTime"/>
        <result column="user_id" property="userId"/>
        <result column="publish_nick_name" property="publishNickName"/>
        <result column="status" property="status"/>
      <!--  <collection
                property="regions"
                javaType="list"
                ofType="com.example.eps.ins.common.entity.enterprise.Region"
                column="id" select="findRegions"
        >
        </collection>-->
    </resultMap>
    <resultMap id="regionMap" type="com.example.eps.ins.common.entity.enterprise.Region">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, title, content, attachment_path, publish_time, user_id, publish_nick_name, status
    </sql>

    <sql id="pageQuery">
        where 1=1
        and id in (select distinct message_id from t_message_region where 1=1
        <if test="null!=page.regionId and page.regionId!=''">
            and region_id = #{page.regionId}
        </if>
        )
        <if test="null!=page.type and page.type!=''">
            and type=#{page.type}
        </if>
        <if test="null!=page.title and page.title!=''">
            and title like concat('%',#{page.title},'%')
        </if>
        <if test="null!=page.publishTimeSlot and page.publishTimeSlot.size()==2">
            and publish_time between #{page.publishTimeSlot[0]} and #{page.publishTimeSlot[1]}
        </if>
    </sql>

    <select id="findMessage" resultMap="BaseResultMap" parameterType="long">
        select * from t_message where id=#{id}
    </select>

    <select id="findRegions" resultMap="regionMap" parameterType="long">
        select r.* from t_message_region mr,t_region r where mr.region_id = r.id and mr.message_id=#{id}
    </select>

    <select id="findByPage" resultMap="BaseResultMap"
            parameterType="com.example.eps.ins.common.bean.MessageRequest">
        select * from t_message
        <include refid="pageQuery"/>
        order by publish_time desc
        limit #{page.pageNum},#{page.pageSize}
    </select>

    <select id="count" resultType="long" parameterType="com.example.eps.ins.common.bean.MessageRequest">
        select count(1) from t_message
        <include refid="pageQuery"/>
    </select>

    <select id="findMessages" resultMap="BaseResultMap">
        SELECT t.* from t_message t,t_message_region mr
        where t.id=mr.message_id
        <if test="null!=regionId">
            and mr.region_id = #{regionId}
        </if>
        <if test="null!=type">
            and t.type = #{type}
        </if>
        order by publish_time desc
        limit 4
    </select>
</mapper>
