<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.BehaviorMapper">
    <select id="selectByCheckId" resultType="com.example.eps.ins.common.dto.report.resp.BehaviorResp">
        SELECT DISTINCT illegal.check_list_id,
        behavior as behavior,
        (SELECT SUM(number)
        FROM t_rpt_illegal_behavior as behavior
        WHERE behavior.check_list_id = list.id
        <if test="year != null and year != 1">
            AND year=#{year}
        </if>
        <if test="regionId != null and regionId != 1">
            AND region_id = #{regionId}
        </if>
        ) as number
        FROM t_rpt_illegal_behavior as illegal,
        t_check_list as list
        WHERE illegal.check_list_id = list.id
        <if test="year != null and year != 1">
            and illegal.year =#{year}
        </if>
        <if test="regionId != null and regionId != 1">
            AND region_id = #{regionId}
        </if>
        AND illegal.check_list_id = list.id
        ORDER BY number DESC
        LIMIT #{limit}
    </select>
    <select id="getCityAll" resultType="com.example.eps.ins.common.dto.report.resp.YearCheckListResp">
        select COALESCE(sum(tryc.complete), 0) as complete,
               COALESCE(sum(tryc.no_check), 0) as noCheck,
               COALESCE(sum(tryc.number), 0)   as number
        from t_rpt_year_check as tryc
        where 1 = 1
        <if test="regionId != null and regionId != 1">
            AND region_id = #{regionId}
        </if>
        <if test="year != null and year != 1">
            and year=#{year}
        </if>
    </select>
    <select id="getDataViewAll" resultType="com.example.eps.ins.common.dto.report.resp.EnforcementResp">
        select COALESCE(sum(trdv.complete),0) as complete,
        COALESCE(sum(trdv.fine),0) as fine,
        COALESCE(sum(trdv.check_list_number),0) as checkListNumber,
        COALESCE(sum(trdv.no_check_list),0) as noCheckList,
        COALESCE(sum(trdv.yes_check_list),0) as yesCheckList
        from t_rpt_data_view as trdv
        where 1=1
        <if test="regionId != null and regionId != 1">
            AND region_id = #{regionId}
        </if>
        <if test="year != null and year != 1">
            and year=#{year}
        </if>
    </select>
    <select id="getRectifyAll" resultType="com.example.eps.ins.common.dto.report.resp.EnforcementResp">
        select COALESCE(sum(trdv.no_rectify),0) as noRectify,
        COALESCE(sum(trdv.yes_rectify),0) as yesRectify,
        COALESCE(sum(trdv.rectify),0) as startRectify,
        COALESCE(sum(trdv.rectify_number),0) as rectifyNumber
        from t_rpt_rectify_data_view as trdv
        where 1 = 1
        <if test="regionId != null and regionId != 1">
            AND region_id = #{regionId}
        </if>
        <if test="year != null and year != 1">
            and year=#{year}
        </if>
    </select>
    <select id="selectLiveTwo" resultType="com.example.eps.ins.common.dto.report.resp.BehaviorResp">
        SELECT DISTINCT illegal.check_list_id,
        behavior as behavior,
        (SELECT SUM(number)
        FROM t_rpt_illegal_behavior as behavior
        WHERE  behavior.check_list_id = list.id
        <if test="year != null and year != 1">
            and illegal.year=#{year}
        </if>
        and region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        ) as number
        FROM t_rpt_illegal_behavior as illegal,
        t_check_list as list
        WHERE  illegal.check_list_id = list.id
        <if test="year != null and year != 1">
            and illegal.year=#{year}
        </if>
        and region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND illegal.check_list_id = list.id
        ORDER BY number DESC
        LIMIT #{limit}
    </select>
    <select id="getDataViewOrTwoLive" resultType="com.example.eps.ins.common.dto.report.resp.EnforcementResp">
        select COALESCE(sum(trdv.complete),0) as complete,
        COALESCE(sum(trdv.fine),0) as fine,
        COALESCE(sum(trdv.check_list_number),0) as checkListNumber,
        COALESCE(sum(trdv.no_check_list),0) as noCheckList,
        COALESCE(sum(trdv.yes_check_list),0) as yesCheckList
        from t_rpt_data_view as trdv
        where  region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="year != null and year != 1">
            and year=#{year}
        </if>
    </select>
    <select id="getRectifyOrTwoLive" resultType="com.example.eps.ins.common.dto.report.resp.EnforcementResp">
        select COALESCE(sum(trdv.no_rectify),0) as noRectify,
        COALESCE(sum(trdv.yes_rectify),0) as yesRectify,
        COALESCE(sum(trdv.rectify),0) as startRectify,
        COALESCE(sum(trdv.rectify_number),0) as rectifyNumber
        from t_rpt_rectify_data_view as trdv
        where region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="year != null and year != 1">
            and year=#{year}
        </if>
    </select>
    <select id="getCityAllOrTwoLive" resultType="com.example.eps.ins.common.dto.report.resp.YearCheckListResp">
        select COALESCE(sum(tryc.complete), 0) as complete,
               COALESCE(sum(tryc.no_check), 0) as noCheck,
               COALESCE(sum(tryc.number), 0)   as number
        from t_rpt_year_check as tryc
        where  region_id in
        <foreach collection="regionId" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        <if test="year != null and year != 1">
            and year=#{year}
        </if>
    </select>
</mapper>