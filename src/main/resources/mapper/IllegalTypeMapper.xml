<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.IllegalTypeMapper">
    <select id="findByParentId" parameterType="java.lang.Integer"  resultType="java.lang.Integer" >
        select t.id
        from t_illegal_type t
        where t.parent_id =#{id}
    </select>
</mapper>
