<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.eps.ins.mapper.DeviceMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.eps.ins.common.entity.device.Device">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="external_device_id" property="externalDeviceId" jdbcType="BIGINT"/>
        <result column="device_name" property="deviceName" jdbcType="VARCHAR"/>
        <result column="serial" property="serial" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="device_type" property="deviceType" jdbcType="VARCHAR"/>
        <result column="ip" property="ip" jdbcType="VARCHAR"/>
        <result column="sync_status" property="syncStatus" jdbcType="INTEGER"/>
        <result column="last_sync_time" property="lastSyncTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, external_device_id, device_name, serial, status, device_type, ip,
        sync_status, last_sync_time, create_time, update_time, remark, is_deleted
    </sql>

    <!-- 根据外部设备ID查询设备 -->
    <select id="selectByExternalDeviceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_device
        WHERE external_device_id = #{externalDeviceId}
        AND is_deleted = 0
    </select>

    <!-- 根据序列号查询设备 -->
    <select id="selectBySerial" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_device
        WHERE serial = #{serial}
        AND is_deleted = 0
    </select>

    <!-- 分页查询设备列表 -->
    <select id="selectDevicePage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_device
        WHERE is_deleted = 0
        <if test="device.deviceName != null and device.deviceName != ''">
            AND device_name LIKE CONCAT('%', #{device.deviceName}, '%')
        </if>
        <if test="device.serial != null and device.serial != ''">
            AND serial LIKE CONCAT('%', #{device.serial}, '%')
        </if>
        <if test="device.status != null and device.status != ''">
            AND status = #{device.status}
        </if>
        <if test="device.deviceType != null and device.deviceType != ''">
            AND device_type = #{device.deviceType}
        </if>
        <if test="device.syncStatus != null">
            AND sync_status = #{device.syncStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 批量插入设备信息 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_device (
        external_device_id, device_name, serial, status, device_type, ip,
        sync_status, last_sync_time, create_time, update_time, remark, is_deleted
        ) VALUES
        <foreach collection="deviceList" item="device" separator=",">
            (
            #{device.externalDeviceId},
            #{device.deviceName},
            #{device.serial},
            #{device.status},
            #{device.deviceType},
            #{device.ip},
            #{device.syncStatus},
            #{device.lastSyncTime},
            #{device.createTime},
            #{device.updateTime},
            #{device.remark},
            0
            )
        </foreach>
    </insert>

    <!-- 根据同步状态查询设备列表 -->
    <select id="selectBySyncStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_device
        WHERE sync_status = #{syncStatus}
        AND is_deleted = 0
        ORDER BY create_time DESC
    </select>

</mapper>
