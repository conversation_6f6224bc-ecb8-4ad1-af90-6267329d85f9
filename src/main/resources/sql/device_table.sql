-- 设备信息表
CREATE TABLE `t_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `external_device_id` bigint(20) DEFAULT NULL COMMENT '外部设备ID（来自第三方API）',
  `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `serial` varchar(50) DEFAULT NULL COMMENT '设备序列号',
  `status` varchar(20) DEFAULT NULL COMMENT '设备状态（ONLINE/OFFLINE等）',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型（GATEWAY等）',
  `ip` varchar(50) DEFAULT NULL COMMENT '设备IP地址',
  `sync_status` int(1) DEFAULT '0' COMMENT '同步状态（0-未同步，1-已同步）',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` int(1) DEFAULT '0' COMMENT '是否删除（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_external_device_id` (`external_device_id`),
  UNIQUE KEY `uk_serial` (`serial`),
  KEY `idx_device_name` (`device_name`),
  KEY `idx_status` (`status`),
  KEY `idx_device_type` (`device_type`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备信息表';
