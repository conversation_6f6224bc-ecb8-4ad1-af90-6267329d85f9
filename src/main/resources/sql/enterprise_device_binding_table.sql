-- 企业设备绑定关系表
CREATE TABLE `t_enterprise_device_binding` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `enterprise_id` bigint(20) NOT NULL COMMENT '企业ID',
  `external_device_id` bigint(20) NOT NULL COMMENT '外部设备ID（来自第三方API）',
  `device_id` bigint(20) DEFAULT NULL COMMENT '设备ID（关联t_device表的id）',
  `device_name` varchar(100) NOT NULL COMMENT '设备名称（用户输入的自定义名称）',
  `device_model` varchar(100) NOT NULL COMMENT '设备型号',
  `binding_status` int(1) DEFAULT '1' COMMENT '绑定状态（0-未绑定，1-已绑定，2-已解绑）',
  `binding_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '绑定时间',
  `unbinding_time` datetime DEFAULT NULL COMMENT '解绑时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` int(1) DEFAULT '0' COMMENT '是否删除（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_enterprise_external_device` (`enterprise_id`, `external_device_id`, `is_deleted`),
  KEY `idx_enterprise_id` (`enterprise_id`),
  KEY `idx_external_device_id` (`external_device_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_binding_status` (`binding_status`),
  KEY `idx_binding_time` (`binding_time`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_binding_enterprise` FOREIGN KEY (`enterprise_id`) REFERENCES `t_enterprise` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_binding_device` FOREIGN KEY (`device_id`) REFERENCES `t_device` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业设备绑定关系表';

-- 添加索引优化查询性能
CREATE INDEX `idx_enterprise_status` ON `t_enterprise_device_binding` (`enterprise_id`, `binding_status`);
CREATE INDEX `idx_device_status` ON `t_enterprise_device_binding` (`device_id`, `binding_status`);
CREATE INDEX `idx_external_device_status` ON `t_enterprise_device_binding` (`external_device_id`, `binding_status`);

-- 插入绑定状态字典数据（可选）
INSERT INTO `t_dictionaries` (`code`, `details`, `details_one`, `details_two`, `status`) VALUES
('device_binding_status_0', '未绑定', '设备绑定状态', '未绑定', 1),
('device_binding_status_1', '已绑定', '设备绑定状态', '已绑定', 1),
('device_binding_status_2', '已解绑', '设备绑定状态', '已解绑', 1);

-- 创建视图用于查询绑定信息（可选）
CREATE VIEW `v_enterprise_device_binding` AS
SELECT 
    edb.id,
    edb.enterprise_id,
    e.name as enterprise_name,
    edb.external_device_id,
    edb.device_id,
    edb.device_name,
    edb.device_model,
    edb.binding_status,
    CASE 
        WHEN edb.binding_status = 0 THEN '未绑定'
        WHEN edb.binding_status = 1 THEN '已绑定'
        WHEN edb.binding_status = 2 THEN '已解绑'
        ELSE '未知状态'
    END as binding_status_desc,
    edb.binding_time,
    edb.unbinding_time,
    edb.create_time,
    edb.update_time,
    edb.creator_id,
    edb.remark,
    d.serial as device_serial,
    d.status as device_status,
    d.ip as device_ip,
    d.device_type as device_type
FROM t_enterprise_device_binding edb
LEFT JOIN t_enterprise e ON edb.enterprise_id = e.id
LEFT JOIN t_device d ON edb.device_id = d.id
WHERE edb.is_deleted = 0
ORDER BY edb.create_time DESC;
