-- 子设备信息表
CREATE TABLE `t_sub_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_device_id` bigint(20) NOT NULL COMMENT '父设备ID（关联t_device表的id）',
  `external_sub_device_id` bigint(20) DEFAULT NULL COMMENT '外部子设备ID（来自第三方API）',
  `device_name` varchar(100) DEFAULT NULL COMMENT '子设备名称',
  `serial` varchar(50) DEFAULT NULL COMMENT '子设备序列号',
  `status` varchar(20) DEFAULT NULL COMMENT '子设备状态（ONLINE/OFFLINE等）',
  `device_type` varchar(50) DEFAULT NULL COMMENT '子设备类型（SUB_DEVICE等）',
  `ip` varchar(50) DEFAULT NULL COMMENT '子设备IP地址',
  `sync_status` int(1) DEFAULT '0' COMMENT '同步状态（0-未同步，1-已同步）',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` int(1) DEFAULT '0' COMMENT '是否删除（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_external_sub_device_id` (`external_sub_device_id`),
  UNIQUE KEY `uk_parent_serial` (`parent_device_id`, `serial`),
  KEY `idx_parent_device_id` (`parent_device_id`),
  KEY `idx_device_name` (`device_name`),
  KEY `idx_serial` (`serial`),
  KEY `idx_status` (`status`),
  KEY `idx_device_type` (`device_type`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_sub_device_parent` FOREIGN KEY (`parent_device_id`) REFERENCES `t_device` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='子设备信息表';

-- 添加索引优化查询性能
CREATE INDEX `idx_parent_status` ON `t_sub_device` (`parent_device_id`, `status`);
CREATE INDEX `idx_parent_type` ON `t_sub_device` (`parent_device_id`, `device_type`);
CREATE INDEX `idx_sync_time` ON `t_sub_device` (`sync_status`, `last_sync_time`);
