-- 安全监控事件表
CREATE TABLE `t_safety_monitor_event` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `external_event_id` varchar(100) NOT NULL COMMENT '外部事件ID（来自第三方系统）',
  `device_id` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `device_serial` varchar(100) DEFAULT NULL COMMENT '设备序列号',
  `device_ip` varchar(50) DEFAULT NULL COMMENT '设备IP地址',
  `device_type` varchar(50) DEFAULT NULL COMMENT '设备类型',
  `device_status` varchar(20) DEFAULT NULL COMMENT '设备状态',
  `camera_id` varchar(50) DEFAULT NULL COMMENT '摄像头ID',
  `camera_name` varchar(100) DEFAULT NULL COMMENT '摄像头名称',
  `event_type` int(11) DEFAULT NULL COMMENT '事件类型（121=安全帽检测, 122=反光衣检测, 123=人员入侵等）',
  `event_type_name` varchar(50) DEFAULT NULL COMMENT '事件类型名称',
  `event_source` int(11) DEFAULT NULL COMMENT '事件源',
  `confidence` double DEFAULT NULL COMMENT '置信度',
  `detection_score` double DEFAULT NULL COMMENT '检测分数',
  `have_alarm` tinyint(4) DEFAULT '0' COMMENT '是否有告警（0=无, 1=有）',
  `alarm_times` int(11) DEFAULT NULL COMMENT '告警次数',
  `object_name` varchar(100) DEFAULT NULL COMMENT '检测对象名称',
  `bounding_box` text COMMENT '边界框坐标（JSON格式存储）',
  `img_url` varchar(500) DEFAULT NULL COMMENT '图片URL',
  `big_url` text COMMENT '大图URL',
  `img_width` double DEFAULT NULL COMMENT '图片宽度',
  `img_height` double DEFAULT NULL COMMENT '图片高度',
  `start_time` datetime DEFAULT NULL COMMENT '事件开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '事件结束时间',
  `mark_time` datetime DEFAULT NULL COMMENT '事件标记时间',
  `raw_json_data` longtext COMMENT '原始JSON数据',
  `process_status` tinyint(4) DEFAULT '0' COMMENT '处理状态（0=未处理, 1=已处理, 2=处理失败）',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_result` text COMMENT '处理结果',
  `alarm_status` tinyint(4) DEFAULT '0' COMMENT '告警状态（0=未告警, 1=已告警）',
  `alarm_time` datetime DEFAULT NULL COMMENT '告警时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_external_event_id` (`external_event_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_device_serial` (`device_serial`),
  KEY `idx_event_type` (`event_type`),
  KEY `idx_have_alarm` (`have_alarm`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_alarm_status` (`alarm_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全监控事件表';

-- 添加索引优化查询性能
CREATE INDEX `idx_device_time` ON `t_safety_monitor_event` (`device_serial`, `create_time`);
CREATE INDEX `idx_event_type_time` ON `t_safety_monitor_event` (`event_type`, `create_time`);
CREATE INDEX `idx_alarm_time` ON `t_safety_monitor_event` (`have_alarm`, `create_time`);

-- 插入事件类型字典数据（可选）
INSERT INTO `t_dictionaries` (`code`, `details`, `details_one`, `details_two`, `status`) VALUES
('anmjc', '安全帽检测', '安全帽检测', '安全帽检测', 1),
('fgyjc', '反光衣检测', '安全帽检测', '安全帽检测', 1),
('ryrqjc', '人员入侵检测', '安全帽检测', '安全帽检测', 1);

-- 创建视图用于统计分析（可选）
CREATE VIEW `v_safety_event_statistics` AS
SELECT 
    DATE(create_time) as event_date,
    event_type,
    event_type_name,
    COUNT(*) as total_events,
    SUM(CASE WHEN have_alarm = 1 THEN 1 ELSE 0 END) as alarm_events,
    COUNT(DISTINCT device_serial) as device_count,
    AVG(detection_score) as avg_detection_score
FROM t_safety_monitor_event 
WHERE is_deleted = 0
GROUP BY DATE(create_time), event_type, event_type_name
ORDER BY event_date DESC, total_events DESC;
