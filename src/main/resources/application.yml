# 服务器配置
server:
  port: 7456
  connection-timeout: 120000ms  # 增加连接超时时间到2分钟
  servlet:
    context-path: /eps
    session:
      timeout: 30m
  # Tomcat 配置
  tomcat:
    uri-encoding: UTF-8
    # 连接超时时间（毫秒）
    connection-timeout: 120000  # 增加到2分钟
    # 最大连接数
    max-connections: 8192
    # 最大线程数
    max-threads: 200
    # 最小空闲线程数
    min-spare-threads: 30
    # 接受队列长度
    accept-count: 100
    # HTTP请求相关配置
    max-http-post-size: 104857600  # 100MB
    max-http-form-post-size: 104857600  # 100MB
    max-swallow-size: 104857600  # 100MB
    # 连接保持配置
    keep-alive-timeout: 60000  # 保持连接60秒
    max-keep-alive-requests: 100  # 最大保持连接请求数
    # 上传超时配置
    disable-upload-timeout: false  # 启用上传超时
    connection-upload-timeout: 120000  # 上传超时2分钟

spring:
  application:
    name: '工贸系统'
  # 环境 dev|test|prod
  profiles:
    active: prod
#    active: test
  # jackson时间格式化
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  # HTTP 连接超时配置
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  redis:
    open: false  # 是否开启redis缓存  true开启   false关闭
    database: 0
    host: **************
    port: 6379
    password: redis@cache   # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  mvc:
    throw-exception-if-no-handler-found: true
#  resources:
#    add-mappings: false

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.example.eps.ins.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: AUTO
      logic-delete-value: -1
      logic-not-delete-value: 0
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
fdfs:
  so-timeout: 30000          # 增加socket超时时间到30秒
  connect-timeout: 10000     # 增加连接超时时间到10秒
  # 连接池配置
  pool:
    # 连接池配置
    jmx-enabled: false       # 禁用JMX监控减少开销
    # 连接池大小配置
    max-total: 50            # 最大连接数
    max-idle: 20             # 最大空闲连接数
    min-idle: 5              # 最小空闲连接数
    # 连接验证配置
    test-on-borrow: false    # 禁用借用时验证，减少网络开销
    test-on-return: false    # 禁用归还时验证
    test-while-idle: true    # 启用空闲时验证
    time-between-eviction-runs-millis: 300000  # 5分钟检查一次空闲连接
    min-evictable-idle-time-millis: 600000     # 10分钟后回收空闲连接
    num-tests-per-eviction-run: 3              # 每次检查3个连接
    # 连接等待配置
    max-wait-millis: 5000    # 最大等待时间5秒
    block-when-exhausted: true  # 连接池耗尽时阻塞等待
  thumb-image:
    width: 150
    height: 150
  tracker-list:
    - 47.110.181.204:22122
  webServerUrl: http://47.110.181.204:9000
  maxFileSize: 100
  maxFileSizeUnit: M
oss:
  accessKeyId: LTAI5tRpbrYbz2zkYxxAX2pt
  accessKeySecret: ******************************
  roleArn: acs:ram::1709232887969008:role/aliyunosstokengeneratorrole
  bucket: eps-file
  region: oss-cn-hangzhou
  endpoint: https://oss-cn-hangzhou.aliyuncs.com

# EPS 系统配置
eps:
  security:
    type: mvc  # 使用传统的 MVC 安全配置，可选值：mvc, webflux
  roleArn: acs:ram::1709232887969008:role/aliyunosstokengeneratorrole
  bucket: eps-file
  region: oss-cn-hangzhou
  endpoint: https://oss-cn-hangzhou.aliyuncs.com
business:
  fileRootPath: /root/upload