# RiskPushController 企业相机列表接口文档

## 基础信息
- **控制器名称**: RiskPushController
- **基础路径**: `/risk/push`
- **功能描述**: 根据企业ID获取相机列表，支持查询所有相机和在线相机

---

## 接口列表

### 1. 根据企业ID获取相机列表
**接口地址**: `GET /risk/push/cameras/{enterpriseId}`

**功能描述**: 根据企业ID查询该企业绑定的所有相机设备，包含相机详细信息和统计数据

**路径参数**:
- `enterpriseId`: 企业ID (Long类型, 必填)

**请求示例**:
```bash
GET /risk/push/cameras/123
```

**响应示例**:
```json
{
  "success": true,
  "message": "企业相机列表查询成功",
  "enterpriseId": 123,
  "totalCount": 6,
  "statistics": {
    "totalCount": 6,
    "statusCount": {
      "ONLINE": 4,
      "OFFLINE": 2
    },
    "syncStatusCount": {
      "0": 1,
      "1": 5
    }
  },
  "data": [
    {
      "id": 1,
      "externalSubDeviceId": 1945381594326446081,
      "parentDeviceId": 456,
      "enterpriseId": 123,
      "enterpriseName": "测试企业",
      "deviceName": "工贸粉尘测试盒子",
      "deviceSerial": "5CF838712B91",
      "deviceIp": "************",
      "deviceType": "GATEWAY",
      "deviceStatus": "ONLINE",
      "cameraId": "0001",
      "cameraName": "相机1",
      "cameraStatus": "ONLINE",
      "cameraType": "SUB_DEVICE",
      "cameraIp": "*************",
      "syncStatus": 1,
      "syncStatusDesc": "已同步",
      "lastSyncTime": "2025-07-16 10:30:00",
      "createTime": "2025-07-16 09:00:00",
      "updateTime": "2025-07-16 10:30:00",
      "remark": null,
      "bindingStatus": 1,
      "bindingStatusDesc": "已绑定",
      "bindingTime": "2025-07-16 08:00:00"
    },
    {
      "id": 2,
      "externalSubDeviceId": 1945382067360051202,
      "parentDeviceId": 456,
      "enterpriseId": 123,
      "enterpriseName": "测试企业",
      "deviceName": "工贸粉尘测试盒子",
      "deviceSerial": "5CF838712B91",
      "deviceIp": "************",
      "deviceType": "GATEWAY",
      "deviceStatus": "ONLINE",
      "cameraId": "0002",
      "cameraName": "相机2",
      "cameraStatus": "ONLINE",
      "cameraType": "SUB_DEVICE",
      "cameraIp": "*************",
      "syncStatus": 1,
      "syncStatusDesc": "已同步",
      "lastSyncTime": "2025-07-16 10:30:00",
      "createTime": "2025-07-16 09:00:00",
      "updateTime": "2025-07-16 10:30:00",
      "remark": null,
      "bindingStatus": 1,
      "bindingStatusDesc": "已绑定",
      "bindingTime": "2025-07-16 08:00:00"
    }
  ]
}
```

---

### 2. 根据企业ID获取在线相机列表
**接口地址**: `GET /risk/push/cameras/{enterpriseId}/online`

**功能描述**: 根据企业ID查询该企业绑定的在线相机设备，只返回状态为ONLINE的相机

**路径参数**:
- `enterpriseId`: 企业ID (Long类型, 必填)

**请求示例**:
```bash
GET /risk/push/cameras/123/online
```

**响应示例**:
```json
{
  "success": true,
  "message": "企业在线相机列表查询成功",
  "enterpriseId": 123,
  "totalCount": 6,
  "onlineCount": 4,
  "offlineCount": 2,
  "data": [
    {
      "id": 1,
      "externalSubDeviceId": 1945381594326446081,
      "parentDeviceId": 456,
      "enterpriseId": 123,
      "enterpriseName": "测试企业",
      "deviceName": "工贸粉尘测试盒子",
      "deviceSerial": "5CF838712B91",
      "deviceIp": "************",
      "deviceType": "GATEWAY",
      "deviceStatus": "ONLINE",
      "cameraId": "0001",
      "cameraName": "相机1",
      "cameraStatus": "ONLINE",
      "cameraType": "SUB_DEVICE",
      "cameraIp": "*************",
      "syncStatus": 1,
      "syncStatusDesc": "已同步",
      "lastSyncTime": "2025-07-16 10:30:00",
      "createTime": "2025-07-16 09:00:00",
      "updateTime": "2025-07-16 10:30:00",
      "remark": null,
      "bindingStatus": 1,
      "bindingStatusDesc": "已绑定",
      "bindingTime": "2025-07-16 08:00:00"
    }
  ]
}
```

---

## 响应字段说明

### 基础响应字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| message | String | 响应消息 |
| enterpriseId | Long | 企业ID |
| totalCount | Integer | 相机总数 |
| onlineCount | Integer | 在线相机数（仅在线接口返回） |
| offlineCount | Integer | 离线相机数（仅在线接口返回） |
| statistics | Object | 统计信息（仅全量接口返回） |
| data | Array | 相机列表数据 |

### 统计信息字段 (statistics)
| 字段名 | 类型 | 描述 |
|--------|------|------|
| totalCount | Integer | 相机总数 |
| statusCount | Object | 按状态统计 |
| syncStatusCount | Object | 按同步状态统计 |

### 相机数据字段 (data中的每个对象)
| 字段名 | 类型 | 描述 | 示例 |
|--------|------|------|------|
| id | Long | 子设备ID | 1 |
| externalSubDeviceId | Long | 外部子设备ID | 1945381594326446081 |
| parentDeviceId | Long | 父设备ID | 456 |
| enterpriseId | Long | 企业ID | 123 |
| enterpriseName | String | 企业名称 | "测试企业" |
| deviceName | String | 设备名称（父设备） | "工贸粉尘测试盒子" |
| deviceSerial | String | 设备序列号（父设备） | "5CF838712B91" |
| deviceIp | String | 设备IP地址（父设备） | "************" |
| deviceType | String | 设备类型（父设备） | "GATEWAY" |
| deviceStatus | String | 设备状态（父设备） | "ONLINE" |
| cameraId | String | 相机ID（子设备序列号） | "0001" |
| cameraName | String | 相机名称 | "相机1" |
| cameraStatus | String | 相机状态 | "ONLINE" |
| cameraType | String | 相机类型 | "SUB_DEVICE" |
| cameraIp | String | 相机IP地址 | "*************" |
| syncStatus | Integer | 同步状态 | 1 |
| syncStatusDesc | String | 同步状态描述 | "已同步" |
| lastSyncTime | String | 最后同步时间 | "2025-07-16 10:30:00" |
| createTime | String | 创建时间 | "2025-07-16 09:00:00" |
| updateTime | String | 更新时间 | "2025-07-16 10:30:00" |
| remark | String | 备注 | null |
| bindingStatus | Integer | 绑定状态 | 1 |
| bindingStatusDesc | String | 绑定状态描述 | "已绑定" |
| bindingTime | String | 绑定时间 | "2025-07-16 08:00:00" |

---

## 状态值说明

### 相机状态 (cameraStatus)
- `ONLINE`: 在线
- `OFFLINE`: 离线
- `UNKNOWN`: 未知状态

### 同步状态 (syncStatus)
- `0`: 未同步
- `1`: 已同步

### 绑定状态 (bindingStatus)
- `0`: 未绑定
- `1`: 已绑定
- `2`: 已解绑

---

## 使用示例

### 1. 查询企业所有相机
```bash
curl -X GET "http://localhost:8080/risk/push/cameras/123"
```

### 2. 查询企业在线相机
```bash
curl -X GET "http://localhost:8080/risk/push/cameras/123/online"
```

### 3. JavaScript调用示例
```javascript
// 查询所有相机
fetch('/risk/push/cameras/123')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('相机总数:', data.totalCount);
      console.log('相机列表:', data.data);
      console.log('状态统计:', data.statistics);
    }
  });

// 查询在线相机
fetch('/risk/push/cameras/123/online')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('在线相机数:', data.onlineCount);
      console.log('离线相机数:', data.offlineCount);
      console.log('在线相机列表:', data.data);
    }
  });
```

---

## 错误响应格式
```json
{
  "success": false,
  "message": "错误描述信息"
}
```

## 常见错误信息
- "企业ID不能为空且必须大于0"
- "查询企业相机列表失败: 数据库连接异常"
- "查询企业在线相机列表失败: 企业不存在"

---

## 业务逻辑说明

### 1. 数据来源
- 通过企业设备绑定关系表关联企业和设备
- 通过子设备表获取相机信息
- 只返回绑定状态为"已绑定"的设备下的相机

### 2. 查询逻辑
- 根据企业ID查找已绑定的设备
- 查找这些设备下的所有子设备（相机）
- 返回相机的详细信息和统计数据

### 3. 在线相机过滤
- 基于相机状态字段进行过滤
- 只返回状态为"ONLINE"的相机
- 同时提供总数和在线数的统计

---

## 注意事项

1. **企业权限**: 实际使用时应根据用户权限验证企业访问权限
2. **数据实时性**: 相机状态依赖于设备同步，可能存在延迟
3. **性能考虑**: 大企业可能有大量相机，建议添加分页功能
4. **错误处理**: 接口包含完整的错误处理和日志记录
